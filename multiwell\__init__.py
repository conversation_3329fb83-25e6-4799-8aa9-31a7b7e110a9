"""
MWLT MultiWell - Organized Multi-Well Log Transformer Package

This package provides a clean, organized structure for the Multi-Well Log Transformer
with separate modules for training, prediction, validation, and utilities.

Modules:
    training: Enhanced model training with cross-validation
    prediction: Improved prediction capabilities with validation  
    validation: Comprehensive model validation and testing
    utils: Utility functions and basic tests
    demos: Interactive demonstrations and examples

Key Features:
    - Fixed normalization infrastructure (Priority 1)
    - Enhanced training pipeline with cross-validation (Priority 2)
    - Comprehensive validation suite with uncertainty quantification
    - Improved prediction accuracy and reliability
    - Clean modular structure for maintainability

Usage:
    from multiwell.launcher import multiwell_launcher
    # Or run scripts directly from subdirectories
"""

__version__ = "2.0.0"
__author__ = "AI Assistant"
__description__ = "Organized Multi-Well Log Transformer with Enhanced Training and Validation"

# Quick access to main functionality
from pathlib import Path

def get_training_script():
    """Get path to enhanced training script"""
    return Path(__file__).parent / "training" / "enhanced_multi_curve_training.py"

def get_validation_script():
    """Get path to validation suite"""
    return Path(__file__).parent / "validation" / "model_validation_suite.py"

def get_prediction_script():
    """Get path to improved prediction demo"""
    return Path(__file__).parent / "prediction" / "improved_multi_curve_prediction_demo.py"

def get_demo_script():
    """Get path to demo suite"""
    return Path(__file__).parent / "demos" / "demo_curve_prediction.py"

def show_structure():
    """Display the organized structure"""
    structure = """
    multiwell/
    ├── training/           # Enhanced model training
    ├── prediction/         # Improved predictions  
    ├── validation/         # Comprehensive validation
    ├── utils/              # Testing and utilities
    ├── demos/              # Interactive demonstrations
    └── multiwell_launcher.py  # Quick access launcher
    """
    print(structure)

# Expose key paths
__all__ = [
    'get_training_script',
    'get_validation_script', 
    'get_prediction_script',
    'get_demo_script',
    'show_structure'
]
