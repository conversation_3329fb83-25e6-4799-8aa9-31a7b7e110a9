{"learning_rate": 0.0001, "weight_decay": 1e-05, "batch_size": 16, "max_epochs": 200, "patience": 50, "optimizer": "adam", "scheduler": "plateau", "scheduler_params": {"patience": 20, "factor": 0.6, "min_lr": 1e-07}, "loss_config": {"type": "curve_specific", "custom_params": {"constraint_weight": 0.5, "physics_constraints": true}}, "save_best_only": true, "save_frequency": 10, "validation_frequency": 1, "gradient_clipping": 1.0, "mixed_precision": false, "save_dir": "enhanced_training_outputs\\CNL_fold_1"}