"""
Multi-Curve Well Log Prediction Demo
Demonstrates three prediction scenarios:
1. Vp prediction (current VpTransformer) - WORKING
2. Density prediction (conceptual GeneralTransformer) - DEMO
3. RLLD prediction (conceptual GeneralTransformer) - DEMO

This script shows how the current VpTransformer works and how it could be extended
for other well log curve predictions using the GeneralTransformer approach.
"""
import os
import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt
import tkinter as tk
from tkinter import filedialog, messagebox

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import VpDataNormalizer, MWLT_Vp_Base
    from vp_predictor.utils import get_device
    import torch
    import torch.nn as nn
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    sys.exit(1)

class MultiCurvePredictor:
    """Demonstration class showing how GeneralTransformer would work"""
    
    def __init__(self, device):
        self.device = device
        self.normalizer = VpDataNormalizer()
    
    def load_and_prepare_data(self, file_path, input_curves, target_curve, sequence_length=640):
        """Load HDF5 data and prepare for prediction"""
        print(f"📂 Loading {os.path.basename(file_path)} for {target_curve} prediction")
        
        curves = {}
        with h5py.File(file_path, 'r') as f:
            for curve_name in f.keys():
                data = f[curve_name][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data
        
        # Prepare input features
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves:
                data = self._resample_curve(curves[curve_name], sequence_length)
                input_features.append(data)
                print(f"   ✅ {curve_name}: range [{data.min():.2f}, {data.max():.2f}]")
            else:
                print(f"   ⚠️  Missing {curve_name}")
                input_features.append(np.zeros(sequence_length))
        
        # Prepare target
        if target_curve in curves:
            target_data = self._resample_curve(curves[target_curve], sequence_length)
            print(f"   🎯 {target_curve}: range [{target_data.min():.2f}, {target_data.max():.2f}]")
        else:
            target_data = np.zeros(sequence_length)
        
        return np.array(input_features), target_data, curves
    
    def _resample_curve(self, data, target_length):
        """Resample curve to target length"""
        if len(data) == target_length:
            return data
        original_indices = np.linspace(0, len(data) - 1, len(data))
        target_indices = np.linspace(0, len(data) - 1, target_length)
        return np.interp(target_indices, original_indices, data)
    
    def predict_vp_current(self, input_data):
        """Current Vp prediction using VpTransformer"""
        print("🔮 Making Vp prediction with current VpTransformer...")
        
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(self.device)
        model.eval()
        
        with torch.no_grad():
            input_tensor = torch.FloatTensor(input_data).unsqueeze(0).to(self.device)
            prediction = model(input_tensor)
            prediction_np = prediction.squeeze().cpu().numpy()
        
        print(f"   ✅ Prediction range: [{prediction_np.min():.2f}, {prediction_np.max():.2f}]")
        return prediction_np
    
    def predict_density_conceptual(self, input_data, target_data):
        """Conceptual density prediction (simulated)"""
        print("💡 Simulating density prediction with GeneralTransformer...")
        print("   Configuration: GR, CNL, AC, RLLD → DEN")
        print("   Normalization: mean=2.5, std=0.3, range=[1.5, 3.0]")
        print("   Activation: sigmoid (bounded output)")
        
        # Simulate a reasonable density prediction based on input trends
        # This is just for demonstration - real GeneralTransformer would be trained
        gr_norm = (input_data[0] - input_data[0].mean()) / input_data[0].std()
        cnl_norm = (input_data[1] - input_data[1].mean()) / input_data[1].std()
        
        # Simple heuristic: lower GR and CNL typically mean higher density
        density_trend = 2.5 - 0.1 * gr_norm - 0.15 * cnl_norm
        density_prediction = np.clip(density_trend + 0.05 * np.random.randn(len(density_trend)), 1.5, 3.0)
        
        print(f"   ✅ Simulated prediction range: [{density_prediction.min():.2f}, {density_prediction.max():.2f}] g/cm³")
        return density_prediction
    
    def predict_rlld_conceptual(self, input_data, target_data):
        """Conceptual RLLD prediction (simulated)"""
        print("💡 Simulating RLLD prediction with GeneralTransformer...")
        print("   Configuration: GR, CNL, DEN, AC → RLLD")
        print("   Preprocessing: log10 transform")
        print("   Normalization: log-scale, mean=1.0, std=2.0")
        
        # Simulate resistivity prediction based on geological relationships
        # Lower porosity (higher density, lower neutron) typically means higher resistivity
        den_norm = (input_data[2] - input_data[2].mean()) / input_data[2].std()
        cnl_norm = (input_data[1] - input_data[1].mean()) / input_data[1].std()
        
        # Log-scale resistivity trend
        log_rlld_trend = 0.5 + 0.8 * den_norm - 0.6 * cnl_norm
        log_rlld_prediction = log_rlld_trend + 0.3 * np.random.randn(len(log_rlld_trend))
        rlld_prediction = 10 ** log_rlld_prediction
        rlld_prediction = np.clip(rlld_prediction, 0.1, 1000)
        
        print(f"   ✅ Simulated prediction range: [{rlld_prediction.min():.2f}, {rlld_prediction.max():.2f}] ohm-m")
        return rlld_prediction

def create_comparison_plot(well_name, scenarios_data, output_dir=None):
    """Create comprehensive comparison plot for all three scenarios"""
    fig, axes = plt.subplots(3, 4, figsize=(20, 15))
    fig.suptitle(f'Multi-Curve Prediction Demo - {well_name}', fontsize=16)
    
    depth = np.arange(640)  # Simplified depth array
    
    scenario_names = ['Vp Prediction (Current)', 'Density Prediction (Conceptual)', 'RLLD Prediction (Conceptual)']
    
    for i, (scenario_name, data) in enumerate(zip(scenario_names, scenarios_data)):
        input_data, target_data, prediction, input_curves, target_curve = data
        
        # Input curves plot
        for j, curve_name in enumerate(input_curves):
            if j < 3:  # Show first 3 input curves
                axes[i, j].plot(input_data[j], depth, 'b-', linewidth=1)
                axes[i, j].set_title(f'{curve_name}')
                axes[i, j].set_ylabel('Depth Index')
                axes[i, j].grid(True, alpha=0.3)
                axes[i, j].invert_yaxis()
        
        # Prediction vs actual
        axes[i, 3].plot(target_data, depth, 'g-', linewidth=2, label=f'Actual {target_curve}')
        axes[i, 3].plot(prediction, depth, 'r--', linewidth=2, label=f'Predicted {target_curve}')
        axes[i, 3].set_title(f'{scenario_name}')
        axes[i, 3].set_ylabel('Depth Index')
        axes[i, 3].legend()
        axes[i, 3].grid(True, alpha=0.3)
        axes[i, 3].invert_yaxis()
        
        # Calculate and display statistics
        error = prediction - target_data
        rmse = np.sqrt(np.mean(error**2))
        mae = np.mean(np.abs(error))
        r2 = 1 - np.sum(error**2) / np.sum((target_data - target_data.mean())**2)
        
        axes[i, 3].text(0.02, 0.98, f'RMSE: {rmse:.2f}\nMAE: {mae:.2f}\nR²: {r2:.3f}', 
                       transform=axes[i, 3].transAxes, verticalalignment='top',
                       bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
    
    plt.tight_layout()
    
    # Save to specified output directory or current directory
    if output_dir:
        output_path = os.path.join(output_dir, f'multi_curve_demo_{well_name.lower()}.png')
    else:
        output_path = f'multi_curve_demo_{well_name.lower()}.png'
    
    plt.savefig(output_path, dpi=300, bbox_inches='tight')
    print(f"📊 Comprehensive plot saved as: {output_path}")
    
    return fig

def select_input_files():
    """Open dialog to select multiple HDF5 input files"""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    file_paths = filedialog.askopenfilenames(
        title="Select HDF5 Input Files",
        filetypes=[("HDF5 files", "*.hdf5"), ("All files", "*.*")],
        initialdir=os.getcwd()
    )
    
    root.destroy()
    return file_paths


def select_output_directory():
    """Open dialog to select output directory for saving plots"""
    root = tk.Tk()
    root.withdraw()  # Hide the main window
    
    directory = filedialog.askdirectory(
        title="Select Output Directory for Plots",
        initialdir=os.getcwd()
    )
    
    root.destroy()
    return directory


def main():
    """Main function demonstrating all three prediction scenarios"""
    print("🚀 MULTI-CURVE WELL LOG PREDICTION DEMO")
    print("="*80)
    print("Demonstrating current Vp prediction and conceptual multi-curve capabilities")
    
    # Select input files using dialog
    print("📁 Please select HDF5 input files...")
    input_files = select_input_files()
    
    if not input_files:
        print("❌ No input files selected. Exiting...")
        return
    
    # Select output directory using dialog
    print("📂 Please select output directory for plots...")
    output_dir = select_output_directory()
    
    if not output_dir:
        print("⚠️ No output directory selected. Plots will be saved in current directory.")
        output_dir = None
    
    print(f"\n📋 Selected {len(input_files)} input file(s):")
    for file_path in input_files:
        print(f"   • {os.path.basename(file_path)}")
    
    if output_dir:
        print(f"📂 Output directory: {output_dir}")
    else:
        print(f"📂 Output directory: {os.getcwd()} (current directory)")
    
    device = get_device(device_id=0)
    predictor = MultiCurvePredictor(device)
    
    # Process selected files
    test_files = [(file_path, os.path.splitext(os.path.basename(file_path))[0]) for file_path in input_files]
    
    for file_path, well_name in test_files:
        print(f"\n" + "="*80)
        print(f"PROCESSING WELL {well_name}")
        print("="*80)
        
        try:
            scenarios_data = []
            
            # Scenario 1: Vp Prediction (Current VpTransformer)
            print(f"\n📊 SCENARIO 1: Vp PREDICTION (CURRENT)")
            print("-" * 50)
            input_data, target_data, curves = predictor.load_and_prepare_data(
                file_path, ['GR', 'CNL', 'DEN', 'RLLD'], 'AC'
            )
            vp_prediction = predictor.predict_vp_current(input_data)
            scenarios_data.append((input_data, target_data, vp_prediction, ['GR', 'CNL', 'DEN', 'RLLD'], 'AC'))
            
            # Scenario 2: Density Prediction (Conceptual)
            print(f"\n📊 SCENARIO 2: DENSITY PREDICTION (CONCEPTUAL)")
            print("-" * 50)
            input_data, target_data, curves = predictor.load_and_prepare_data(
                file_path, ['GR', 'CNL', 'AC', 'RLLD'], 'DEN'
            )
            density_prediction = predictor.predict_density_conceptual(input_data, target_data)
            scenarios_data.append((input_data, target_data, density_prediction, ['GR', 'CNL', 'AC', 'RLLD'], 'DEN'))
            
            # Scenario 3: RLLD Prediction (Conceptual)
            print(f"\n📊 SCENARIO 3: RLLD PREDICTION (CONCEPTUAL)")
            print("-" * 50)
            input_data, target_data, curves = predictor.load_and_prepare_data(
                file_path, ['GR', 'CNL', 'DEN', 'AC'], 'RLLD'
            )
            rlld_prediction = predictor.predict_rlld_conceptual(input_data, target_data)
            scenarios_data.append((input_data, target_data, rlld_prediction, ['GR', 'CNL', 'DEN', 'AC'], 'RLLD'))
            
            # Create comprehensive visualization
            print(f"\n📈 Creating comprehensive visualization...")
            create_comparison_plot(well_name, scenarios_data, output_dir)
            
            print(f"\n✅ {well_name} processing completed successfully!")
            
        except Exception as e:
            print(f"❌ Error processing {well_name}: {e}")
    
    # Summary and next steps
    print(f"\n" + "="*80)
    print("🎉 MULTI-CURVE PREDICTION DEMO COMPLETED")
    print("="*80)
    
    print(f"\n📋 SUMMARY:")
    print(f"✅ Scenario 1 (Vp): Uses current VpTransformer (untrained weights)")
    print(f"💡 Scenario 2 (Density): Conceptual GeneralTransformer simulation")
    print(f"💡 Scenario 3 (RLLD): Conceptual GeneralTransformer simulation")
    
    print(f"\n🔧 TO IMPLEMENT REAL MULTI-CURVE PREDICTION:")
    print(f"1. Follow the refactoring plan in refactor_general.md")
    print(f"2. Implement GeneralTransformer with configurable inputs/outputs")
    print(f"3. Train models for each curve type using appropriate data")
    print(f"4. Implement proper normalization for each curve type")
    print(f"5. Add geological constraint validation")
    
    print(f"\n📚 REFERENCES:")
    print(f"- Current implementation: vp_predictor/vp_model_improved.py")
    print(f"- Refactoring plan: refactor_general.md")
    print(f"- Training data creation: create_improved_vp_data()")

if __name__ == "__main__":
    main()
