"""
Advanced Dataset Optimization Fixes for demo_curve_prediction.py

This module provides intelligent dataset sizing, data augmentation,
and memory-efficient data loading patterns to address the small
dataset and indexing issues.
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Union
import logging
from dataclasses import dataclass
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


@dataclass
class DatasetMetrics:
    """Container for dataset quality metrics"""
    total_samples: int
    sequence_length: int
    stride: int
    coverage_ratio: float  # How much of original data is covered
    overlap_ratio: float   # How much overlap between sequences
    data_efficiency: float # Balance between samples and coverage
    recommended_changes: List[str]


class IntelligentSequenceOptimizer:
    """Advanced sequence configuration optimizer with multiple strategies"""
    
    def __init__(self, min_samples: int = 20, max_sequence_length: int = 640):
        self.min_samples = min_samples
        self.max_sequence_length = max_sequence_length
        
    def analyze_data_characteristics(self, data_dict: Dict[str, np.ndarray]) -> Dict[str, any]:
        """Analyze data characteristics to inform optimization"""
        # Get data length
        data_length = len(next(iter(data_dict.values())))
        
        # Analyze variability (higher variability = can use shorter sequences)
        variabilities = {}
        for curve, values in data_dict.items():
            # Calculate local variability using rolling standard deviation
            window_size = min(50, len(values) // 20)
            rolling_std = np.array([
                np.std(values[max(0, i-window_size//2):i+window_size//2])
                for i in range(len(values))
            ])
            variabilities[curve] = np.mean(rolling_std)
        
        # Detect trends (trending data needs longer sequences)
        trend_strengths = {}
        for curve, values in data_dict.items():
            # Simple trend detection using correlation with position
            positions = np.arange(len(values))
            correlation = np.corrcoef(positions, values)[0, 1]
            trend_strengths[curve] = abs(correlation)
        
        return {
            'data_length': data_length,
            'mean_variability': np.mean(list(variabilities.values())),
            'max_trend_strength': max(trend_strengths.values()),
            'variabilities': variabilities,
            'trend_strengths': trend_strengths
        }
    
    def optimize_configuration(self, data_dict: Dict[str, np.ndarray], 
                             target_samples: Optional[int] = None) -> Dict[str, int]:
        """
        Optimize sequence configuration using multiple strategies
        
        Args:
            data_dict: Dictionary of curve data
            target_samples: Target number of samples (if None, use min_samples)
            
        Returns:
            Optimized configuration dict
        """
        characteristics = self.analyze_data_characteristics(data_dict)
        data_length = characteristics['data_length']
        
        if target_samples is None:
            target_samples = max(self.min_samples, data_length // 100)
        
        # Strategy 1: Variable sequence length based on data characteristics
        base_seq_length = min(self.max_sequence_length, data_length // 4)
        
        # Adjust based on variability (high variability = shorter sequences OK)
        variability_factor = min(2.0, characteristics['mean_variability'] / 0.1)
        adjusted_seq_length = int(base_seq_length / variability_factor)
        
        # Adjust based on trends (strong trends = longer sequences needed)
        trend_factor = 1.0 + characteristics['max_trend_strength']
        final_seq_length = int(adjusted_seq_length * trend_factor)
        
        # Ensure reasonable bounds
        final_seq_length = max(64, min(self.max_sequence_length, final_seq_length))
        
        # Calculate stride to achieve target samples
        max_stride = (data_length - final_seq_length) // (target_samples - 1)
        stride = max(1, min(final_seq_length // 4, max_stride))
        
        # Calculate actual metrics
        actual_samples = (data_length - final_seq_length) // stride + 1
        
        config = {
            'length': final_seq_length,
            'stride': stride,
            'expected_samples': actual_samples,
            'data_characteristics': characteristics
        }
        
        logger.info(f"Intelligent sequence optimization: {config['length']}/{config['stride']} "
                   f"→ {config['expected_samples']} samples")
        
        return config


class DataAugmentationEngine:
    """Advanced data augmentation for well log data with geological constraints"""
    
    def __init__(self, augmentation_factor: float = 2.0):
        self.augmentation_factor = augmentation_factor
        
    def augment_well_data(self, data_dict: Dict[str, np.ndarray], 
                         target_length: Optional[int] = None) -> Dict[str, np.ndarray]:
        """
        Apply geological-aware data augmentation
        
        Args:
            data_dict: Original well log data
            target_length: Target length after augmentation
            
        Returns:
            Augmented data dictionary
        """
        if target_length is None:
            target_length = int(len(next(iter(data_dict.values()))) * self.augmentation_factor)
        
        augmented_data = {}
        
        for curve, values in data_dict.items():
            augmented_values = self._augment_curve(values, curve, target_length)
            augmented_data[curve] = augmented_values
            
        logger.info(f"Data augmentation: {len(next(iter(data_dict.values())))} → {target_length} points")
        return augmented_data
    
    def _augment_curve(self, values: np.ndarray, curve_type: str, target_length: int) -> np.ndarray:
        """Apply curve-specific augmentation strategies"""
        original_length = len(values)
        
        if target_length <= original_length:
            return values
        
        augmented = np.zeros(target_length)
        augmented[:original_length] = values
        
        # Strategy 1: Geological trend extrapolation
        trend_portion = self._extrapolate_geological_trend(values, target_length - original_length)
        
        # Strategy 2: Add realistic noise based on curve type
        noise_portion = self._add_realistic_noise(values, curve_type, target_length - original_length)
        
        # Strategy 3: Smooth blending of original patterns
        pattern_portion = self._extend_patterns(values, target_length - original_length)
        
        # Combine strategies with weights
        extension = 0.5 * trend_portion + 0.3 * pattern_portion + 0.2 * noise_portion
        
        # Apply curve-specific constraints
        extension = self._apply_curve_constraints(extension, curve_type, values)
        
        augmented[original_length:] = extension
        
        return augmented
    
    def _extrapolate_geological_trend(self, values: np.ndarray, extension_length: int) -> np.ndarray:
        """Extrapolate geological trends"""
        # Fit polynomial trend to last portion of data
        trend_window = min(200, len(values) // 2)
        x_trend = np.arange(trend_window)
        y_trend = values[-trend_window:]
        
        # Fit second-order polynomial
        coeffs = np.polyfit(x_trend, y_trend, 2)
        
        # Extrapolate
        x_extend = np.arange(trend_window, trend_window + extension_length)
        extension = np.polyval(coeffs, x_extend)
        
        return extension
    
    def _add_realistic_noise(self, values: np.ndarray, curve_type: str, extension_length: int) -> np.ndarray:
        """Add curve-specific realistic noise"""
        # Analyze existing noise characteristics
        local_std = np.std(np.diff(values))  # Local variation
        
        # Curve-specific noise patterns
        noise_params = {
            'GR': {'scale': local_std * 0.5, 'correlation': 0.3},
            'CNL': {'scale': local_std * 0.3, 'correlation': 0.5},
            'DEN': {'scale': local_std * 0.2, 'correlation': 0.7},
            'RLLD': {'scale': local_std * 0.4, 'correlation': 0.2},  # More erratic
            'VP': {'scale': local_std * 0.3, 'correlation': 0.6},
            'AC': {'scale': local_std * 0.3, 'correlation': 0.6}
        }
        
        params = noise_params.get(curve_type.upper(), {'scale': local_std * 0.3, 'correlation': 0.5})
        
        # Generate correlated noise
        noise = np.random.normal(0, params['scale'], extension_length)
        
        # Apply correlation
        for i in range(1, len(noise)):
            noise[i] = params['correlation'] * noise[i-1] + (1 - params['correlation']) * noise[i]
        
        # Add to base level (last value)
        base_level = values[-1]
        return base_level + noise
    
    def _extend_patterns(self, values: np.ndarray, extension_length: int) -> np.ndarray:
        """Extend patterns found in original data"""
        # Find repeating patterns using autocorrelation
        pattern_length = min(100, len(values) // 4)
        
        if pattern_length < 10:
            # Not enough data for pattern detection
            return np.full(extension_length, values[-1])
        
        # Use last pattern_length points as template
        pattern = values[-pattern_length:]
        
        # Repeat and truncate to desired length
        repeats = extension_length // pattern_length + 1
        extended = np.tile(pattern, repeats)[:extension_length]
        
        # Add smooth transition from original data
        transition_length = min(20, extension_length // 4)
        if transition_length > 0:
            transition_weights = np.linspace(0, 1, transition_length)
            extended[:transition_length] = (
                values[-1] * (1 - transition_weights) + 
                extended[:transition_length] * transition_weights
            )
        
        return extended
    
    def _apply_curve_constraints(self, extension: np.ndarray, curve_type: str, 
                               original_values: np.ndarray) -> np.ndarray:
        """Apply geological and physical constraints to extended data"""
        # Define physical ranges for different curves
        constraints = {
            'GR': (0, 200),      # API units
            'CNL': (0, 60),      # Porosity %
            'DEN': (1.5, 3.0),   # g/cm³
            'RLLD': (0.1, 1000), # ohm-m
            'VP': (40, 400),     # μs/ft
            'AC': (40, 400)      # μs/ft
        }
        
        if curve_type.upper() in constraints:
            min_val, max_val = constraints[curve_type.upper()]
            extension = np.clip(extension, min_val, max_val)
        
        # Smooth extreme changes
        max_change = np.std(np.diff(original_values)) * 3
        last_value = original_values[-1]
        
        for i in range(len(extension)):
            if i == 0:
                change = extension[i] - last_value
            else:
                change = extension[i] - extension[i-1]
            
            if abs(change) > max_change:
                if i == 0:
                    extension[i] = last_value + np.sign(change) * max_change
                else:
                    extension[i] = extension[i-1] + np.sign(change) * max_change
        
        return extension


class MemoryEfficientDataset(torch.utils.data.Dataset):
    """Memory-efficient dataset with on-the-fly processing and caching"""
    
    def __init__(self, data_dict: Dict[str, np.ndarray], input_curves: List[str],
                 target_curve: str, normalizer, sequence_config: Dict[str, int],
                 augment_data: bool = True, cache_size: int = 1000):
        
        self.input_curves = input_curves
        self.target_curve = target_curve
        self.normalizer = normalizer
        self.sequence_config = sequence_config
        self.cache_size = cache_size
        self.cache = {}
        
        # Apply data augmentation if needed
        if augment_data and len(next(iter(data_dict.values()))) < 10000:
            augmenter = DataAugmentationEngine(augmentation_factor=2.0)
            data_dict = augmenter.augment_well_data(data_dict)
        
        # Store processed data
        self.data = self._prepare_data(data_dict)
        self.length = self._calculate_length()
        
        logger.info(f"MemoryEfficientDataset created: {self.length} samples")
    
    def _prepare_data(self, data_dict: Dict[str, np.ndarray]) -> Dict[str, torch.Tensor]:
        """Prepare and normalize data"""
        # Ensure all curves are present
        for curve in self.input_curves + [self.target_curve]:
            if curve not in data_dict:
                raise ValueError(f"Curve {curve} not found in data")
        
        # Convert to tensors and normalize
        prepared_data = {}
        
        # Normalize input curves
        input_data = np.stack([data_dict[curve] for curve in self.input_curves])
        normalized_inputs = self.normalizer.normalize_inputs(
            {curve: data_dict[curve] for curve in self.input_curves}
        )
        prepared_data['inputs'] = torch.from_numpy(
            np.stack([normalized_inputs[curve] for curve in self.input_curves])
        ).float()
        
        # Normalize target
        target_data = data_dict[self.target_curve]
        normalized_target = self.normalizer.normalize_target(target_data, self.target_curve)
        prepared_data['target'] = torch.from_numpy(normalized_target).float()
        
        return prepared_data
    
    def _calculate_length(self) -> int:
        """Calculate dataset length based on sequence configuration"""
        data_length = len(self.data['target'])
        seq_length = self.sequence_config['length']
        stride = self.sequence_config['stride']
        
        if data_length < seq_length:
            return 0
        
        return (data_length - seq_length) // stride + 1
    
    def __len__(self) -> int:
        return self.length
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, torch.Tensor]:
        # Check cache first
        if idx in self.cache:
            return self.cache[idx]
        
        # Calculate sequence boundaries
        start_idx = idx * self.sequence_config['stride']
        end_idx = start_idx + self.sequence_config['length']
        
        # Extract sequences
        input_sequence = self.data['inputs'][:, start_idx:end_idx]
        target_sequence = self.data['target'][start_idx:end_idx]
        
        # Add to cache if space available
        if len(self.cache) < self.cache_size:
            self.cache[idx] = (input_sequence, target_sequence)
        
        return input_sequence, target_sequence


def create_optimized_datasets(train_data: Dict[str, np.ndarray], 
                            val_data: Dict[str, np.ndarray],
                            input_curves: List[str], target_curve: str,
                            normalizer, min_train_samples: int = 50,
                            min_val_samples: int = 20) -> Tuple[MemoryEfficientDataset, MemoryEfficientDataset]:
    """Create optimized train and validation datasets"""
    
    # Optimize configurations
    optimizer = IntelligentSequenceOptimizer()
    
    train_config = optimizer.optimize_configuration(
        train_data, target_samples=min_train_samples
    )
    val_config = optimizer.optimize_configuration(
        val_data, target_samples=min_val_samples
    )
    
    # Create datasets
    train_dataset = MemoryEfficientDataset(
        train_data, input_curves, target_curve, normalizer,
        train_config, augment_data=True
    )
    
    val_dataset = MemoryEfficientDataset(
        val_data, input_curves, target_curve, normalizer,
        val_config, augment_data=False  # No augmentation for validation
    )
    
    # Validate dataset sizes
    if len(train_dataset) < min_train_samples // 2:
        logger.warning(f"Training dataset too small: {len(train_dataset)} samples")
        
    if len(val_dataset) < min_val_samples // 2:
        logger.warning(f"Validation dataset too small: {len(val_dataset)} samples")
    
    logger.info(f"Optimized datasets created: train={len(train_dataset)}, val={len(val_dataset)}")
    
    return train_dataset, val_dataset


def analyze_dataset_quality(dataset: torch.utils.data.Dataset, 
                          sequence_config: Dict[str, int]) -> DatasetMetrics:
    """Analyze dataset quality and provide recommendations"""
    
    # Calculate metrics
    total_samples = len(dataset)
    sequence_length = sequence_config['length']
    stride = sequence_config['stride']
    
    # Estimate coverage and overlap
    if hasattr(dataset, 'data'):
        original_length = len(dataset.data['target'])
        coverage_ratio = (total_samples * stride + sequence_length) / original_length
        overlap_ratio = max(0, (sequence_length - stride) / sequence_length)
    else:
        coverage_ratio = 1.0  # Unknown
        overlap_ratio = 0.5   # Estimate
    
    # Calculate data efficiency (balance between samples and coverage)
    data_efficiency = (total_samples / 100) * (coverage_ratio / 2) * (1 - overlap_ratio / 2)
    
    # Generate recommendations
    recommendations = []
    
    if total_samples < 20:
        recommendations.append("Increase data size or reduce sequence length")
    
    if overlap_ratio > 0.8:
        recommendations.append("Increase stride to reduce overlap")
    
    if coverage_ratio < 0.7:
        recommendations.append("Reduce stride or increase data size for better coverage")
    
    if data_efficiency < 0.5:
        recommendations.append("Consider data augmentation or different sequence strategy")
    
    return DatasetMetrics(
        total_samples=total_samples,
        sequence_length=sequence_length,
        stride=stride,
        coverage_ratio=coverage_ratio,
        overlap_ratio=overlap_ratio,
        data_efficiency=data_efficiency,
        recommended_changes=recommendations
    )


if __name__ == "__main__":
    # Test the optimization system
    from multiwell.demos.demo_curve_prediction import create_realistic_well_data
    
    # Create test data
    test_data = create_realistic_well_data(2000)
    
    # Test optimizer
    optimizer = IntelligentSequenceOptimizer()
    config = optimizer.optimize_configuration(test_data, target_samples=30)
    
    print(f"Optimized configuration: {config}")
    
    # Test augmentation
    augmenter = DataAugmentationEngine()
    augmented = augmenter.augment_well_data(test_data, target_length=5000)
    
    print(f"Augmentation: {len(next(iter(test_data.values())))} → {len(next(iter(augmented.values())))}")