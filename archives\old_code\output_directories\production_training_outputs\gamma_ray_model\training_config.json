{"learning_rate": 6e-05, "weight_decay": 1e-05, "batch_size": 16, "max_epochs": 80, "patience": 35, "optimizer": "adam", "scheduler": "exponential", "scheduler_params": {"gamma": 0.95}, "loss_config": {"type": "curve_specific", "custom_params": {"constraint_weight": 0.3, "physics_constraints": true}}, "save_best_only": true, "save_frequency": 12, "validation_frequency": 1, "gradient_clipping": 0.9, "mixed_precision": false, "description": "Optimized configuration for gamma ray (GR) prediction", "target_curve": "GR", "save_dir": "production_training_outputs\\gamma_ray_model"}