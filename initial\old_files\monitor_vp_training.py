"""
Monitor and evaluate Vp model training progress
"""
import os
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np
import torch
from utils import get_device, load_checkpoint, cal_RMSE, cal_R2
from model import MWLT_Small, MWLT_Base, MWLT_Large
from test_vp_prediction import predict_vp
import argpar<PERSON>

def plot_training_progress(log_file, save_path=None):
    """
    Plot training progress from log file
    """
    if not os.path.exists(log_file):
        print(f"Log file not found: {log_file}")
        return
    
    df = pd.read_csv(log_file)
    
    fig, axes = plt.subplots(2, 2, figsize=(15, 10))
    
    # Loss curves
    axes[0, 0].plot(df['epoch'], df['train_loss'], 'b-', label='Train Loss', linewidth=2)
    axes[0, 0].plot(df['epoch'], df['val_loss'], 'r-', label='Val Loss', linewidth=2)
    axes[0, 0].set_xlabel('Epoch')
    axes[0, 0].set_ylabel('Loss')
    axes[0, 0].set_title('Training and Validation Loss')
    axes[0, 0].legend()
    axes[0, 0].grid(True, alpha=0.3)
    
    # RMSE
    axes[0, 1].plot(df['epoch'], df['val_rmse'], 'g-', label='Val RMSE', linewidth=2)
    axes[0, 1].set_xlabel('Epoch')
    axes[0, 1].set_ylabel('RMSE')
    axes[0, 1].set_title('Validation RMSE')
    axes[0, 1].legend()
    axes[0, 1].grid(True, alpha=0.3)
    
    # R²
    axes[1, 0].plot(df['epoch'], df['val_r2'], 'm-', label='Val R²', linewidth=2)
    axes[1, 0].set_xlabel('Epoch')
    axes[1, 0].set_ylabel('R²')
    axes[1, 0].set_title('Validation R²')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    
    # Loss ratio (overfitting indicator)
    loss_ratio = df['val_loss'] / df['train_loss']
    axes[1, 1].plot(df['epoch'], loss_ratio, 'orange', label='Val/Train Loss Ratio', linewidth=2)
    axes[1, 1].axhline(y=1.0, color='red', linestyle='--', alpha=0.7, label='Perfect Fit')
    axes[1, 1].set_xlabel('Epoch')
    axes[1, 1].set_ylabel('Loss Ratio')
    axes[1, 1].set_title('Overfitting Indicator')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"Training progress plot saved: {save_path}")
    
    plt.show()

def evaluate_trained_model(model_path, model_type="base", test_data_path="../data_vp_training/val"):
    """
    Evaluate a trained Vp model
    """
    print(f"=== Evaluating Trained Vp Model ===")
    print(f"Model: {model_path}")
    print(f"Test data: {test_data_path}")
    
    device = get_device(0)
    
    # Load model
    if model_type == "small":
        model = MWLT_Small(in_channels=4, out_channels=1, feature_num=64)
    elif model_type == "base":
        model = MWLT_Base(in_channels=4, out_channels=1, feature_num=64)
    elif model_type == "large":
        model = MWLT_Large(in_channels=4, out_channels=1, feature_num=128)
    
    model = model.to(device)
    
    # Load checkpoint
    try:
        model_dict, epoch, loss = load_checkpoint(model_path, device)
        model.load_state_dict(model_dict)
        model.eval()
        
        print(f"✅ Model loaded successfully")
        print(f"   Epoch: {epoch}")
        print(f"   Loss: {loss:.6f}")
        
        if 'rmse' in model_dict:
            print(f"   RMSE: {model_dict['rmse']:.4f}")
        if 'r2' in model_dict:
            print(f"   R²: {model_dict['r2']:.4f}")
            
    except Exception as e:
        print(f"❌ Error loading model: {e}")
        return
    
    # Test on validation data
    from torch.utils.data import DataLoader
    from dataset import WellDataset
    
    val_dataset = WellDataset(
        root_path=test_data_path,
        input_curves=["GR", "CNL", "DEN", "RLLD"],
        output_curves=["AC"],
        transform=False,
        total_seqlen=720,
        effect_seqlen=640
    )
    
    val_loader = DataLoader(dataset=val_dataset, batch_size=1, shuffle=False)
    
    print(f"Testing on {len(val_dataset)} samples...")
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for conditions, targets in val_loader:
            conditions = conditions.to(device)
            targets = targets.to(device)
            
            predictions = model(conditions)
            
            all_predictions.append(predictions.cpu().numpy())
            all_targets.append(targets.cpu().numpy())
    
    # Calculate metrics
    all_predictions = np.concatenate(all_predictions, axis=0).flatten()
    all_targets = np.concatenate(all_targets, axis=0).flatten()
    
    rmse = cal_RMSE(all_predictions, all_targets)
    r2 = cal_R2(all_predictions, all_targets)
    
    print(f"\n📊 Evaluation Results:")
    print(f"   RMSE: {rmse:.4f}")
    print(f"   R²: {r2:.4f}")
    print(f"   Mean Absolute Error: {np.mean(np.abs(all_predictions - all_targets)):.4f}")
    print(f"   Prediction range: [{all_predictions.min():.2f}, {all_predictions.max():.2f}]")
    print(f"   Target range: [{all_targets.min():.2f}, {all_targets.max():.2f}]")
    
    return rmse, r2

def compare_models(model_paths, model_names=None):
    """
    Compare multiple trained models
    """
    print("=== Model Comparison ===")
    
    if model_names is None:
        model_names = [f"Model_{i+1}" for i in range(len(model_paths))]
    
    results = []
    
    for i, (model_path, model_name) in enumerate(zip(model_paths, model_names)):
        print(f"\nEvaluating {model_name}...")
        try:
            rmse, r2 = evaluate_trained_model(model_path)
            results.append({
                'Model': model_name,
                'Path': model_path,
                'RMSE': rmse,
                'R²': r2
            })
        except Exception as e:
            print(f"❌ Error evaluating {model_name}: {e}")
    
    if results:
        df_results = pd.DataFrame(results)
        print(f"\n📊 Comparison Results:")
        print(df_results.to_string(index=False))
        
        # Find best model
        best_rmse_idx = df_results['RMSE'].idxmin()
        best_r2_idx = df_results['R²'].idxmax()
        
        print(f"\n🏆 Best Models:")
        print(f"   Lowest RMSE: {df_results.loc[best_rmse_idx, 'Model']} (RMSE: {df_results.loc[best_rmse_idx, 'RMSE']:.4f})")
        print(f"   Highest R²: {df_results.loc[best_r2_idx, 'Model']} (R²: {df_results.loc[best_r2_idx, 'R²']:.4f})")

def monitor_training_directory(training_dir):
    """
    Monitor all training runs in a directory
    """
    print(f"=== Monitoring Training Directory: {training_dir} ===")
    
    if not os.path.exists(training_dir):
        print(f"Directory not found: {training_dir}")
        return
    
    # Find all training runs
    training_runs = []
    for item in os.listdir(training_dir):
        item_path = os.path.join(training_dir, item)
        if os.path.isdir(item_path):
            log_file = os.path.join(item_path, "training_log.csv")
            model_file = None
            
            # Look for model files
            for model_name in ["best_vp_model.pth", "best_vp_transfer_model.pth", "best_model.pth"]:
                model_path = os.path.join(item_path, model_name)
                if os.path.exists(model_path):
                    model_file = model_path
                    break
            
            if os.path.exists(log_file):
                training_runs.append({
                    'name': item,
                    'path': item_path,
                    'log_file': log_file,
                    'model_file': model_file
                })
    
    if not training_runs:
        print("No training runs found!")
        return
    
    print(f"Found {len(training_runs)} training runs:")
    
    for run in training_runs:
        print(f"\n📁 {run['name']}:")
        print(f"   Path: {run['path']}")
        
        # Read training log
        if os.path.exists(run['log_file']):
            df = pd.read_csv(run['log_file'])
            last_epoch = df.iloc[-1]
            
            print(f"   Epochs: {len(df)}")
            print(f"   Final train loss: {last_epoch['train_loss']:.6f}")
            print(f"   Final val loss: {last_epoch['val_loss']:.6f}")
            print(f"   Final RMSE: {last_epoch['val_rmse']:.4f}")
            print(f"   Final R²: {last_epoch['val_r2']:.4f}")
            
            # Plot training progress
            plot_save_path = os.path.join(run['path'], "training_progress.png")
            plot_training_progress(run['log_file'], plot_save_path)
        
        # Evaluate model if available
        if run['model_file']:
            print(f"   Model: {os.path.basename(run['model_file'])}")
            try:
                rmse, r2 = evaluate_trained_model(run['model_file'])
                print(f"   Evaluation RMSE: {rmse:.4f}")
                print(f"   Evaluation R²: {r2:.4f}")
            except Exception as e:
                print(f"   Evaluation failed: {e}")

def main():
    parser = argparse.ArgumentParser(description="Monitor Vp training progress")
    parser.add_argument("action", choices=["plot", "evaluate", "compare", "monitor"],
                       help="Action to perform")
    parser.add_argument("--log_file", type=str, help="Training log file")
    parser.add_argument("--model_path", type=str, help="Model checkpoint path")
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"])
    parser.add_argument("--training_dir", type=str, default="../", help="Training directory to monitor")
    parser.add_argument("--model_paths", nargs="+", help="Multiple model paths for comparison")
    
    args = parser.parse_args()
    
    if args.action == "plot":
        if args.log_file:
            plot_training_progress(args.log_file)
        else:
            print("Please provide --log_file")
    
    elif args.action == "evaluate":
        if args.model_path:
            evaluate_trained_model(args.model_path, args.model_type)
        else:
            print("Please provide --model_path")
    
    elif args.action == "compare":
        if args.model_paths:
            compare_models(args.model_paths)
        else:
            print("Please provide --model_paths")
    
    elif args.action == "monitor":
        monitor_training_directory(args.training_dir)

if __name__ == "__main__":
    main()
