# Core ML Architecture Components

## Model Architecture

### VpTransformer Class
```python
class VpTransformer(nn.Module):
    """
    Complete transformer model for Vp prediction with proper scaling
    """
    def __init__(self, in_channels=4, out_channels=1, feature_num=64, res_num=4, 
                 encoder_num=4, use_pe=True, dim=64, seq_len=640, num_heads=4, 
                 mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0., position_drop=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        
        self.feature_embedding = Input_Embedding(in_channels=in_channels, 
                                                feature_num=feature_num, res_num=res_num)
        self.position_embedding = PositionalEncoding(d_model=dim, dropout=position_drop, 
                                                    seq_len=seq_len)
        self.use_pe = use_pe
        self.transformer_encoder = TransformerBlock(block_num=encoder_num, dim=dim, 
                                                   num_heads=num_heads, mlp_ratio=mlp_ratio, 
                                                   qkv_bias=qkv_bias, drop=drop, attn_drop=attn_drop,
                                                   act_layer=act_layer, norm_layer=norm_layer)
        
        self.decoder = VpDecoder(out_channels=out_channels, feature_num=feature_num, 
                                res_num=res_num)

    def forward(self, x):
        x = self.feature_embedding(x)
        x = x.transpose(-2, -1)
        if self.use_pe:
            x = self.position_embedding(x)
        x = self.transformer_encoder(x)
        x = x.transpose(-2, -1)
        x = self.decoder(x)
        return x
```

### VpDecoder Class
```python
class VpDecoder(nn.Module):
    """
    Decoder specifically designed for Vp prediction
    Outputs normalized values that are denormalized during inference
    """
    def __init__(self, res_num=4, out_channels=1, feature_num=64):
        super().__init__()
        self.rescnn = nn.ModuleList([
            ResCNN(in_channels=feature_num, out_channels=feature_num,
                   kernel_size=11, padding=5, stride=1)
            for i in range(res_num)
        ])

        self.out_layer = nn.Sequential(
            nn.Conv1d(in_channels=feature_num, out_channels=feature_num//2,
                     kernel_size=11, padding=5, stride=1),
            nn.ReLU(),
            nn.Conv1d(in_channels=feature_num//2, out_channels=out_channels,
                     kernel_size=11, padding=5, stride=1)
        )

    def forward(self, x):
        for model in self.rescnn:
            x = model(x)
        x = self.out_layer(x)
        return x
```

### Model Variants
```python
def MWLT_Vp_Small(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=2, encoder_num=2, 
                        num_heads=2, **kwargs)

def MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=4, encoder_num=4, 
                        num_heads=4, **kwargs)

def MWLT_Vp_Large(in_channels=4, out_channels=1, feature_num=128, **kwargs):
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=6, encoder_num=6, 
                        num_heads=8, **kwargs)
```

## Data Processing

### VpDataNormalizer Class
```python
class VpDataNormalizer:
    def __init__(self):
        self.input_stats = {
            'GR': {'mean': 75.0, 'std': 50.0, 'min': 0, 'max': 200},
            'CNL': {'mean': 20.0, 'std': 15.0, 'min': 0, 'max': 60},
            'DEN': {'mean': 2.5, 'std': 0.3, 'min': 1.5, 'max': 3.0},
            'RLLD': {'mean': 10.0, 'std': 50.0, 'min': 0.1, 'max': 1000}
        }
        self.vp_stats = {'mean': 200.0, 'std': 75.0, 'min': 40, 'max': 400}
    
    def normalize_inputs(self, curves_dict):
        normalized = {}
        for curve_name, data in curves_dict.items():
            if curve_name in self.input_stats:
                stats = self.input_stats[curve_name]
                if curve_name == 'RLLD':
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized[curve_name] = (data - 1.0) / 2.0
                else:
                    normalized[curve_name] = (data - stats['mean']) / stats['std']
                    normalized[curve_name] = torch.clamp(normalized[curve_name], -3, 3) / 3
            else:
                normalized[curve_name] = data
        return normalized
    
    def normalize_vp(self, vp_data):
        return (vp_data - self.vp_stats['mean']) / self.vp_stats['std']
    
    def denormalize_vp(self, normalized_vp):
        return normalized_vp * self.vp_stats['std'] + self.vp_stats['mean']
```

### VpDataset Class
```python
class VpDataset(torch.utils.data.Dataset):
    def __init__(self, input_data, target_data, normalizer, total_seqlen=720, effect_seqlen=640, transform=False):
        self.input_data = input_data
        self.target_data = target_data
        self.normalizer = normalizer
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        self.transform = transform
        
    def __len__(self):
        return len(self.input_data)
    
    def __getitem__(self, idx):
        inputs = self.input_data[idx]
        target = self.target_data[idx]
        
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]
        
        return torch.FloatTensor(inputs), torch.FloatTensor(target).unsqueeze(0)
```

### Data Creation Function
```python
def create_improved_vp_data():
    processor = LASProcessor()
    normalizer = VpDataNormalizer()
    
    input_files = find_data_files()
    if not input_files:
        return np.array([]), np.array([])
    
    all_inputs = []
    all_targets = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            continue
            
        curves = processor.process_hdf5_to_las_format(file_path)
        sequence_length = 720
        step_size = 360
        
        data_length = len(curves['AC'])
        num_windows = max(1, (data_length - sequence_length) // step_size + 1)
        
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + sequence_length
            
            if end_idx > data_length:
                start_idx = data_length - sequence_length
                end_idx = data_length
            
            window_curves = {}
            for curve_name, data in curves.items():
                window_curves[curve_name] = data[start_idx:end_idx]
            
            input_features = []
            for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
                if curve_name in window_curves:
                    data = torch.FloatTensor(window_curves[curve_name])
                    if curve_name == 'RLLD':
                        data = torch.log10(torch.clamp(data, min=0.1))
                        normalized = (data - 1.0) / 2.0
                    else:
                        stats = normalizer.input_stats[curve_name]
                        normalized = (data - stats['mean']) / stats['std']
                        normalized = torch.clamp(normalized, -3, 3) / 3
                    input_features.append(normalized.numpy())
                else:
                    input_features.append(np.zeros(sequence_length))
            
            target = window_curves.get('AC', np.zeros(sequence_length))
            all_inputs.append(np.array(input_features))
            all_targets.append(target)
    
    return np.array(all_inputs), np.array(all_targets)
```

## Training Components

### VpLoss Class
```python
class VpLoss(nn.Module):
    def __init__(self, alpha=1.0, beta=0.1):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.alpha = alpha
        self.beta = beta
        
    def forward(self, pred, target):
        mse = self.mse_loss(pred, target)
        constraint_loss = torch.mean(torch.relu(pred - 500) + torch.relu(20 - pred))
        return self.alpha * mse + self.beta * constraint_loss
```

## Inference Components

### Model Loading Function
```python
def load_trained_model(model_path):
    try:
        device = get_device(device_id=0)
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        
        checkpoint = torch.load(model_path, map_location=device)
        
        if isinstance(checkpoint, dict):
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            elif 'state_dict' in checkpoint:
                model.load_state_dict(checkpoint['state_dict'])
            else:
                model.load_state_dict(checkpoint)
        else:
            model.load_state_dict(checkpoint)
        
        model = model.to(device)
        model.eval()
        return model, device
        
    except Exception as e:
        return None, None
```

### Prediction Function
```python
def predict_with_model(model, device, input_data, normalizer):
    with torch.no_grad():
        input_tensor = torch.FloatTensor(input_data).unsqueeze(0).to(device)
        normalized_prediction = model(input_tensor)
        normalized_prediction_np = normalized_prediction.squeeze().cpu().numpy()
    
    prediction = normalizer.denormalize_vp(normalized_prediction_np)
    return prediction
```

## Utility Functions

### Core Utilities
```python
def resample_curve(data, target_length):
    if len(data) == target_length:
        return data
    original_indices = np.linspace(0, len(data) - 1, len(data))
    target_indices = np.linspace(0, len(data) - 1, target_length)
    return np.interp(target_indices, original_indices, data)

def get_device(device_id=0):
    if torch.cuda.is_available() and device_id >= 0:
        return torch.device(f'cuda:{device_id}')
    return torch.device('cpu')
```