{"model": {"type": "base", "input_channels": 4, "output_channels": 1, "feature_num": 64, "sequence_length": 720, "effect_length": 640}, "training": {"batch_size": 8, "learning_rate": 0.0001, "weight_decay": 1e-05, "epochs": 200, "patience": 50, "validation_split": 0.2, "random_seed": 42, "device": 0, "resume_training": false}, "validation": {"cv_folds": 5, "monte_carlo_samples": 50, "confidence_level": 0.95, "metrics": ["r2", "rmse", "mae"]}, "data": {"input_curves": ["GR", "CNL", "DEN", "RLLD"], "target_curve": "AC", "data_augmentation": true, "augmentation_factor": 2.0, "quality_threshold": 0.8}, "output": {"base_dir": "vp_prediction_outputs", "save_plots": true, "save_models": true, "save_predictions": true, "plot_format": "png", "plot_dpi": 300}}