CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-22 14:02:51,807 - INFO - � Enhanced Training Pipeline Initialized
2025-08-22 14:02:51,808 - INFO - � Device: cuda:0
2025-08-22 14:02:51,808 - INFO - � Results directory: enhanced_training_outputs
2025-08-22 14:02:51,809 - INFO - � Starting Enhanced Multi-Curve Training Pipeline
2025-08-22 14:02:51,809 - INFO - � Loading and validating training data...
2025-08-22 14:02:51,810 - INFO -    ⚠️  Real training data not available, using synthetic data
2025-08-22 14:02:51,829 - INFO -    ✅ Generated synthetic training data
2025-08-22 14:02:51,829 - INFO - ✅ Loaded data for curves: ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:51,831 - INFO -    GR: 72000/72000 valid (100.0%)
2025-08-22 14:02:51,833 - INFO -    CNL: 72000/72000 valid (100.0%)
2025-08-22 14:02:51,834 - INFO -    DEN: 72000/72000 valid (100.0%)
2025-08-22 14:02:51,836 - INFO -    AC: 72000/72000 valid (100.0%)
2025-08-22 14:02:51,838 - INFO -    RLLD: 72000/72000 valid (100.0%)
2025-08-22 14:02:51,848 - INFO - � Creating 5-fold cross-validation splits...
2025-08-22 14:02:51,851 - INFO -    Fold 1: 80 train, 20 val samples
2025-08-22 14:02:51,854 - INFO -    Fold 2: 80 train, 20 val samples
2025-08-22 14:02:51,857 - INFO -    Fold 3: 80 train, 20 val samples
2025-08-22 14:02:51,860 - INFO -    Fold 4: 80 train, 20 val samples
2025-08-22 14:02:51,863 - INFO -    Fold 5: 80 train, 20 val samples
2025-08-22 14:02:51,864 - INFO - � Training targets: ['AC', 'DEN', 'CNL', 'GR', 'RLLD']
2025-08-22 14:02:51,866 - INFO -
================================================================================
2025-08-22 14:02:51,868 - INFO - � TRAINING AC PREDICTION MODELS
2025-08-22 14:02:51,872 - INFO - ================================================================================
2025-08-22 14:02:51,875 - INFO - � Training model for AC (Fold 1)
2025-08-22 14:02:51,877 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 14:02:51,879 - ERROR -    ❌ Training failed for AC (Fold 1): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,901 - INFO - � Training model for AC (Fold 2)
2025-08-22 14:02:51,902 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 14:02:51,903 - ERROR -    ❌ Training failed for AC (Fold 2): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,918 - INFO - � Training model for AC (Fold 3)
2025-08-22 14:02:51,919 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 14:02:51,920 - ERROR -    ❌ Training failed for AC (Fold 3): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,933 - INFO - � Training model for AC (Fold 4)
2025-08-22 14:02:51,933 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 14:02:51,935 - ERROR -    ❌ Training failed for AC (Fold 4): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,944 - INFO - � Training model for AC (Fold 5)
2025-08-22 14:02:51,945 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 14:02:51,945 - ERROR -    ❌ Training failed for AC (Fold 5): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,954 - INFO - � Calculating confidence metrics for AC...
2025-08-22 14:02:51,955 - WARNING -    ⚠️  No valid R² scores for AC
2025-08-22 14:02:51,955 - INFO -
================================================================================
2025-08-22 14:02:51,956 - INFO - � TRAINING DEN PREDICTION MODELS
2025-08-22 14:02:51,956 - INFO - ================================================================================
2025-08-22 14:02:51,957 - INFO - � Training model for DEN (Fold 1)
2025-08-22 14:02:51,957 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 14:02:51,958 - ERROR -    ❌ Training failed for DEN (Fold 1): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,967 - INFO - � Training model for DEN (Fold 2)
2025-08-22 14:02:51,967 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 14:02:51,968 - ERROR -    ❌ Training failed for DEN (Fold 2): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,979 - INFO - � Training model for DEN (Fold 3)
2025-08-22 14:02:51,980 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 14:02:51,980 - ERROR -    ❌ Training failed for DEN (Fold 3): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:51,989 - INFO - � Training model for DEN (Fold 4)
2025-08-22 14:02:51,990 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 14:02:51,991 - ERROR -    ❌ Training failed for DEN (Fold 4): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,000 - INFO - � Training model for DEN (Fold 5)
2025-08-22 14:02:52,000 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 14:02:52,001 - ERROR -    ❌ Training failed for DEN (Fold 5): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,016 - INFO - � Calculating confidence metrics for DEN...
2025-08-22 14:02:52,016 - WARNING -    ⚠️  No valid R² scores for DEN
2025-08-22 14:02:52,018 - INFO -
================================================================================
2025-08-22 14:02:52,018 - INFO - � TRAINING CNL PREDICTION MODELS
2025-08-22 14:02:52,019 - INFO - ================================================================================
2025-08-22 14:02:52,019 - INFO - � Training model for CNL (Fold 1)
2025-08-22 14:02:52,020 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,021 - ERROR -    ❌ Training failed for CNL (Fold 1): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,029 - INFO - � Training model for CNL (Fold 2)
2025-08-22 14:02:52,029 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,030 - ERROR -    ❌ Training failed for CNL (Fold 2): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,039 - INFO - � Training model for CNL (Fold 3)
2025-08-22 14:02:52,040 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,040 - ERROR -    ❌ Training failed for CNL (Fold 3): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,055 - INFO - � Training model for CNL (Fold 4)
2025-08-22 14:02:52,057 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,062 - ERROR -    ❌ Training failed for CNL (Fold 4): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,081 - INFO - � Training model for CNL (Fold 5)
2025-08-22 14:02:52,084 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,087 - ERROR -    ❌ Training failed for CNL (Fold 5): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,103 - INFO - � Calculating confidence metrics for CNL...
2025-08-22 14:02:52,105 - WARNING -    ⚠️  No valid R² scores for CNL
2025-08-22 14:02:52,106 - INFO -
================================================================================
2025-08-22 14:02:52,107 - INFO - � TRAINING GR PREDICTION MODELS
2025-08-22 14:02:52,108 - INFO - ================================================================================
2025-08-22 14:02:52,112 - INFO - � Training model for GR (Fold 1)
2025-08-22 14:02:52,113 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,114 - ERROR -    ❌ Training failed for GR (Fold 1): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,130 - INFO - � Training model for GR (Fold 2)
2025-08-22 14:02:52,130 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,131 - ERROR -    ❌ Training failed for GR (Fold 2): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,139 - INFO - � Training model for GR (Fold 3)
2025-08-22 14:02:52,140 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,141 - ERROR -    ❌ Training failed for GR (Fold 3): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,153 - INFO - � Training model for GR (Fold 4)
2025-08-22 14:02:52,155 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,156 - ERROR -    ❌ Training failed for GR (Fold 4): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,173 - INFO - � Training model for GR (Fold 5)
2025-08-22 14:02:52,173 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 14:02:52,174 - ERROR -    ❌ Training failed for GR (Fold 5): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,184 - INFO - � Calculating confidence metrics for GR...
2025-08-22 14:02:52,185 - WARNING -    ⚠️  No valid R² scores for GR
2025-08-22 14:02:52,186 - INFO -
================================================================================
2025-08-22 14:02:52,188 - INFO - � TRAINING RLLD PREDICTION MODELS
2025-08-22 14:02:52,188 - INFO - ================================================================================
2025-08-22 14:02:52,189 - INFO - � Training model for RLLD (Fold 1)
2025-08-22 14:02:52,190 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 14:02:52,190 - ERROR -    ❌ Training failed for RLLD (Fold 1): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,198 - INFO - � Training model for RLLD (Fold 2)
2025-08-22 14:02:52,199 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 14:02:52,210 - ERROR -    ❌ Training failed for RLLD (Fold 2): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,217 - INFO - � Training model for RLLD (Fold 3)
2025-08-22 14:02:52,219 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 14:02:52,221 - ERROR -    ❌ Training failed for RLLD (Fold 3): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,229 - INFO - � Training model for RLLD (Fold 4)
2025-08-22 14:02:52,230 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 14:02:52,230 - ERROR -    ❌ Training failed for RLLD (Fold 4): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,243 - INFO - � Training model for RLLD (Fold 5)
2025-08-22 14:02:52,244 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 14:02:52,245 - ERROR -    ❌ Training failed for RLLD (Fold 5): Data length (80) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (80) is less than required sequence length (720)
2025-08-22 14:02:52,271 - INFO - � Calculating confidence metrics for RLLD...
2025-08-22 14:02:52,272 - WARNING -    ⚠️  No valid R² scores for RLLD
2025-08-22 14:02:52,273 - INFO - � Saving comprehensive results...
2025-08-22 14:02:52,282 - INFO -    ✅ Results saved to: enhanced_training_outputs\enhanced_training_results.json
2025-08-22 14:02:52,283 - INFO - � Generating summary report...
2025-08-22 14:02:52,291 - INFO -    ✅ Summary report saved to: enhanced_training_outputs\training_summary_report.md
2025-08-22 14:02:52,292 - INFO -
� Enhanced Training Pipeline Complete!
2025-08-22 14:02:52,292 - INFO - � Results saved in: enhanced_training_outputs