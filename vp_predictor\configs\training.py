"""
Training configuration templates for General Well Log Transformer

This module provides pre-configured training templates optimized for different 
curve types and common use cases, building upon the Phase 1 model templates.
"""

from typing import Dict, Any, List, Optional
from .curves import CURVE_CONFIGURATIONS

# Training configuration templates optimized for specific curve types
TRAINING_TEMPLATES = {
    'vp_training': {
        'description': 'Optimized configuration for sonic velocity (AC) prediction',
        'target_curve': 'AC',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'constraint_weight': 1.0,
                'physics_constraints': True
            }
        },
        'optimizer': 'adam',
        'learning_rate': 1e-4,
        'weight_decay': 1e-5,
        'batch_size': 8,
        'max_epochs': 200,
        'patience': 50,
        'scheduler': 'plateau',
        'scheduler_params': {
            'patience': 25,
            'factor': 0.5,
            'min_lr': 1e-7
        },
        'gradient_clipping': 1.0,
        'validation_frequency': 1,
        'save_frequency': 10
    },
    
    'density_training': {
        'description': 'Optimized configuration for bulk density (DEN) prediction',
        'target_curve': 'DEN',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'constraint_weight': 0.5,
                'physics_constraints': True
            }
        },
        'optimizer': 'adam',
        'learning_rate': 5e-5,
        'weight_decay': 1e-5,
        'batch_size': 16,
        'max_epochs': 150,
        'patience': 30,
        'scheduler': 'plateau',
        'scheduler_params': {
            'patience': 20,
            'factor': 0.6,
            'min_lr': 1e-7
        },
        'gradient_clipping': 0.8,
        'validation_frequency': 1,
        'save_frequency': 10
    },
    
    'neutron_training': {
        'description': 'Optimized configuration for neutron porosity (CNL) prediction',
        'target_curve': 'CNL',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'loss_type': 'huber',
                'constraint_weight': 0.8,
                'robust_loss_delta': 1.5,
                'physics_constraints': True
            }
        },
        'optimizer': 'adamw',
        'learning_rate': 8e-5,
        'weight_decay': 2e-5,
        'batch_size': 12,
        'max_epochs': 180,
        'patience': 40,
        'scheduler': 'cosine',
        'scheduler_params': {
            'T_max': 100,
            'eta_min': 1e-7
        },
        'gradient_clipping': 1.2,
        'validation_frequency': 1,
        'save_frequency': 10
    },
    
    'gamma_ray_training': {
        'description': 'Optimized configuration for gamma ray (GR) prediction',
        'target_curve': 'GR',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'constraint_weight': 0.3,
                'physics_constraints': True
            }
        },
        'optimizer': 'adam',
        'learning_rate': 6e-5,
        'weight_decay': 1e-5,
        'batch_size': 16,
        'max_epochs': 120,
        'patience': 35,
        'scheduler': 'exponential',
        'scheduler_params': {
            'gamma': 0.95
        },
        'gradient_clipping': 0.9,
        'validation_frequency': 1,
        'save_frequency': 12
    },
    
    'resistivity_training': {
        'description': 'Optimized configuration for resistivity (RLLD) prediction',
        'target_curve': 'RLLD',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'loss_type': 'mae',  # Robust to extreme values
                'constraint_weight': 0.2,
                'physics_constraints': True
            }
        },
        'optimizer': 'adamw',
        'learning_rate': 1e-4,
        'weight_decay': 3e-5,
        'batch_size': 8,
        'max_epochs': 200,
        'patience': 60,
        'scheduler': 'plateau',
        'scheduler_params': {
            'patience': 30,
            'factor': 0.4,
            'min_lr': 1e-8
        },
        'gradient_clipping': 1.5,
        'validation_frequency': 1,
        'save_frequency': 15
    },
    
    # Fast training configurations for experimentation
    'fast_vp_training': {
        'description': 'Fast training configuration for VP prediction (experimentation)',
        'target_curve': 'AC',
        'loss_config': {'type': 'curve_specific'},
        'optimizer': 'adam',
        'learning_rate': 2e-4,
        'batch_size': 16,
        'max_epochs': 50,
        'patience': 15,
        'scheduler': 'exponential',
        'scheduler_params': {'gamma': 0.9},
        'validation_frequency': 2,
        'save_frequency': 10
    },
    
    'fast_density_training': {
        'description': 'Fast training configuration for density prediction (experimentation)',
        'target_curve': 'DEN',
        'loss_config': {'type': 'curve_specific'},
        'optimizer': 'adam',
        'learning_rate': 1e-4,
        'batch_size': 24,
        'max_epochs': 30,
        'patience': 10,
        'scheduler': 'exponential',
        'scheduler_params': {'gamma': 0.88},
        'validation_frequency': 2,
        'save_frequency': 8
    },
    
    # High-precision training configurations
    'precision_vp_training': {
        'description': 'High-precision training for VP prediction (production quality)',
        'target_curve': 'AC',
        'loss_config': {
            'type': 'curve_specific',
            'custom_params': {
                'constraint_weight': 1.5,
                'physics_constraints': True
            }
        },
        'optimizer': 'adamw',
        'learning_rate': 3e-5,
        'weight_decay': 5e-6,
        'batch_size': 4,
        'max_epochs': 300,
        'patience': 80,
        'scheduler': 'plateau',
        'scheduler_params': {
            'patience': 40,
            'factor': 0.3,
            'min_lr': 1e-8
        },
        'gradient_clipping': 0.5,
        'mixed_precision': True,
        'validation_frequency': 1,
        'save_frequency': 5
    }
}

# Data configuration templates
DATA_TEMPLATES = {
    'standard_sequence': {
        'description': 'Standard sequence configuration for most applications',
        'total_length': 720,
        'effective_length': 640,
        'augmentation': True,
        'missing_data_handling': 'interpolation',
        'quality_threshold': 0.8
    },
    
    'long_sequence': {
        'description': 'Long sequence configuration for complex patterns',
        'total_length': 1440,
        'effective_length': 1280,
        'augmentation': False,
        'missing_data_handling': 'interpolation',
        'quality_threshold': 0.85
    },
    
    'short_sequence': {
        'description': 'Short sequence configuration for fast training',
        'total_length': 480,
        'effective_length': 400,
        'augmentation': True,
        'missing_data_handling': 'masking',
        'quality_threshold': 0.75
    },
    
    'robust_sequence': {
        'description': 'Robust configuration for noisy or incomplete data',
        'total_length': 720,
        'effective_length': 640,
        'augmentation': True,
        'missing_data_handling': 'interpolation',
        'quality_threshold': 0.6
    },
    
    'high_quality': {
        'description': 'High-quality data configuration with strict requirements',
        'total_length': 960,
        'effective_length': 800,
        'augmentation': False,
        'missing_data_handling': 'skip',
        'quality_threshold': 0.95
    }
}

# Model-training template combinations
INTEGRATED_TEMPLATES = {
    'standard_vp': {
        'description': 'Standard VP prediction with optimized settings',
        'model_template': 'vp_prediction',
        'training_template': 'vp_training',
        'data_template': 'standard_sequence'
    },
    
    'standard_density': {
        'description': 'Standard density prediction with optimized settings',
        'model_template': 'density_prediction',
        'training_template': 'density_training',
        'data_template': 'standard_sequence'
    },
    
    'fast_vp': {
        'description': 'Fast VP prediction for experimentation',
        'model_template': 'vp_prediction_small',
        'training_template': 'fast_vp_training',
        'data_template': 'short_sequence'
    },
    
    'precision_vp': {
        'description': 'High-precision VP prediction for production',
        'model_template': 'vp_prediction_large',
        'training_template': 'precision_vp_training',
        'data_template': 'high_quality'
    },
    
    'robust_neutron': {
        'description': 'Robust neutron porosity prediction for noisy data',
        'model_template': 'multi_curve_basic',  # Configured for single CNL output
        'training_template': 'neutron_training',
        'data_template': 'robust_sequence'
    }
}

# Training hyperparameter suggestions based on curve characteristics
CURVE_TRAINING_HINTS = {
    'AC': {
        'suggested_lr_range': (5e-5, 2e-4),
        'suggested_batch_sizes': [4, 8, 16],
        'convergence_patterns': 'Usually converges smoothly, benefits from constraint loss',
        'common_issues': ['May plateau early without proper scheduling', 'Sensitive to outliers'],
        'optimization_tips': [
            'Use constraint_weight around 1.0',
            'Patience of 50-80 epochs works well',
            'Benefits from mixed precision training'
        ]
    },
    
    'DEN': {
        'suggested_lr_range': (3e-5, 1e-4),
        'suggested_batch_sizes': [8, 16, 24],
        'convergence_patterns': 'Stable convergence, less sensitive to hyperparameters',
        'common_issues': ['May overfit on small datasets'],
        'optimization_tips': [
            'Lower constraint_weight (0.3-0.7) often sufficient',
            'Can use larger batch sizes',
            'Weight decay helps prevent overfitting'
        ]
    },
    
    'CNL': {
        'suggested_lr_range': (6e-5, 1.5e-4),
        'suggested_batch_sizes': [6, 12, 18],
        'convergence_patterns': 'May have irregular convergence due to porosity variations',
        'common_issues': ['Sensitive to outliers', 'May need robust loss functions'],
        'optimization_tips': [
            'Huber loss often works better than MSE',
            'Moderate constraint_weight (0.5-1.0)',
            'Consider longer patience for convergence'
        ]
    },
    
    'GR': {
        'suggested_lr_range': (4e-5, 1.2e-4),
        'suggested_batch_sizes': [12, 16, 24],
        'convergence_patterns': 'Generally stable but may be noisy',
        'common_issues': ['High variability in some formations', 'May need regularization'],
        'optimization_tips': [
            'Lower constraint_weight (0.2-0.5)',
            'Can tolerate higher learning rates',
            'Exponential decay often works well'
        ]
    },
    
    'RLLD': {
        'suggested_lr_range': (8e-5, 2e-4),
        'suggested_batch_sizes': [4, 8, 12],
        'convergence_patterns': 'Can be challenging due to wide dynamic range',
        'common_issues': ['Extreme values can dominate loss', 'Log-scale variations'],
        'optimization_tips': [
            'MAE loss often better than MSE',
            'Very low constraint_weight (0.1-0.3)',
            'May need longer training',
            'Consider log-scale preprocessing'
        ]
    }
}


def get_training_template(template_name: str) -> Dict[str, Any]:
    """Get training template by name"""
    if template_name not in TRAINING_TEMPLATES:
        raise ValueError(f"Training template '{template_name}' not found. Available: {list(TRAINING_TEMPLATES.keys())}")
    
    return TRAINING_TEMPLATES[template_name].copy()


def get_data_template(template_name: str) -> Dict[str, Any]:
    """Get data template by name"""
    if template_name not in DATA_TEMPLATES:
        raise ValueError(f"Data template '{template_name}' not found. Available: {list(DATA_TEMPLATES.keys())}")
    
    return DATA_TEMPLATES[template_name].copy()


def get_integrated_template(template_name: str) -> Dict[str, Any]:
    """Get integrated template combining model, training, and data configurations"""
    if template_name not in INTEGRATED_TEMPLATES:
        raise ValueError(f"Integrated template '{template_name}' not found. Available: {list(INTEGRATED_TEMPLATES.keys())}")
    
    return INTEGRATED_TEMPLATES[template_name].copy()


def create_custom_training_config(
    target_curve: str,
    base_template: Optional[str] = None,
    **overrides
) -> Dict[str, Any]:
    """
    Create custom training configuration for a specific curve
    
    Args:
        target_curve: Target curve name
        base_template: Base template to start from (optional)
        **overrides: Configuration overrides
        
    Returns:
        Custom training configuration
    """
    # Validate target curve
    if target_curve not in CURVE_CONFIGURATIONS:
        raise ValueError(f"Target curve '{target_curve}' not supported")
    
    # Get base template or create default
    if base_template:
        if base_template not in TRAINING_TEMPLATES:
            raise ValueError(f"Base template '{base_template}' not found")
        config = TRAINING_TEMPLATES[base_template].copy()
    else:
        # Create default configuration
        config = {
            'target_curve': target_curve,
            'loss_config': {'type': 'curve_specific'},
            'optimizer': 'adam',
            'learning_rate': 1e-4,
            'batch_size': 8,
            'max_epochs': 200,
            'patience': 50,
            'scheduler': 'plateau',
            'scheduler_params': {'patience': 25, 'factor': 0.5}
        }
    
    # Update target curve
    config['target_curve'] = target_curve
    
    # Apply overrides
    config.update(overrides)
    
    return config


def get_curve_training_hints(target_curve: str) -> Dict[str, Any]:
    """Get training hints and suggestions for a specific curve"""
    if target_curve not in CURVE_TRAINING_HINTS:
        return {
            'suggested_lr_range': (5e-5, 1.5e-4),
            'suggested_batch_sizes': [8, 16],
            'convergence_patterns': 'Unknown - use standard settings',
            'common_issues': [],
            'optimization_tips': ['Start with standard configuration', 'Monitor convergence carefully']
        }
    
    return CURVE_TRAINING_HINTS[target_curve].copy()


def validate_training_config(config: Dict[str, Any]) -> List[str]:
    """
    Validate training configuration and return warnings/suggestions
    
    Args:
        config: Training configuration to validate
        
    Returns:
        List of validation warnings/suggestions
    """
    warnings = []
    
    # Check required fields
    required_fields = ['target_curve', 'learning_rate', 'batch_size', 'max_epochs']
    for field in required_fields:
        if field not in config:
            warnings.append(f"Missing required field: {field}")
    
    # Validate target curve
    if 'target_curve' in config:
        target_curve = config['target_curve']
        if target_curve not in CURVE_CONFIGURATIONS:
            warnings.append(f"Unknown target curve: {target_curve}")
        else:
            # Check against curve-specific hints
            hints = get_curve_training_hints(target_curve)
            
            # Learning rate validation
            if 'learning_rate' in config:
                lr = config['learning_rate']
                lr_range = hints['suggested_lr_range']
                if not (lr_range[0] <= lr <= lr_range[1]):
                    warnings.append(
                        f"Learning rate {lr} outside suggested range {lr_range} for curve {target_curve}"
                    )
            
            # Batch size validation
            if 'batch_size' in config:
                batch_size = config['batch_size']
                suggested_sizes = hints['suggested_batch_sizes']
                if batch_size not in suggested_sizes:
                    warnings.append(
                        f"Batch size {batch_size} not in suggested sizes {suggested_sizes} for curve {target_curve}"
                    )
    
    # Validate learning rate
    if 'learning_rate' in config and config['learning_rate'] <= 0:
        warnings.append("Learning rate must be positive")
    
    # Validate batch size
    if 'batch_size' in config and config['batch_size'] <= 0:
        warnings.append("Batch size must be positive")
    
    # Validate epochs
    if 'max_epochs' in config and config['max_epochs'] <= 0:
        warnings.append("Max epochs must be positive")
    
    # Validate patience
    if 'patience' in config and config['patience'] <= 0:
        warnings.append("Patience must be positive")
    
    return warnings


def list_available_templates() -> Dict[str, List[str]]:
    """List all available templates"""
    return {
        'training_templates': list(TRAINING_TEMPLATES.keys()),
        'data_templates': list(DATA_TEMPLATES.keys()),
        'integrated_templates': list(INTEGRATED_TEMPLATES.keys())
    }


def print_template_info(template_name: str, template_type: str = 'training'):
    """Print detailed information about a template"""
    if template_type == 'training':
        if template_name not in TRAINING_TEMPLATES:
            print(f"Training template '{template_name}' not found")
            return
        template = TRAINING_TEMPLATES[template_name]
    elif template_type == 'data':
        if template_name not in DATA_TEMPLATES:
            print(f"Data template '{template_name}' not found")
            return
        template = DATA_TEMPLATES[template_name]
    elif template_type == 'integrated':
        if template_name not in INTEGRATED_TEMPLATES:
            print(f"Integrated template '{template_name}' not found")
            return
        template = INTEGRATED_TEMPLATES[template_name]
    else:
        print(f"Unknown template type: {template_type}")
        return
    
    print(f"\n=== {template_type.title()} Template: {template_name} ===")
    print(f"Description: {template.get('description', 'No description available')}")
    print("\nConfiguration:")
    for key, value in template.items():
        if key != 'description':
            print(f"  {key}: {value}")
    print()


# Example usage and testing functions
def get_example_configs() -> Dict[str, Dict[str, Any]]:
    """Get example configurations for testing"""
    return {
        'basic_vp': get_training_template('vp_training'),
        'fast_density': get_training_template('fast_density_training'),
        'robust_neutron': create_custom_training_config(
            'CNL',
            base_template='neutron_training',
            learning_rate=6e-5,
            batch_size=10
        )
    }