CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-22 13:35:14,883 - INFO - � Enhanced Training Pipeline Initialized
2025-08-22 13:35:14,884 - INFO - � Device: cuda:0
2025-08-22 13:35:14,885 - INFO - � Results directory: enhanced_training_outputs
2025-08-22 13:35:14,885 - INFO - � Starting Enhanced Multi-Curve Training Pipeline
2025-08-22 13:35:14,886 - INFO - � Loading and validating training data...
2025-08-22 13:35:14,888 - INFO -    ⚠️  Real training data not available, using synthetic data
2025-08-22 13:35:14,896 - INFO -    ✅ Generated synthetic training data
2025-08-22 13:35:14,897 - INFO - ✅ Loaded data for curves: ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:14,898 - INFO -    GR: 32000/32000 valid (100.0%)
2025-08-22 13:35:14,899 - INFO -    CNL: 32000/32000 valid (100.0%)
2025-08-22 13:35:14,903 - INFO -    DEN: 32000/32000 valid (100.0%)
2025-08-22 13:35:14,904 - INFO -    AC: 32000/32000 valid (100.0%)
2025-08-22 13:35:14,907 - INFO -    RLLD: 32000/32000 valid (100.0%)
2025-08-22 13:35:14,915 - INFO - � Creating 5-fold cross-validation splits...
2025-08-22 13:35:14,920 - INFO -    Fold 1: 40 train, 10 val samples
2025-08-22 13:35:14,925 - INFO -    Fold 2: 40 train, 10 val samples
2025-08-22 13:35:14,926 - INFO -    Fold 3: 40 train, 10 val samples
2025-08-22 13:35:14,928 - INFO -    Fold 4: 40 train, 10 val samples
2025-08-22 13:35:14,933 - INFO -    Fold 5: 40 train, 10 val samples
2025-08-22 13:35:14,933 - INFO - � Training targets: ['AC', 'DEN', 'CNL', 'GR', 'RLLD']
2025-08-22 13:35:14,935 - INFO -
================================================================================
2025-08-22 13:35:14,937 - INFO - � TRAINING AC PREDICTION MODELS
2025-08-22 13:35:14,939 - INFO - ================================================================================
2025-08-22 13:35:14,941 - INFO - � Training model for AC (Fold 1)
2025-08-22 13:35:14,943 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 13:35:14,946 - ERROR -    ❌ Training failed for AC (Fold 1): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:14,963 - INFO - � Training model for AC (Fold 2)
2025-08-22 13:35:14,965 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 13:35:14,967 - ERROR -    ❌ Training failed for AC (Fold 2): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:14,980 - INFO - � Training model for AC (Fold 3)
2025-08-22 13:35:14,981 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 13:35:14,982 - ERROR -    ❌ Training failed for AC (Fold 3): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:14,995 - INFO - � Training model for AC (Fold 4)
2025-08-22 13:35:14,996 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 13:35:14,997 - ERROR -    ❌ Training failed for AC (Fold 4): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,016 - INFO - � Training model for AC (Fold 5)
2025-08-22 13:35:15,016 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-22 13:35:15,020 - ERROR -    ❌ Training failed for AC (Fold 5): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,031 - INFO - � Calculating confidence metrics for AC...
2025-08-22 13:35:15,033 - WARNING -    ⚠️  No valid R² scores for AC
2025-08-22 13:35:15,034 - INFO -
================================================================================
2025-08-22 13:35:15,035 - INFO - � TRAINING DEN PREDICTION MODELS
2025-08-22 13:35:15,036 - INFO - ================================================================================
2025-08-22 13:35:15,037 - INFO - � Training model for DEN (Fold 1)
2025-08-22 13:35:15,038 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 13:35:15,039 - ERROR -    ❌ Training failed for DEN (Fold 1): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,057 - INFO - � Training model for DEN (Fold 2)
2025-08-22 13:35:15,057 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 13:35:15,058 - ERROR -    ❌ Training failed for DEN (Fold 2): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,071 - INFO - � Training model for DEN (Fold 3)
2025-08-22 13:35:15,072 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 13:35:15,073 - ERROR -    ❌ Training failed for DEN (Fold 3): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,085 - INFO - � Training model for DEN (Fold 4)
2025-08-22 13:35:15,086 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 13:35:15,088 - ERROR -    ❌ Training failed for DEN (Fold 4): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,108 - INFO - � Training model for DEN (Fold 5)
2025-08-22 13:35:15,119 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-22 13:35:15,122 - ERROR -    ❌ Training failed for DEN (Fold 5): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,139 - INFO - � Calculating confidence metrics for DEN...
2025-08-22 13:35:15,140 - WARNING -    ⚠️  No valid R² scores for DEN
2025-08-22 13:35:15,141 - INFO -
================================================================================
2025-08-22 13:35:15,142 - INFO - � TRAINING CNL PREDICTION MODELS
2025-08-22 13:35:15,142 - INFO - ================================================================================
2025-08-22 13:35:15,144 - INFO - � Training model for CNL (Fold 1)
2025-08-22 13:35:15,145 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,146 - ERROR -    ❌ Training failed for CNL (Fold 1): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,167 - INFO - � Training model for CNL (Fold 2)
2025-08-22 13:35:15,168 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,169 - ERROR -    ❌ Training failed for CNL (Fold 2): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,182 - INFO - � Training model for CNL (Fold 3)
2025-08-22 13:35:15,183 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,184 - ERROR -    ❌ Training failed for CNL (Fold 3): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,195 - INFO - � Training model for CNL (Fold 4)
2025-08-22 13:35:15,197 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,198 - ERROR -    ❌ Training failed for CNL (Fold 4): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,218 - INFO - � Training model for CNL (Fold 5)
2025-08-22 13:35:15,219 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,220 - ERROR -    ❌ Training failed for CNL (Fold 5): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,233 - INFO - � Calculating confidence metrics for CNL...
2025-08-22 13:35:15,234 - WARNING -    ⚠️  No valid R² scores for CNL
2025-08-22 13:35:15,235 - INFO -
================================================================================
2025-08-22 13:35:15,237 - INFO - � TRAINING GR PREDICTION MODELS
2025-08-22 13:35:15,239 - INFO - ================================================================================
2025-08-22 13:35:15,240 - INFO - � Training model for GR (Fold 1)
2025-08-22 13:35:15,241 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,242 - ERROR -    ❌ Training failed for GR (Fold 1): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,265 - INFO - � Training model for GR (Fold 2)
2025-08-22 13:35:15,266 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,269 - ERROR -    ❌ Training failed for GR (Fold 2): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,280 - INFO - � Training model for GR (Fold 3)
2025-08-22 13:35:15,281 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,282 - ERROR -    ❌ Training failed for GR (Fold 3): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,291 - INFO - � Training model for GR (Fold 4)
2025-08-22 13:35:15,292 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,295 - ERROR -    ❌ Training failed for GR (Fold 4): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,314 - INFO - � Training model for GR (Fold 5)
2025-08-22 13:35:15,314 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-22 13:35:15,315 - ERROR -    ❌ Training failed for GR (Fold 5): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,324 - INFO - � Calculating confidence metrics for GR...
2025-08-22 13:35:15,325 - WARNING -    ⚠️  No valid R² scores for GR
2025-08-22 13:35:15,325 - INFO -
================================================================================
2025-08-22 13:35:15,326 - INFO - � TRAINING RLLD PREDICTION MODELS
2025-08-22 13:35:15,327 - INFO - ================================================================================
2025-08-22 13:35:15,329 - INFO - � Training model for RLLD (Fold 1)
2025-08-22 13:35:15,330 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 13:35:15,330 - ERROR -    ❌ Training failed for RLLD (Fold 1): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,343 - INFO - � Training model for RLLD (Fold 2)
2025-08-22 13:35:15,344 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 13:35:15,346 - ERROR -    ❌ Training failed for RLLD (Fold 2): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,356 - INFO - � Training model for RLLD (Fold 3)
2025-08-22 13:35:15,357 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 13:35:15,359 - ERROR -    ❌ Training failed for RLLD (Fold 3): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,392 - INFO - � Training model for RLLD (Fold 4)
2025-08-22 13:35:15,395 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 13:35:15,397 - ERROR -    ❌ Training failed for RLLD (Fold 4): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,413 - INFO - � Training model for RLLD (Fold 5)
2025-08-22 13:35:15,415 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-22 13:35:15,418 - ERROR -    ❌ Training failed for RLLD (Fold 5): Data length (40) is less than required sequence length (720)
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\trainin
g\enhanced_multi_curve_training.py", line 190, in train_single_model
    train_dataset = create_general_dataset(
                    ^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 544, in create_general_dataset
    return GeneralWellLogDataset(
           ^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 83, in __init__
    self._prepare_data()
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core
\dataset.py", line 134, in _prepare_data
    raise ValueError(f"Data length ({min_length}) is less than required sequence length ({self.total_seqlen})")
ValueError: Data length (40) is less than required sequence length (720)
2025-08-22 13:35:15,450 - INFO - � Calculating confidence metrics for RLLD...
2025-08-22 13:35:15,453 - WARNING -    ⚠️  No valid R² scores for RLLD
2025-08-22 13:35:15,455 - INFO - � Saving comprehensive results...
2025-08-22 13:35:15,464 - INFO -    ✅ Results saved to: enhanced_training_outputs\enhanced_training_results.json
2025-08-22 13:35:15,465 - INFO - � Generating summary report...
2025-08-22 13:35:15,473 - INFO -    ✅ Summary report saved to: enhanced_training_outputs\training_summary_report.md
2025-08-22 13:35:15,474 - INFO -
� Enhanced Training Pipeline Complete!
2025-08-22 13:35:15,475 - INFO - � Results saved in: enhanced_training_outputs