"""
Quick validation of remaining Phase 2 components
"""

import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_training_manager():
    """Test GeneralTrainingManager functionality"""
    logger.info("=== Testing Training Manager ===")
    
    try:
        import torch
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDataNormalizer, 
            GeneralWellLogDataset, GeneralTrainingManager,
            get_model_template, get_training_template, get_data_template
        )
        
        # Create minimal setup
        model_config = get_model_template('vp_prediction')
        training_config = get_training_template('fast_vp_training')
        training_config['max_epochs'] = 1
        training_config['batch_size'] = 2
        
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        
        # Create dummy dataset (larger to meet sequence requirements)
        data_dict = {
            'GR': torch.randn(2000),  # Increased to meet sequence length requirements
            'CNL': torch.randn(2000),
            'DEN': torch.randn(2000),
            'RLLD': torch.randn(2000),
            'VP': torch.randn(2000)
        }
        
        normalizer = GeneralDataNormalizer(['GR', 'CNL', 'DEN', 'RLLD'], ['VP'])
        dataset = GeneralWellLogDataset(
            data_dict=data_dict,
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            target_curve='VP',
            normalizer=normalizer,
            sequence_config=get_data_template('short_sequence')
        )
        
        # Create trainer
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=dataset,
            val_dataset=None,
            training_config=training_config
        )
        
        # Test trainer methods
        info = trainer.get_training_info()
        assert 'target_curve' in info
        assert 'dataset_info' in info
        
        logger.info("✓ Training Manager created and methods work")
        return True
        
    except Exception as e:
        logger.error(f"✗ Training Manager test failed: {e}")
        return False

def test_backward_compatibility():
    """Test backward compatibility features"""
    logger.info("=== Testing Backward Compatibility ===")
    
    try:
        from vp_predictor.core import (
            VpCompatibleNormalizer, VpLossCompatible, VpCompatibleDecoder,
            VpDatasetCompatible
        )
        
        # Test normalizer compatibility
        normalizer = VpCompatibleNormalizer()
        assert normalizer.input_curves == ['GR', 'CNL', 'DEN', 'RLLD']
        assert normalizer.output_curves == ['VP']
        
        # Test loss compatibility
        loss_fn = VpLossCompatible()
        assert loss_fn.target_curve == 'AC'  # VP maps to AC internally
        
        logger.info("✓ Backward compatibility components work")
        return True
        
    except Exception as e:
        logger.error(f"✗ Backward compatibility test failed: {e}")
        return False

def test_different_targets():
    """Test training with different target curves"""
    logger.info("=== Testing Different Target Curves ===")
    
    try:
        from vp_predictor import (
            GeneralWellLogLoss, CurveSpecificLossFactory,
            get_training_template
        )
        
        # Test different curve loss functions
        curves_to_test = ['AC', 'DEN', 'CNL', 'GR', 'RLLD']
        
        for curve in curves_to_test:
            # Test direct loss creation
            loss_fn = GeneralWellLogLoss(target_curve=curve)
            assert loss_fn.target_curve == curve
            
            # Test factory creation
            factory_loss = CurveSpecificLossFactory.create_loss(curve)
            assert factory_loss.target_curve == curve
            
        logger.info(f"✓ Loss functions created for {len(curves_to_test)} different curves")
        
        # Test training templates for different curves
        template_names = ['vp_training', 'density_training', 'neutron_training']
        for template_name in template_names:
            template = get_training_template(template_name)
            assert 'target_curve' in template
            
        logger.info("✓ Training templates work for different curves")
        return True
        
    except Exception as e:
        logger.error(f"✗ Different target curves test failed: {e}")
        return False

def main():
    """Run quick validation tests"""
    logger.info("🚀 Starting Quick Phase 2 Validation")
    logger.info("=" * 50)
    
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    tests = [
        ("Training Manager", test_training_manager),
        ("Backward Compatibility", test_backward_compatibility), 
        ("Different Target Curves", test_different_targets)
    ]
    
    results = {}
    for test_name, test_func in tests:
        logger.info(f"\n📋 Running {test_name}...")
        try:
            results[test_name] = test_func()
            if results[test_name]:
                logger.info(f"✅ {test_name} PASSED")
            else:
                logger.info(f"❌ {test_name} FAILED")
        except Exception as e:
            logger.error(f"❌ {test_name} CRASHED: {e}")
            results[test_name] = False
    
    # Summary
    logger.info(f"\n{'='*50}")
    logger.info("📊 VALIDATION SUMMARY")
    logger.info(f"{'='*50}")
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{test_name:<25} {status}")
    
    logger.info(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\nAll Phase 2 validation tests PASSED!")
        return True
    else:
        print(f"\n WARNING: {total - passed}/{total} tests FAILED")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)