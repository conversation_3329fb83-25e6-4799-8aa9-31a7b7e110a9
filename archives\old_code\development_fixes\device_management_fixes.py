"""
Advanced PyTorch Device Management Fixes for demo_curve_prediction.py

This module provides modern PyTorch patterns for device management,
addressing the tensor/weight device mismatch errors and optimizing
memory usage with advanced techniques.
"""

import torch
import torch.nn as nn
from contextlib import contextmanager
from typing import Optional, Union, Dict, Any
import logging

logger = logging.getLogger(__name__)


class DeviceManager:
    """Advanced device management with automatic fallback and memory optimization"""
    
    def __init__(self, preferred_device: Optional[Union[str, torch.device]] = None):
        self.preferred_device = preferred_device
        self.device = self._select_optimal_device()
        self.memory_stats = {}
        
    def _select_optimal_device(self) -> torch.device:
        """Intelligent device selection with memory considerations"""
        if self.preferred_device:
            return torch.device(self.preferred_device)
            
        if torch.cuda.is_available():
            # Select GPU with most available memory
            best_gpu = 0
            max_memory = 0
            
            for i in range(torch.cuda.device_count()):
                with torch.cuda.device(i):
                    memory_free = torch.cuda.get_device_properties(i).total_memory
                    memory_used = torch.cuda.memory_allocated(i)
                    available = memory_free - memory_used
                    
                    if available > max_memory:
                        max_memory = available
                        best_gpu = i
                        
            device = torch.device(f'cuda:{best_gpu}')
            logger.info(f"Selected GPU {best_gpu} with {max_memory/1024**3:.1f}GB available")
            return device
        else:
            logger.info("CUDA not available, using CPU")
            return torch.device('cpu')
    
    @contextmanager
    def device_context(self):
        """Context manager for device operations with cleanup"""
        if self.device.type == 'cuda':
            with torch.cuda.device(self.device):
                try:
                    yield self.device
                finally:
                    torch.cuda.empty_cache()
        else:
            yield self.device
    
    def move_to_device(self, obj, non_blocking: bool = True):
        """Safe device movement with verification"""
        if isinstance(obj, torch.Tensor):
            return obj.to(self.device, non_blocking=non_blocking)
        elif isinstance(obj, nn.Module):
            return self._move_model_to_device(obj, non_blocking)
        elif isinstance(obj, dict):
            return {k: self.move_to_device(v, non_blocking) for k, v in obj.items()}
        elif isinstance(obj, (list, tuple)):
            return type(obj)(self.move_to_device(item, non_blocking) for item in obj)
        else:
            return obj
    
    def _move_model_to_device(self, model: nn.Module, non_blocking: bool = True) -> nn.Module:
        """Advanced model device movement with verification"""
        with self.device_context():
            # Move model to device
            model = model.to(self.device, non_blocking=non_blocking)
            
            # Verify all parameters are on correct device
            self._verify_model_device(model)
            
            # Apply device-specific optimizations
            if self.device.type == 'cuda':
                model = self._apply_cuda_optimizations(model)
                
            return model
    
    def _verify_model_device(self, model: nn.Module):
        """Verify all model parameters are on correct device"""
        misplaced_params = []
        
        for name, param in model.named_parameters():
            if param.device != self.device:
                misplaced_params.append((name, param.device))
        
        for name, buffer in model.named_buffers():
            if buffer.device != self.device:
                misplaced_params.append((name, buffer.device))
                
        if misplaced_params:
            logger.warning(f"Found {len(misplaced_params)} parameters on wrong device:")
            for name, device in misplaced_params[:5]:  # Show first 5
                logger.warning(f"  {name}: {device} (expected {self.device})")
            
            # Force move misplaced parameters
            self._force_move_parameters(model)
    
    def _force_move_parameters(self, model: nn.Module):
        """Force move all parameters to correct device"""
        with torch.no_grad():
            for param in model.parameters():
                param.data = param.data.to(self.device)
            for buffer in model.buffers():
                buffer.data = buffer.data.to(self.device)
    
    def _apply_cuda_optimizations(self, model: nn.Module) -> nn.Module:
        """Apply CUDA-specific optimizations"""
        try:
            # Enable cuDNN optimizations
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
            
            # Try to compile model for newer PyTorch versions
            if hasattr(torch, 'compile'):
                try:
                    model = torch.compile(model, mode='default')
                    logger.info("Applied torch.compile optimization")
                except Exception as e:
                    logger.debug(f"torch.compile failed: {e}")
                    
        except Exception as e:
            logger.debug(f"CUDA optimizations failed: {e}")
            
        return model


class MemoryOptimizedDataLoader:
    """Memory-optimized data loading with automatic batch size adjustment"""
    
    def __init__(self, dataset, device_manager: DeviceManager, 
                 initial_batch_size: int = 8, max_memory_fraction: float = 0.8):
        self.dataset = dataset
        self.device_manager = device_manager
        self.max_memory_fraction = max_memory_fraction
        self.optimal_batch_size = self._find_optimal_batch_size(initial_batch_size)
        
    def _find_optimal_batch_size(self, initial_batch_size: int) -> int:
        """Automatically find optimal batch size based on available memory"""
        if self.device_manager.device.type != 'cuda':
            return initial_batch_size
            
        batch_size = initial_batch_size
        max_batch_size = initial_batch_size * 4
        
        with self.device_manager.device_context():
            torch.cuda.empty_cache()
            
            while batch_size <= max_batch_size:
                try:
                    # Test with dummy batch
                    loader = torch.utils.data.DataLoader(
                        self.dataset, batch_size=batch_size, shuffle=False
                    )
                    
                    batch_inputs, batch_targets = next(iter(loader))
                    batch_inputs = self.device_manager.move_to_device(batch_inputs)
                    batch_targets = self.device_manager.move_to_device(batch_targets)
                    
                    # Check memory usage
                    memory_used = torch.cuda.memory_allocated()
                    memory_total = torch.cuda.get_device_properties(0).total_memory
                    memory_fraction = memory_used / memory_total
                    
                    if memory_fraction > self.max_memory_fraction:
                        batch_size = max(1, batch_size // 2)
                        break
                        
                    batch_size *= 2
                    
                except RuntimeError as e:
                    if "out of memory" in str(e):
                        batch_size = max(1, batch_size // 2)
                        break
                    else:
                        raise e
                finally:
                    torch.cuda.empty_cache()
        
        logger.info(f"Optimal batch size determined: {batch_size}")
        return batch_size
    
    def get_dataloader(self, shuffle: bool = True, num_workers: int = 0):
        """Get optimized DataLoader"""
        return torch.utils.data.DataLoader(
            self.dataset,
            batch_size=self.optimal_batch_size,
            shuffle=shuffle,
            num_workers=num_workers,
            pin_memory=(self.device_manager.device.type == 'cuda'),
            persistent_workers=(num_workers > 0)
        )


class AdaptiveSequenceConfig:
    """Adaptive sequence configuration to prevent small dataset issues"""
    
    @staticmethod
    def optimize_sequence_config(data_size: int, min_samples: int = 20, 
                               target_sequence_length: int = 640) -> Dict[str, int]:
        """
        Optimize sequence configuration based on data size
        
        Args:
            data_size: Total number of data points
            min_samples: Minimum number of samples required
            target_sequence_length: Desired sequence length
            
        Returns:
            Optimized sequence configuration
        """
        if data_size < target_sequence_length:
            # Data too small for target sequence length
            sequence_length = max(64, data_size // 4)
            stride = sequence_length // 4
        else:
            # Calculate stride to achieve minimum samples
            max_stride = (data_size - target_sequence_length) // (min_samples - 1)
            
            if max_stride <= 0:
                # Reduce sequence length
                sequence_length = data_size // min_samples
                stride = sequence_length // 4
            else:
                sequence_length = target_sequence_length
                stride = min(target_sequence_length // 4, max_stride)
        
        # Ensure valid configuration
        stride = max(1, stride)
        sequence_length = max(64, sequence_length)
        
        # Calculate actual samples
        actual_samples = (data_size - sequence_length) // stride + 1
        
        config = {
            'length': sequence_length,
            'stride': stride,
            'expected_samples': max(1, actual_samples)
        }
        
        logger.info(f"Adaptive sequence config: {config} for data_size={data_size}")
        return config


def create_device_aware_model(model_class, template_name: str, device_manager: DeviceManager, **kwargs):
    """Create model with proper device management"""
    with device_manager.device_context():
        # Create model
        if hasattr(model_class, 'from_template'):
            model = model_class.from_template(template_name, **kwargs)
        else:
            model = model_class(**kwargs)
        
        # Move to device with verification
        model = device_manager.move_to_device(model)
        
        # Verify model is properly on device
        device_manager._verify_model_device(model)
        
        return model


def safe_forward_pass(model: nn.Module, inputs: torch.Tensor, 
                     device_manager: DeviceManager) -> torch.Tensor:
    """Safe forward pass with device verification"""
    with device_manager.device_context():
        # Ensure inputs are on correct device
        inputs = device_manager.move_to_device(inputs)
        
        # Verify model is on correct device
        model_device = next(model.parameters()).device
        if model_device != device_manager.device:
            logger.warning(f"Model device mismatch: {model_device} vs {device_manager.device}")
            model = device_manager.move_to_device(model)
        
        # Forward pass
        return model(inputs)


# Integration with existing demo code
def fix_demo_training_simulation():
    """
    Fixed version of demo_training_simulation() with proper device management
    
    This should replace the problematic lines 502-511 in the original demo
    """
    try:
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDataNormalizer,
            GeneralWellLogDataset, GeneralTrainingManager,
            get_model_template, get_training_template
        )
        
        # Create device manager
        device_manager = DeviceManager()
        
        # Create training data with adaptive sizing
        from multiwell.demos.demo_curve_prediction import create_realistic_well_data
        train_data = create_realistic_well_data(4000)
        val_data = create_realistic_well_data(1000)
        
        # Get configurations
        model_config = get_model_template('vp_prediction')
        training_config = get_training_template('vp_training')
        training_config['max_epochs'] = 2
        
        # Optimize sequence configuration
        train_seq_config = AdaptiveSequenceConfig.optimize_sequence_config(
            data_size=4000, min_samples=25
        )
        val_seq_config = AdaptiveSequenceConfig.optimize_sequence_config(
            data_size=1000, min_samples=10
        )
        
        # Create device-aware model
        model = create_device_aware_model(
            GeneralWellLogTransformer, 'vp_prediction', device_manager
        )
        
        # Create normalizer
        normalizer = GeneralDataNormalizer(
            model_config['input_curves'], 
            model_config['output_curves']
        )
        
        # Create datasets with adaptive configuration
        train_dataset = GeneralWellLogDataset(
            data_dict=train_data,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],
            normalizer=normalizer,
            sequence_config=train_seq_config
        )
        
        val_dataset = GeneralWellLogDataset(
            data_dict=val_data,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],
            normalizer=normalizer,
            sequence_config=val_seq_config
        )
        
        logger.info(f"✓ Datasets created: train={len(train_dataset)}, val={len(val_dataset)}")
        
        # Create memory-optimized data loader
        memory_loader = MemoryOptimizedDataLoader(
            train_dataset, device_manager, 
            initial_batch_size=training_config['batch_size']
        )
        train_loader = memory_loader.get_dataloader(shuffle=True)
        
        # Safe batch processing
        batch_inputs, batch_targets = next(iter(train_loader))
        batch_inputs = device_manager.move_to_device(batch_inputs)
        batch_targets = device_manager.move_to_device(batch_targets)
        
        logger.info(f"✓ Batch shapes: inputs {batch_inputs.shape}, targets {batch_targets.shape}")
        logger.info(f"  Batch device: inputs={batch_inputs.device}, targets={batch_targets.device}")
        logger.info(f"  Model device: {next(model.parameters()).device}")
        
        # Safe forward pass
        model.eval()
        with torch.no_grad():
            predictions = safe_forward_pass(model, batch_inputs, device_manager)
            
        logger.info(f"✓ Forward pass successful: {predictions.shape}")
        logger.info(f"  Prediction device: {predictions.device}")
        logger.info(f"  Memory usage: {torch.cuda.memory_allocated()/1024**2:.1f}MB" 
                   if torch.cuda.is_available() else "CPU mode")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Fixed training simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    # Test the fixes
    success = fix_demo_training_simulation()
    print(f"Device management fixes: {'✅ SUCCESS' if success else '❌ FAILED'}")