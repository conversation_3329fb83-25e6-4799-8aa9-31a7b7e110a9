# Old VP Prediction Files Archive

## Overview

This directory contains outdated VP (Acoustic Velocity) prediction scripts that have been superseded by the new refactored `train_vp_improved.py` pipeline. These files are preserved for historical reference and potential future use.

## Organization Date
**Archived on:** August 23, 2025  
**Reason:** Directory cleanup after VP prediction pipeline refactoring

## Files Moved to Archive

### 🏋️ Old Training Scripts
These scripts provided various approaches to VP model training but are now replaced by the comprehensive `VpTrainingManager` in the new pipeline:

- **`simple_train_vp.py`** - Basic VP training implementation
- **`quick_train_vp.py`** - Fast training script for quick experiments  
- **`train_vp_model.py`** - Dedicated VP model training script
- **`train_vp_transfer.py`** - Transfer learning approach for VP prediction
- **`train.py`** - Generic training script

### 🧪 Old Test Scripts
These testing scripts are replaced by the new `test_vp_pipeline.py` and integrated validation in the pipeline:

- **`test_vp_prediction.py`** - VP prediction testing script
- **`test_vp_improved.py`** - Improved VP model testing (23KB)
- **`test.py`** - Generic test script

### 📊 Old Analysis Scripts
These analysis and plotting scripts are now integrated into the pipeline's validation and prediction managers:

- **`analyze_vp_results.py`** - VP results analysis script
- **`plot_vp_results.py`** - VP prediction plotting utilities
- **`plot_density_results.py`** - Density prediction plotting (related to VP work)

### 🔧 Old Utility Scripts
These utility scripts provided various data processing and monitoring functions now integrated into the pipeline:

- **`prepare_vp_data.py`** - Data preparation utilities
- **`monitor_vp_training.py`** - Training monitoring script
- **`create_sample_data.py`** - Sample data generation
- **`check_data_files.py`** - Data file validation utilities

### 🛠️ Old Development/Test Files
These files were used for development, testing, and debugging:

- **`test_checkpoint.pt`** - Test model checkpoint file
- **`test_device_detection.py`** - GPU/CPU device detection testing
- **`test_density_plotting.py`** - Density plotting tests
- **`run_test.bat`** - Windows batch file for running tests
- **`params_flops.txt`** - Model parameters and FLOPS information

## What Replaced These Files

The outdated scripts have been replaced by a comprehensive, well-structured VP prediction pipeline:

### 🚀 New Pipeline Architecture
- **`train_vp_improved.py`** - Complete three-stage pipeline (1,617 lines)
  - Stage 1: Training with `VpTrainingManager`
  - Stage 2: Validation with `VpValidationManager` 
  - Stage 3: Prediction with `VpPredictionManager`

### 🎯 Key Improvements
- **Modular Design**: 7 specialized classes vs monolithic scripts
- **Professional Quality**: Comprehensive error handling, logging, progress tracking
- **Organized Output**: Structured results in `vp_prediction_outputs/`
- **Configuration Management**: JSON-based configuration system
- **Integration**: Seamless integration with `vp_predictor` package
- **Testing**: Comprehensive test suite in `test_vp_pipeline.py`

## Files Kept in Main Directory

The following files remain in the main `initial/` directory as they are still actively used:

### 📋 Current Active Files
- **`train_vp_improved.py`** - New refactored VP prediction pipeline
- **`test_vp_pipeline.py`** - Comprehensive test suite for the new pipeline
- **`VP_PREDICTION_PIPELINE_README.md`** - Documentation for the new pipeline
- **`REFACTORING_SUMMARY.md`** - Summary of refactoring work completed

### 🔧 Core Dependencies (Still Used)
- **`vp_model_improved.py`** - Enhanced VP model architectures
- **`utils.py`** - Core utility functions
- **`las_processor.py`** - LAS file processing utilities
- **`model.py`** - Base model components
- **`dataset.py`** - Dataset utilities

### 📁 Output Directory
- **`vp_prediction_outputs/`** - Organized output structure for new pipeline
  - `training/` - Training results and models
  - `validation/` - Validation metrics and plots  
  - `prediction/` - Prediction results and visualizations

### 📖 Documentation Files
- **`VP_PREDICTION_GUIDE.md`** - General VP prediction guide
- **`VP_TRAINING_GUIDE.md`** - VP training guide
- **`GPU_CPU_USAGE_GUIDE.md`** - Hardware usage guide

### 📄 Data Files
- **`A1_converted.las`** - Converted LAS data file
- **`test_well.las`** - Test well data file

## Usage Notes

### 🔍 Accessing Old Files
If you need to reference or use any of the old scripts:

```bash
# Navigate to the archive
cd initial/old_files/

# List all archived files
ls -la

# Run an old script (if needed)
python simple_train_vp.py
```

### ⚠️ Important Considerations
1. **Compatibility**: Old scripts may not work with current dependencies
2. **Data Paths**: File paths in old scripts may need updating
3. **Dependencies**: Some scripts may require different package versions
4. **Functionality**: Old scripts lack the robustness of the new pipeline

### 🚀 Recommended Approach
Instead of using old scripts, use the new pipeline which provides:
- All functionality of the old scripts combined
- Better error handling and logging
- Professional-grade output and reporting
- Comprehensive testing and validation
- Modern software engineering practices

## Migration Guide

If you were using any of the old scripts, here's how to migrate to the new pipeline:

### Old → New Mapping
- `simple_train_vp.py` → `python train_vp_improved.py --stage training`
- `test_vp_prediction.py` → `python train_vp_improved.py --stage prediction`
- `analyze_vp_results.py` → Built into validation and prediction stages
- `monitor_vp_training.py` → Built-in progress tracking and logging

### Complete Workflow
```bash
# Instead of running multiple old scripts, run the complete pipeline:
python train_vp_improved.py

# Or run individual stages:
python train_vp_improved.py --stage training
python train_vp_improved.py --stage validation --model-path vp_prediction_outputs/training/best_vp_model.pth
python train_vp_improved.py --stage prediction --model-path vp_prediction_outputs/training/best_vp_model.pth
```

## Archive Maintenance

This archive should be maintained as follows:
- **Keep for reference**: These files provide historical context
- **Don't modify**: Preserve original functionality for reference
- **Document changes**: If files are added/removed, update this README
- **Periodic review**: Consider removing very old files after extended periods

---

**Note**: This archive represents the evolution of the VP prediction system from individual scripts to a comprehensive, professional pipeline. The new system provides all the functionality of these old scripts with significant improvements in organization, reliability, and usability.
