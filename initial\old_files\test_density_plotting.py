#!/usr/bin/env python3
"""
Test script to verify density plotting functionality
Creates sample data if needed and tests the plotting system
"""
import os
import sys
import numpy as np
import h5py
from datetime import datetime

def create_sample_density_results(results_dir="../result_base1/pred_val"):
    """
    Create sample density prediction results for testing plotting
    """
    if not os.path.exists(results_dir):
        os.makedirs(results_dir)
    
    print(f"📁 Creating sample density results in {results_dir}")
    
    # Generate sample data for 3 test files
    sample_files = ['test_well_001.hdf5', 'test_well_002.hdf5', 'test_well_003.hdf5']
    
    for i, filename in enumerate(sample_files):
        file_path = os.path.join(results_dir, filename)
        
        # Generate realistic density data
        n_samples = 640  # Standard sequence length
        
        # Create realistic density values (typical range: 2.0 - 2.8 g/cm³)
        np.random.seed(42 + i)  # For reproducible results
        
        # Base density trend with some geological variation
        depth_trend = np.linspace(2.1, 2.6, n_samples)
        geological_variation = 0.1 * np.sin(np.linspace(0, 4*np.pi, n_samples))
        noise = 0.02 * np.random.randn(n_samples)
        
        real_density = depth_trend + geological_variation + noise
        real_density = np.clip(real_density, 2.0, 2.8)  # Realistic bounds
        
        # Create predicted density with some error
        prediction_error = 0.05 * np.random.randn(n_samples)
        pred_density = real_density + prediction_error
        pred_density = np.clip(pred_density, 2.0, 2.8)
        
        # Add some systematic bias for variety
        if i == 1:  # Second file has slight overestimation
            pred_density += 0.02
        elif i == 2:  # Third file has slight underestimation
            pred_density -= 0.015
        
        # Save to HDF5 file
        with h5py.File(file_path, 'w') as f:
            f.create_dataset('real', data=real_density.reshape(1, 1, -1))
            f.create_dataset('pred', data=pred_density.reshape(1, 1, -1))
            
            # Add metadata
            f.attrs['created'] = datetime.now().isoformat()
            f.attrs['sample_data'] = True
            f.attrs['description'] = f'Sample density prediction data for testing - Well {i+1:03d}'
        
        print(f"  ✅ Created {filename}")
        print(f"     Real range: [{real_density.min():.3f}, {real_density.max():.3f}] g/cm³")
        print(f"     Pred range: [{pred_density.min():.3f}, {pred_density.max():.3f}] g/cm³")
    
    print(f"✅ Sample data creation completed!")
    return len(sample_files)

def test_plotting_functionality():
    """
    Test the density plotting functionality
    """
    print("🧪 Testing Density Plotting Functionality")
    print("=" * 50)
    
    # Check if plotting script exists
    plot_script = "plot_density_results.py"
    if not os.path.exists(plot_script):
        print(f"❌ Plotting script {plot_script} not found!")
        return False
    
    # Check if results directory exists
    results_dir = "../result_base1/pred_val"
    if not os.path.exists(results_dir):
        print(f"📁 Results directory {results_dir} not found. Creating sample data...")
        create_sample_density_results(results_dir)
    else:
        # Check if there are any result files
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.hdf5')]
        if not result_files:
            print(f"📁 No result files found in {results_dir}. Creating sample data...")
            create_sample_density_results(results_dir)
        else:
            print(f"✅ Found {len(result_files)} existing result files")
    
    # Test the plotting functionality
    print("\n🎨 Testing plotting functionality...")
    try:
        from plot_density_results import create_density_comparison_plots
        
        plot_file = create_density_comparison_plots(results_dir)
        
        if plot_file and os.path.exists(plot_file):
            print(f"✅ Plotting test successful!")
            print(f"📊 Plot created: {plot_file}")
            return True
        else:
            print(f"❌ Plotting test failed - no plot file created")
            return False
            
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Plotting error: {e}")
        return False

def main():
    """
    Main test function
    """
    print("🔍 Density Plotting Test Suite")
    print("=" * 40)
    
    success = test_plotting_functionality()
    
    print("\n" + "=" * 40)
    if success:
        print("🎉 All tests passed!")
        print("✅ Density plotting functionality is working correctly")
        print("\n📋 Next steps:")
        print("  1. Run option 1 in run_test.bat to see the enhanced functionality")
        print("  2. The plotting will automatically run after density prediction")
        print("  3. Check ../density_prediction_results/ for visualization outputs")
    else:
        print("❌ Tests failed!")
        print("⚠️  Please check the error messages above")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
