/Users/<USER>/imputeML/Scripts/Activate.ps1
(imputeML) PS C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transf
ormer> & C:/Users/<USER>/imputeML/Scripts/python.exe "c:/Users/<USER>/Documents/OneDrive - PT Pertam
ina (Persero)/13_Python_PKB/4_GIT/MWLT/Init_transformer/multiwell/validation/model_validation_suite.py"
CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-25 17:12:53,016 - INFO - � Model Validator Initialized
2025-08-25 17:12:53,017 - INFO - � Device: cuda:0 (consistent with training)
2025-08-25 17:12:53,018 - INFO - � Starting Comprehensive Model Validation
2025-08-25 17:12:53,018 - INFO - � Loading data...
2025-08-25 17:12:53,019 - INFO -    � Loading same HDF5 data as training...
2025-08-25 17:12:53,020 - INFO -    ✅ Found data files in: C:\Users\<USER>\Documents\OneDrive - PT Pertamina
(Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer
2025-08-25 17:12:53,021 - INFO -    � Processing A1.hdf5...
Processing A1.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5120,), range [165.99, 357.24]
  CNL: (5120,), range [4.37, 35.62]
  DEN: (5120,), range [2.00, 2.77]
  DEPTH: (5120,), range [1805.75, 2450.00]
  GR: (5120,), range [20.31, 149.43]
  RLLD: (5120,), range [0.84, 3.83]
2025-08-25 17:12:53,027 - INFO -    � Creating 13 windows from 5120 samples
2025-08-25 17:12:53,032 - INFO -    � Processing A2.hdf5...
Processing A2.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5194,), range [151.63, 357.28]
  CNL: (5194,), range [0.11, 55.20]
  DEN: (5194,), range [2.00, 2.86]
  DEPTH: (5194,), range [2653.38, 3313.88]
  GR: (5194,), range [22.42, 194.46]
  RLLD: (5194,), range [0.70, 3.84]
2025-08-25 17:12:53,038 - INFO -    � Creating 13 windows from 5194 samples
2025-08-25 17:12:53,051 - INFO -    ✅ Loaded real data with 18720 total samples
2025-08-25 17:12:53,052 - INFO -    ✅ Loaded same HDF5 data as training
2025-08-25 17:12:53,052 - INFO - � Loading trained models...
2025-08-25 17:12:53,053 - INFO - Checking for models in: enhanced_training_outputs
2025-08-25 17:12:53,057 - INFO -    ✅ Found AC fold 0 model
2025-08-25 17:12:53,059 - INFO -    ✅ Found AC fold 1 model
2025-08-25 17:12:53,061 - INFO -    ✅ Found AC fold 2 model
2025-08-25 17:12:53,068 - INFO -    ✅ Found AC fold 3 model
2025-08-25 17:12:53,070 - INFO -    ✅ Found AC fold 4 model
2025-08-25 17:12:53,072 - INFO -    ✅ Found CNL fold 0 model
2025-08-25 17:12:53,073 - INFO -    ✅ Found CNL fold 1 model
2025-08-25 17:12:53,074 - INFO -    ✅ Found CNL fold 2 model
2025-08-25 17:12:53,075 - INFO -    ✅ Found CNL fold 3 model
2025-08-25 17:12:53,077 - INFO -    ✅ Found CNL fold 4 model
2025-08-25 17:12:53,078 - INFO -    ✅ Found DEN fold 0 model
2025-08-25 17:12:53,079 - INFO -    ✅ Found DEN fold 1 model
2025-08-25 17:12:53,082 - INFO -    ✅ Found DEN fold 2 model
2025-08-25 17:12:53,083 - INFO -    ✅ Found DEN fold 3 model
2025-08-25 17:12:53,084 - INFO -    ✅ Found DEN fold 4 model
2025-08-25 17:12:53,086 - INFO -    ✅ Found GR fold 0 model
2025-08-25 17:12:53,087 - INFO -    ✅ Found GR fold 1 model
2025-08-25 17:12:53,090 - INFO -    ✅ Found GR fold 2 model
2025-08-25 17:12:53,092 - INFO -    ✅ Found GR fold 3 model
2025-08-25 17:12:53,093 - INFO -    ✅ Found GR fold 4 model
2025-08-25 17:12:53,094 - INFO -    ✅ Found RLLD fold 0 model
2025-08-25 17:12:53,096 - INFO -    ✅ Found RLLD fold 1 model
2025-08-25 17:12:53,097 - INFO -    ✅ Found RLLD fold 2 model
2025-08-25 17:12:53,099 - INFO -    ✅ Found RLLD fold 3 model
2025-08-25 17:12:53,100 - INFO -    ✅ Found RLLD fold 4 model
2025-08-25 17:12:53,101 - INFO - Checking for models in: enhanced_training_outputs
2025-08-25 17:12:53,104 - INFO -    ✅ Found AC fold 0 model
2025-08-25 17:12:53,105 - INFO -    ✅ Found AC fold 1 model
2025-08-25 17:12:53,107 - INFO -    ✅ Found AC fold 2 model
2025-08-25 17:12:53,108 - INFO -    ✅ Found AC fold 3 model
2025-08-25 17:12:53,109 - INFO -    ✅ Found AC fold 4 model
2025-08-25 17:12:53,111 - INFO -    ✅ Found CNL fold 0 model
2025-08-25 17:12:53,113 - INFO -    ✅ Found CNL fold 1 model
2025-08-25 17:12:53,116 - INFO -    ✅ Found CNL fold 2 model
2025-08-25 17:12:53,117 - INFO -    ✅ Found CNL fold 3 model
2025-08-25 17:12:53,119 - INFO -    ✅ Found CNL fold 4 model
2025-08-25 17:12:53,120 - INFO -    ✅ Found DEN fold 0 model
2025-08-25 17:12:53,124 - INFO -    ✅ Found DEN fold 1 model
2025-08-25 17:12:53,125 - INFO -    ✅ Found DEN fold 2 model
2025-08-25 17:12:53,126 - INFO -    ✅ Found DEN fold 3 model
2025-08-25 17:12:53,128 - INFO -    ✅ Found DEN fold 4 model
2025-08-25 17:12:53,129 - INFO -    ✅ Found GR fold 0 model
2025-08-25 17:12:53,130 - INFO -    ✅ Found GR fold 1 model
2025-08-25 17:12:53,132 - INFO -    ✅ Found GR fold 2 model
2025-08-25 17:12:53,132 - INFO -    ✅ Found GR fold 3 model
2025-08-25 17:12:53,134 - INFO -    ✅ Found GR fold 4 model
2025-08-25 17:12:53,136 - INFO -    ✅ Found RLLD fold 0 model
2025-08-25 17:12:53,137 - INFO -    ✅ Found RLLD fold 1 model
2025-08-25 17:12:53,138 - INFO -    ✅ Found RLLD fold 2 model
2025-08-25 17:12:53,139 - INFO -    ✅ Found RLLD fold 3 model
2025-08-25 17:12:53,140 - INFO -    ✅ Found RLLD fold 4 model
2025-08-25 17:12:53,141 - INFO -
================================================================================
2025-08-25 17:12:53,142 - INFO - � VALIDATING AC PREDICTION MODEL
2025-08-25 17:12:53,144 - INFO - ================================================================================
2025-08-25 17:12:53,145 - INFO - � Cross-validating AC prediction (5 folds)
2025-08-25 17:12:53,146 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-25 17:12:53,148 - INFO -    � Fold 1/5
2025-08-25 17:12:53,203 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:53,443 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:58,411 - INFO -       � Physical units - R²: -1.9049, MAE: 23.76 μs/ft, RMSE: 28.76 μs/ft
2025-08-25 17:12:58,414 - INFO -       � R²: -1.9049, MAE: 0.3168, RMSE: 0.3834
2025-08-25 17:12:58,415 - INFO -    � Fold 2/5
2025-08-25 17:12:58,486 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:58,536 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:58,935 - INFO -       � Physical units - R²: -1.1903, MAE: 30.91 μs/ft, RMSE: 42.36 μs/ft
2025-08-25 17:12:58,946 - INFO -       � R²: -1.1903, MAE: 0.4122, RMSE: 0.5648
2025-08-25 17:12:58,947 - INFO -    � Fold 3/5
2025-08-25 17:12:59,003 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:59,064 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:59,492 - INFO -       � Physical units - R²: -0.7453, MAE: 34.40 μs/ft, RMSE: 48.04 μs/ft
2025-08-25 17:12:59,497 - INFO -       � R²: -0.7453, MAE: 0.4587, RMSE: 0.6406
2025-08-25 17:12:59,498 - INFO -    � Fold 4/5
2025-08-25 17:12:59,560 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:59,607 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:00,032 - INFO -       � Physical units - R²: -0.6579, MAE: 24.44 μs/ft, RMSE: 34.13 μs/ft
2025-08-25 17:13:00,038 - INFO -       � R²: -0.6579, MAE: 0.3259, RMSE: 0.4551
2025-08-25 17:13:00,040 - INFO -    � Fold 5/5
2025-08-25 17:13:00,086 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:13:00,150 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:00,585 - INFO -       � Physical units - R²: -0.3168, MAE: 18.01 μs/ft, RMSE: 25.31 μs/ft
2025-08-25 17:13:00,595 - INFO -       � R²: -0.3168, MAE: 0.2401, RMSE: 0.3374
2025-08-25 17:13:00,606 - INFO -    � Overall Performance:
2025-08-25 17:13:00,607 - INFO -       R² (normalized) = -0.9630 ± 0.2736 (95% CI: [-1.7226, -0.2034])
2025-08-25 17:13:00,609 - INFO -       MAE (normalized) = 0.3507 ± 0.0384
2025-08-25 17:13:00,612 - INFO -       RMSE (normalized) = 0.4763 ± 0.0562
2025-08-25 17:13:00,615 - INFO -       R² (physical) = -0.9630 ± 0.2736 (95% CI: [-1.7226, -0.2034])
2025-08-25 17:13:00,616 - INFO -       MAE (physical) = 26.31 ± 2.88 μs/ft
2025-08-25 17:13:00,617 - INFO -       RMSE (physical) = 35.72 ± 4.22 μs/ft
2025-08-25 17:13:00,619 - INFO - � Analyzing overfitting for AC
2025-08-25 17:13:00,621 - ERROR - ❌ Validation failed for AC: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:00,636 - INFO -
================================================================================
2025-08-25 17:13:00,645 - INFO - � VALIDATING CNL PREDICTION MODEL
2025-08-25 17:13:00,647 - INFO - ================================================================================
2025-08-25 17:13:00,648 - INFO - � Cross-validating CNL prediction (5 folds)
2025-08-25 17:13:00,649 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-25 17:13:00,651 - INFO -    � Fold 1/5
2025-08-25 17:13:00,704 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:00,751 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:01,155 - INFO -       � R²: -0.1359, MAE: 0.8676, RMSE: 1.0623
2025-08-25 17:13:01,156 - INFO -    � Fold 2/5
2025-08-25 17:13:01,217 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:01,265 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:01,681 - INFO -       � R²: -0.0780, MAE: 0.8205, RMSE: 1.0353
2025-08-25 17:13:01,682 - INFO -    � Fold 3/5
2025-08-25 17:13:01,742 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:01,791 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:02,236 - INFO -       � R²: -0.0399, MAE: 0.7991, RMSE: 0.9980
2025-08-25 17:13:02,237 - INFO -    � Fold 4/5
2025-08-25 17:13:02,285 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:02,331 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:02,771 - INFO -       � R²: -0.2742, MAE: 0.7435, RMSE: 1.0775
2025-08-25 17:13:02,772 - INFO -    � Fold 5/5
2025-08-25 17:13:02,842 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:02,897 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:03,452 - INFO -       � R²: -0.2208, MAE: 0.7651, RMSE: 0.9727
2025-08-25 17:13:03,462 - INFO -    � Overall Performance:
2025-08-25 17:13:03,462 - INFO -       R² (normalized) = -0.1498 ± 0.0436 (95% CI: [-0.2707, -0.0288])
2025-08-25 17:13:03,465 - INFO -       MAE (normalized) = 0.7992 ± 0.0217
2025-08-25 17:13:03,466 - INFO -       RMSE (normalized) = 1.0292 ± 0.0195
2025-08-25 17:13:03,479 - INFO - � Analyzing overfitting for CNL
2025-08-25 17:13:03,480 - ERROR - ❌ Validation failed for CNL: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:03,862 - INFO -
================================================================================
2025-08-25 17:13:03,864 - INFO - � VALIDATING DEN PREDICTION MODEL
2025-08-25 17:13:03,865 - INFO - ================================================================================
2025-08-25 17:13:03,869 - INFO - � Cross-validating DEN prediction (5 folds)
2025-08-25 17:13:03,871 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-25 17:13:03,874 - INFO -    � Fold 1/5
2025-08-25 17:13:03,937 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:03,989 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:04,702 - INFO -       � R²: -0.2077, MAE: 0.9052, RMSE: 1.0881
2025-08-25 17:13:04,703 - INFO -    � Fold 2/5
2025-08-25 17:13:04,757 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:04,811 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:05,540 - INFO -       � R²: -0.0203, MAE: 0.8060, RMSE: 0.9928
2025-08-25 17:13:05,541 - INFO -    � Fold 3/5
2025-08-25 17:13:05,593 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:05,643 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:06,267 - INFO -       � R²: -0.0034, MAE: 0.6859, RMSE: 0.9278
2025-08-25 17:13:06,268 - INFO -    � Fold 4/5
2025-08-25 17:13:06,328 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:06,381 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:06,977 - INFO -       � R²: -0.0365, MAE: 0.7788, RMSE: 0.9828
2025-08-25 17:13:06,978 - INFO -    � Fold 5/5
2025-08-25 17:13:07,024 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:07,078 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:07,657 - INFO -       � R²: -0.1706, MAE: 0.7877, RMSE: 1.0328
2025-08-25 17:13:07,666 - INFO -    � Overall Performance:
2025-08-25 17:13:07,667 - INFO -       R² (normalized) = -0.0877 ± 0.0422 (95% CI: [-0.2047, 0.0293])
2025-08-25 17:13:07,669 - INFO -       MAE (normalized) = 0.7927 ± 0.0350
2025-08-25 17:13:07,670 - INFO -       RMSE (normalized) = 1.0049 ± 0.0267
2025-08-25 17:13:07,683 - INFO - � Analyzing overfitting for DEN
2025-08-25 17:13:07,684 - ERROR - ❌ Validation failed for DEN: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:07,694 - INFO -
================================================================================
2025-08-25 17:13:07,695 - INFO - � VALIDATING GR PREDICTION MODEL
2025-08-25 17:13:07,696 - INFO - ================================================================================
2025-08-25 17:13:07,699 - INFO - � Cross-validating GR prediction (5 folds)
2025-08-25 17:13:07,700 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-25 17:13:07,702 - INFO -    � Fold 1/5
2025-08-25 17:13:07,760 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:07,806 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:08,410 - INFO -       � R²: -0.0007, MAE: 0.7821, RMSE: 0.9862
2025-08-25 17:13:08,411 - INFO -    � Fold 2/5
2025-08-25 17:13:08,473 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:08,523 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:09,125 - INFO -       � R²: 0.0489, MAE: 0.7285, RMSE: 0.9681
2025-08-25 17:13:09,126 - INFO -    � Fold 3/5
2025-08-25 17:13:09,166 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:09,217 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:09,800 - INFO -       � R²: -0.1890, MAE: 0.8588, RMSE: 1.0871
2025-08-25 17:13:09,801 - INFO -    � Fold 4/5
2025-08-25 17:13:09,861 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:09,913 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:10,469 - INFO -       � R²: -0.1880, MAE: 0.8456, RMSE: 1.0831
2025-08-25 17:13:10,469 - INFO -    � Fold 5/5
2025-08-25 17:13:10,510 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:10,558 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:11,123 - INFO -       � R²: -0.1618, MAE: 0.7765, RMSE: 1.0517
2025-08-25 17:13:11,132 - INFO -    � Overall Performance:
2025-08-25 17:13:11,133 - INFO -       R² (normalized) = -0.0981 ± 0.0507 (95% CI: [-0.2390, 0.0428])
2025-08-25 17:13:11,134 - INFO -       MAE (normalized) = 0.7983 ± 0.0240
2025-08-25 17:13:11,136 - INFO -       RMSE (normalized) = 1.0352 ± 0.0247
2025-08-25 17:13:11,142 - INFO - � Analyzing overfitting for GR
2025-08-25 17:13:11,143 - ERROR - ❌ Validation failed for GR: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:11,154 - INFO -
================================================================================
2025-08-25 17:13:11,155 - INFO - � VALIDATING RLLD PREDICTION MODEL
2025-08-25 17:13:11,158 - INFO - ================================================================================
2025-08-25 17:13:11,160 - INFO - � Cross-validating RLLD prediction (5 folds)
2025-08-25 17:13:11,163 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-25 17:13:11,165 - INFO -    � Fold 1/5
2025-08-25 17:13:11,209 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:11,263 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:11,819 - INFO -       � R²: -0.2082, MAE: 0.8810, RMSE: 1.0765
2025-08-25 17:13:11,820 - INFO -    � Fold 2/5
2025-08-25 17:13:11,872 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:11,921 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:12,498 - INFO -       � R²: -0.0362, MAE: 0.7612, RMSE: 1.0016
2025-08-25 17:13:12,498 - INFO -    � Fold 3/5
2025-08-25 17:13:12,540 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:12,590 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:13,154 - INFO -       � R²: -0.1253, MAE: 0.8663, RMSE: 1.0556
2025-08-25 17:13:13,156 - INFO -    � Fold 4/5
2025-08-25 17:13:13,216 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:13,265 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:13,830 - INFO -       � R²: -0.0485, MAE: 0.7980, RMSE: 1.0063
2025-08-25 17:13:13,831 - INFO -    � Fold 5/5
2025-08-25 17:13:13,892 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:13,953 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:14,524 - INFO -       � R²: -0.0960, MAE: 0.8548, RMSE: 1.0416
2025-08-25 17:13:14,528 - INFO -    � Overall Performance:
2025-08-25 17:13:14,529 - INFO -       R² (normalized) = -0.1028 ± 0.0309 (95% CI: [-0.1885, -0.0172])
2025-08-25 17:13:14,533 - INFO -       MAE (normalized) = 0.8323 ± 0.0227
2025-08-25 17:13:14,537 - INFO -       RMSE (normalized) = 1.0363 ± 0.0144
2025-08-25 17:13:14,546 - INFO - � Analyzing overfitting for RLLD
2025-08-25 17:13:14,547 - ERROR - ❌ Validation failed for RLLD: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwel
\Init_transformer\multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_val
idation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\validation\model_validati
l\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:14,580 - INFO - � Validation results saved: validation_outputs\comprehensive_validation_results.json
2025-08-25 17:13:14,589 - INFO - � Validation report saved: validation_outputs\validation_report.md
2025-08-25 17:13:14,590 - INFO - � Comprehensive Model Validation Complete!
(impu> & C:/Users/<USER>/imputeML/Scripts/python.exe "c:/Users/<USER>/Documents/OneDrive - PT Pertamina (Per
sero)/13_Python_PKB/4_GIT/MWLT/Init_transformer/multiwell/validation/model_validation_suite.py"
CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-26 13:54:07,753 - INFO - � Model Validator Initialized
2025-08-26 13:54:07,753 - INFO - � Device: cuda:0 (consistent with training)
2025-08-26 13:54:07,763 - INFO - � Starting Comprehensive Model Validation
2025-08-26 13:54:07,763 - INFO - � Loading data...
2025-08-26 13:54:07,763 - INFO -    � Loading same HDF5 data as training...
2025-08-26 13:54:07,763 - INFO -    ✅ Found data files in: C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero
)\13_Python_PKB\4_GIT\MWLT\Init_transformer
2025-08-26 13:54:07,763 - INFO -    � Processing A1.hdf5...
Processing A1.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5120,), range [165.99, 357.24]
  CNL: (5120,), range [4.37, 35.62]
  DEN: (5120,), range [2.00, 2.77]
  DEPTH: (5120,), range [1805.75, 2450.00]
  GR: (5120,), range [20.31, 149.43]
  RLLD: (5120,), range [0.84, 3.83]
2025-08-26 13:54:07,783 - INFO -    � Creating 13 windows from 5120 samples
2025-08-26 13:54:07,798 - INFO -    � Processing A2.hdf5...
Processing A2.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5194,), range [151.63, 357.28]
  CNL: (5194,), range [0.11, 55.20]
  DEN: (5194,), range [2.00, 2.86]
  DEPTH: (5194,), range [2653.38, 3313.88]
  GR: (5194,), range [22.42, 194.46]
  RLLD: (5194,), range [0.70, 3.84]
2025-08-26 13:54:07,817 - INFO -    � Creating 13 windows from 5194 samples
2025-08-26 13:54:07,840 - INFO -    ✅ Loaded real data with 18720 total samples
2025-08-26 13:54:07,850 - INFO -    ✅ Loaded same HDF5 data as training
2025-08-26 13:54:07,850 - INFO - � Loading trained models...
2025-08-26 13:54:07,854 - INFO - Checking for models in: enhanced_training_outputs
2025-08-26 13:54:07,856 - INFO -    ✅ Found AC fold 0 model
2025-08-26 13:54:07,858 - INFO -    ✅ Found AC fold 1 model
2025-08-26 13:54:07,858 - INFO -    ✅ Found AC fold 2 model
2025-08-26 13:54:07,858 - INFO -    ✅ Found AC fold 3 model
2025-08-26 13:54:07,874 - INFO -    ✅ Found AC fold 4 model
2025-08-26 13:54:07,884 - INFO -    ✅ Found CNL fold 0 model
2025-08-26 13:54:07,889 - INFO -    ✅ Found CNL fold 1 model
2025-08-26 13:54:07,890 - INFO -    ✅ Found CNL fold 2 model
2025-08-26 13:54:07,890 - INFO -    ✅ Found CNL fold 3 model
2025-08-26 13:54:07,890 - INFO -    ✅ Found CNL fold 4 model
2025-08-26 13:54:07,890 - INFO -    ✅ Found DEN fold 0 model
2025-08-26 13:54:07,900 - INFO -    ✅ Found DEN fold 1 model
2025-08-26 13:54:07,903 - INFO -    ✅ Found DEN fold 2 model
2025-08-26 13:54:07,905 - INFO -    ✅ Found DEN fold 3 model
2025-08-26 13:54:07,908 - INFO -    ✅ Found DEN fold 4 model
2025-08-26 13:54:07,910 - INFO -    ✅ Found GR fold 0 model
2025-08-26 13:54:07,911 - INFO -    ✅ Found GR fold 1 model
2025-08-26 13:54:07,913 - INFO -    ✅ Found GR fold 2 model
2025-08-26 13:54:07,915 - INFO -    ✅ Found GR fold 3 model
2025-08-26 13:54:07,916 - INFO -    ✅ Found GR fold 4 model
2025-08-26 13:54:07,917 - INFO -    ✅ Found RLLD fold 0 model
2025-08-26 13:54:07,917 - INFO -    ✅ Found RLLD fold 1 model
2025-08-26 13:54:07,917 - INFO -    ✅ Found RLLD fold 2 model
2025-08-26 13:54:07,923 - INFO -    ✅ Found RLLD fold 3 model
2025-08-26 13:54:07,924 - INFO -    ✅ Found RLLD fold 4 model
2025-08-26 13:54:07,924 - INFO - Checking for models in: enhanced_training_outputs
2025-08-26 13:54:07,924 - INFO -    ✅ Found AC fold 0 model
2025-08-26 13:54:07,934 - INFO -    ✅ Found AC fold 1 model
2025-08-26 13:54:07,934 - INFO -    ✅ Found AC fold 2 model
2025-08-26 13:54:07,934 - INFO -    ✅ Found AC fold 3 model
2025-08-26 13:54:07,938 - INFO -    ✅ Found AC fold 4 model
2025-08-26 13:54:07,940 - INFO -    ✅ Found CNL fold 0 model
2025-08-26 13:54:07,941 - INFO -    ✅ Found CNL fold 1 model
2025-08-26 13:54:07,943 - INFO -    ✅ Found CNL fold 2 model
2025-08-26 13:54:07,944 - INFO -    ✅ Found CNL fold 3 model
2025-08-26 13:54:07,946 - INFO -    ✅ Found CNL fold 4 model
2025-08-26 13:54:07,948 - INFO -    ✅ Found DEN fold 0 model
2025-08-26 13:54:07,949 - INFO -    ✅ Found DEN fold 1 model
2025-08-26 13:54:07,949 - INFO -    ✅ Found DEN fold 2 model
2025-08-26 13:54:07,949 - INFO -    ✅ Found DEN fold 3 model
2025-08-26 13:54:07,957 - INFO -    ✅ Found DEN fold 4 model
2025-08-26 13:54:07,957 - INFO -    ✅ Found GR fold 0 model
2025-08-26 13:54:07,967 - INFO -    ✅ Found GR fold 1 model
2025-08-26 13:54:07,967 - INFO -    ✅ Found GR fold 2 model
2025-08-26 13:54:07,967 - INFO -    ✅ Found GR fold 3 model
2025-08-26 13:54:07,975 - INFO -    ✅ Found GR fold 4 model
2025-08-26 13:54:07,977 - INFO -    ✅ Found RLLD fold 0 model
2025-08-26 13:54:07,978 - INFO -    ✅ Found RLLD fold 1 model
2025-08-26 13:54:07,980 - INFO -    ✅ Found RLLD fold 2 model
2025-08-26 13:54:07,981 - INFO -    ✅ Found RLLD fold 3 model
2025-08-26 13:54:07,981 - INFO -    ✅ Found RLLD fold 4 model
2025-08-26 13:54:07,981 - INFO - Checking for models in: c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\1
3_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_training_outputs
2025-08-26 13:54:07,981 - INFO -    ✅ Found AC fold 0 model
2025-08-26 13:54:07,989 - INFO -    ✅ Found AC fold 1 model
2025-08-26 13:54:07,990 - INFO -    ✅ Found AC fold 2 model
2025-08-26 13:54:07,990 - INFO -    ✅ Found AC fold 3 model
2025-08-26 13:54:07,990 - INFO -    ✅ Found AC fold 4 model
2025-08-26 13:54:08,009 - INFO -
================================================================================
2025-08-26 13:54:08,010 - INFO - � VALIDATING AC PREDICTION MODEL
2025-08-26 13:54:08,011 - INFO - ================================================================================
2025-08-26 13:54:08,012 - INFO - � Cross-validating AC prediction (5 folds)
2025-08-26 13:54:08,015 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-26 13:54:08,015 - INFO -    � Fold 1/5
2025-08-26 13:54:08,067 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 13:54:08,425 - INFO -       ✅ Loaded model for fold 0 (AC_fold_0)
2025-08-26 13:54:12,924 - INFO -       � Physical units - R²: -2094.1064, MAE: 768.21 μs/ft, RMSE: 772.28 μs/ft
2025-08-26 13:54:12,934 - INFO -       � R²: -2094.1062, MAE: 10.2428, RMSE: 10.2971
2025-08-26 13:54:12,934 - INFO -    � Fold 2/5
2025-08-26 13:54:12,989 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 13:54:13,203 - INFO -       ✅ Loaded model for fold 1 (AC_fold_1)
2025-08-26 13:54:13,600 - INFO -       � Physical units - R²: -830.0766, MAE: 816.78 μs/ft, RMSE: 825.10 μs/ft
2025-08-26 13:54:13,600 - INFO -       � R²: -830.0767, MAE: 10.8904, RMSE: 11.0013
2025-08-26 13:54:13,600 - INFO -    � Fold 3/5
2025-08-26 13:54:13,655 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 13:54:13,865 - INFO -       ✅ Loaded model for fold 2 (AC_fold_2)
2025-08-26 13:54:14,230 - INFO -       � Physical units - R²: -402.5542, MAE: 727.78 μs/ft, RMSE: 730.54 μs/ft
2025-08-26 13:54:14,237 - INFO -       � R²: -402.5542, MAE: 9.7037, RMSE: 9.7405
2025-08-26 13:54:14,239 - INFO -    � Fold 4/5
2025-08-26 13:54:14,290 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 13:54:14,495 - INFO -       ✅ Loaded model for fold 3 (AC_fold_3)
2025-08-26 13:54:14,863 - INFO -       � Physical units - R²: -852.0988, MAE: 771.85 μs/ft, RMSE: 774.30 μs/ft
2025-08-26 13:54:14,863 - INFO -       � R²: -852.0987, MAE: 10.2914, RMSE: 10.3240
2025-08-26 13:54:14,873 - INFO -    � Fold 5/5
2025-08-26 13:54:14,920 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 13:54:15,178 - INFO -       ✅ Loaded model for fold 4 (AC_fold_4)
2025-08-26 13:54:15,556 - INFO -       � Physical units - R²: -1346.5820, MAE: 802.80 μs/ft, RMSE: 809.58 μs/ft
2025-08-26 13:54:15,562 - INFO -       � R²: -1346.5820, MAE: 10.7040, RMSE: 10.7944
2025-08-26 13:54:15,563 - INFO -    � Overall Performance:
2025-08-26 13:54:15,563 - INFO -       R² (normalized) = -1105.0836 ± 288.9347 (95% CI: [-1907.2948, -302.8723])
2025-08-26 13:54:15,563 - INFO -       MAE (normalized) = 10.3664 ± 0.2060
2025-08-26 13:54:15,563 - INFO -       RMSE (normalized) = 10.4315 ± 0.2195
2025-08-26 13:54:15,563 - INFO -       R² (physical) = -1105.0836 ± 288.9347 (95% CI: [-1907.2950, -302.8722])
2025-08-26 13:54:15,563 - INFO -       MAE (physical) = 777.48 ± 15.45 μs/ft
2025-08-26 13:54:15,573 - INFO -       RMSE (physical) = 782.36 ± 16.46 μs/ft
2025-08-26 13:54:15,573 - INFO - � Analyzing overfitting for AC
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 13:54:15,596 - WARNING -    � Overfitting detected (score: 0.0%)
2025-08-26 13:54:15,598 - INFO - � Estimating prediction uncertainty for AC
2025-08-26 13:54:15,812 - INFO -    � Created 283 sequences for AC prediction
2025-08-26 13:54:15,812 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 13:54:16,032 - INFO -    � Loading fold 0 for ensemble
2025-08-26 13:54:18,537 - INFO -    � Loading fold 1 for ensemble
2025-08-26 13:54:20,695 - INFO -    � Loading fold 2 for ensemble
2025-08-26 13:54:22,819 - INFO -    � Loading fold 3 for ensemble
2025-08-26 13:54:25,002 - INFO -    � Loading fold 4 for ensemble
2025-08-26 13:54:27,578 - INFO -    � Fold ensemble uncertainty ratio: 0.0742
2025-08-26 13:54:27,578 - INFO -    ⚠️  High uncertainty samples: 28694/181120
2025-08-26 13:54:27,624 - INFO - � Creating validation visualizations...
2025-08-26 13:54:35,136 - INFO -    ✅ Validation plot saved: validation_outputs\AC_validation_results.png
2025-08-26 13:54:41,262 - INFO - ✅ Validation completed for AC
2025-08-26 13:54:41,264 - INFO -
================================================================================
2025-08-26 13:54:41,270 - INFO - � VALIDATING CNL PREDICTION MODEL
2025-08-26 13:54:41,273 - INFO - ================================================================================
2025-08-26 13:54:41,277 - INFO - � Cross-validating CNL prediction (5 folds)
2025-08-26 13:54:41,285 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-26 13:54:41,292 - INFO -    � Fold 1/5
2025-08-26 13:54:41,340 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 13:54:41,669 - INFO -       ✅ Loaded model for fold 0 (CNL_fold_0)
2025-08-26 13:54:42,112 - INFO -       � R²: 0.7170, MAE: 0.4052, RMSE: 0.5302
2025-08-26 13:54:42,112 - INFO -    � Fold 2/5
2025-08-26 13:54:42,171 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 13:54:42,385 - INFO -       ✅ Loaded model for fold 1 (CNL_fold_1)
2025-08-26 13:54:42,737 - INFO -       � R²: 0.8676, MAE: 0.2737, RMSE: 0.3628
2025-08-26 13:54:42,737 - INFO -    � Fold 3/5
2025-08-26 13:54:42,772 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 13:54:42,994 - INFO -       ✅ Loaded model for fold 2 (CNL_fold_2)
2025-08-26 13:54:43,333 - INFO -       � R²: 0.7755, MAE: 0.3544, RMSE: 0.4637
2025-08-26 13:54:43,333 - INFO -    � Fold 4/5
2025-08-26 13:54:43,400 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 13:54:43,630 - INFO -       ✅ Loaded model for fold 3 (CNL_fold_3)
2025-08-26 13:54:43,997 - INFO -       � R²: 0.7026, MAE: 0.3764, RMSE: 0.5206
2025-08-26 13:54:43,997 - INFO -    � Fold 5/5
2025-08-26 13:54:44,037 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 13:54:44,254 - INFO -       ✅ Loaded model for fold 4 (CNL_fold_4)
2025-08-26 13:54:44,607 - INFO -       � R²: 0.7144, MAE: 0.3311, RMSE: 0.4705
2025-08-26 13:54:44,617 - INFO -    � Overall Performance:
2025-08-26 13:54:44,617 - INFO -       R² (normalized) = 0.7554 ± 0.0308 (95% CI: [0.6700, 0.8409])
2025-08-26 13:54:44,617 - INFO -       MAE (normalized) = 0.3482 ± 0.0223
2025-08-26 13:54:44,617 - INFO -       RMSE (normalized) = 0.4695 ± 0.0298
2025-08-26 13:54:44,627 - INFO - � Analyzing overfitting for CNL
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 13:54:44,637 - INFO -    � No significant overfitting (score: 9.0%)
2025-08-26 13:54:44,637 - INFO - � Estimating prediction uncertainty for CNL
2025-08-26 13:54:44,874 - INFO -    � Created 283 sequences for CNL prediction
2025-08-26 13:54:44,874 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 13:54:45,081 - INFO -    � Loading fold 0 for ensemble
2025-08-26 13:54:47,219 - INFO -    � Loading fold 1 for ensemble
2025-08-26 13:54:49,383 - INFO -    � Loading fold 2 for ensemble
2025-08-26 13:54:51,578 - INFO -    � Loading fold 3 for ensemble
2025-08-26 13:54:53,686 - INFO -    � Loading fold 4 for ensemble
2025-08-26 13:54:56,032 - INFO -    � Fold ensemble uncertainty ratio: 1.6184
2025-08-26 13:54:56,032 - INFO -    ⚠️  High uncertainty samples: 146776/181120
2025-08-26 13:54:56,086 - INFO - � Creating validation visualizations...
2025-08-26 13:55:02,914 - INFO -    ✅ Validation plot saved: validation_outputs\CNL_validation_results.png
2025-08-26 13:55:16,014 - INFO - ✅ Validation completed for CNL
2025-08-26 13:55:16,015 - INFO -
================================================================================
2025-08-26 13:55:16,019 - INFO - � VALIDATING DEN PREDICTION MODEL
2025-08-26 13:55:16,019 - INFO - ================================================================================
2025-08-26 13:55:16,019 - INFO - � Cross-validating DEN prediction (5 folds)
2025-08-26 13:55:16,019 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-26 13:55:16,029 - INFO -    � Fold 1/5
2025-08-26 13:55:16,074 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 13:55:16,271 - INFO -       ✅ Loaded model for fold 0 (DEN_fold_0)
2025-08-26 13:55:16,652 - INFO -       � R²: -0.0110, MAE: 0.7910, RMSE: 0.9956
2025-08-26 13:55:16,652 - INFO -    � Fold 2/5
2025-08-26 13:55:16,690 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 13:55:16,892 - INFO -       ✅ Loaded model for fold 1 (DEN_fold_1)
2025-08-26 13:55:17,232 - INFO -       � R²: 0.3145, MAE: 0.6196, RMSE: 0.8138
2025-08-26 13:55:17,242 - INFO -    � Fold 3/5
2025-08-26 13:55:17,272 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 13:55:17,486 - INFO -       ✅ Loaded model for fold 2 (DEN_fold_2)
2025-08-26 13:55:17,833 - INFO -       � R²: 0.1308, MAE: 0.6159, RMSE: 0.8636
2025-08-26 13:55:17,833 - INFO -    � Fold 4/5
2025-08-26 13:55:17,863 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 13:55:18,090 - INFO -       ✅ Loaded model for fold 3 (DEN_fold_3)
2025-08-26 13:55:18,461 - INFO -       � R²: 0.3220, MAE: 0.5955, RMSE: 0.7949
2025-08-26 13:55:18,461 - INFO -    � Fold 5/5
2025-08-26 13:55:18,502 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 13:55:18,717 - INFO -       ✅ Loaded model for fold 4 (DEN_fold_4)
2025-08-26 13:55:19,067 - INFO -       � R²: 0.2738, MAE: 0.6072, RMSE: 0.8135
2025-08-26 13:55:19,067 - INFO -    � Overall Performance:
2025-08-26 13:55:19,067 - INFO -       R² (normalized) = 0.2060 ± 0.0643 (95% CI: [0.0276, 0.3844])
2025-08-26 13:55:19,067 - INFO -       MAE (normalized) = 0.6458 ± 0.0365
2025-08-26 13:55:19,077 - INFO -       RMSE (normalized) = 0.8562 ± 0.0367
2025-08-26 13:55:19,077 - INFO - � Analyzing overfitting for DEN
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 13:55:19,087 - WARNING -    � Overfitting detected (score: 5.0%)
2025-08-26 13:55:19,097 - INFO - � Estimating prediction uncertainty for DEN
2025-08-26 13:55:19,301 - INFO -    � Created 283 sequences for DEN prediction
2025-08-26 13:55:19,302 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 13:55:19,517 - INFO -    � Loading fold 0 for ensemble
2025-08-26 13:55:21,646 - INFO -    � Loading fold 1 for ensemble
2025-08-26 13:55:23,851 - INFO -    � Loading fold 2 for ensemble
2025-08-26 13:55:25,976 - INFO -    � Loading fold 3 for ensemble
2025-08-26 13:55:28,095 - INFO -    � Loading fold 4 for ensemble
2025-08-26 13:55:30,387 - INFO -    � Fold ensemble uncertainty ratio: 6.8677
2025-08-26 13:55:30,387 - INFO -    ⚠️  High uncertainty samples: 179242/181120
2025-08-26 13:55:30,435 - INFO - � Creating validation visualizations...
2025-08-26 13:55:37,694 - INFO -    ✅ Validation plot saved: validation_outputs\DEN_validation_results.png
2025-08-26 13:56:48,226 - INFO - ✅ Validation completed for DEN
2025-08-26 13:56:48,226 - INFO -
================================================================================
2025-08-26 13:56:48,226 - INFO - � VALIDATING GR PREDICTION MODEL
2025-08-26 13:56:48,226 - INFO - ================================================================================
2025-08-26 13:56:48,236 - INFO - � Cross-validating GR prediction (5 folds)
2025-08-26 13:56:48,236 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-26 13:56:48,236 - INFO -    � Fold 1/5
2025-08-26 13:56:48,297 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 13:56:48,597 - INFO -       ✅ Loaded model for fold 0 (GR_fold_0)
2025-08-26 13:56:49,070 - INFO -       � R²: 0.5457, MAE: 0.5067, RMSE: 0.6645
2025-08-26 13:56:49,070 - INFO -    � Fold 2/5
2025-08-26 13:56:49,108 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 13:56:49,306 - INFO -       ✅ Loaded model for fold 1 (GR_fold_1)
2025-08-26 13:56:49,657 - INFO -       � R²: 0.7212, MAE: 0.3952, RMSE: 0.5241
2025-08-26 13:56:49,657 - INFO -    � Fold 3/5
2025-08-26 13:56:49,693 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 13:56:49,932 - INFO -       ✅ Loaded model for fold 2 (GR_fold_2)
2025-08-26 13:56:50,318 - INFO -       � R²: 0.6191, MAE: 0.4814, RMSE: 0.6153
2025-08-26 13:56:50,328 - INFO -    � Fold 4/5
2025-08-26 13:56:50,364 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 13:56:50,576 - INFO -       ✅ Loaded model for fold 3 (GR_fold_3)
2025-08-26 13:56:50,932 - INFO -       � R²: 0.6716, MAE: 0.4323, RMSE: 0.5695
2025-08-26 13:56:50,943 - INFO -    � Fold 5/5
2025-08-26 13:56:50,985 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 13:56:51,204 - INFO -       ✅ Loaded model for fold 4 (GR_fold_4)
2025-08-26 13:56:51,565 - INFO -       � R²: 0.4478, MAE: 0.5577, RMSE: 0.7250
2025-08-26 13:56:51,575 - INFO -    � Overall Performance:
2025-08-26 13:56:51,575 - INFO -       R² (normalized) = 0.6011 ± 0.0481 (95% CI: [0.4675, 0.7346])
2025-08-26 13:56:51,575 - INFO -       MAE (normalized) = 0.4747 ± 0.0284
2025-08-26 13:56:51,575 - INFO -       RMSE (normalized) = 0.6197 ± 0.0352
2025-08-26 13:56:51,575 - INFO - � Analyzing overfitting for GR
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 13:56:51,595 - WARNING -    � Overfitting detected (score: 5.5%)
2025-08-26 13:56:51,597 - INFO - � Estimating prediction uncertainty for GR
2025-08-26 13:56:51,815 - INFO -    � Created 283 sequences for GR prediction
2025-08-26 13:56:51,815 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 13:56:52,025 - INFO -    � Loading fold 0 for ensemble
2025-08-26 13:56:54,152 - INFO -    � Loading fold 1 for ensemble
2025-08-26 13:56:56,362 - INFO -    � Loading fold 2 for ensemble
2025-08-26 13:56:58,577 - INFO -    � Loading fold 3 for ensemble
2025-08-26 13:57:00,758 - INFO -    � Loading fold 4 for ensemble
2025-08-26 13:57:03,098 - INFO -    � Fold ensemble uncertainty ratio: 3.5508
2025-08-26 13:57:03,108 - INFO -    ⚠️  High uncertainty samples: 151247/181120
2025-08-26 13:57:03,160 - INFO - � Creating validation visualizations...
2025-08-26 13:57:09,957 - INFO -    ✅ Validation plot saved: validation_outputs\GR_validation_results.png
2025-08-26 13:57:17,699 - INFO - ✅ Validation completed for GR
2025-08-26 13:57:17,700 - INFO -
================================================================================
2025-08-26 13:57:17,702 - INFO - � VALIDATING RLLD PREDICTION MODEL
2025-08-26 13:57:17,703 - INFO - ================================================================================
2025-08-26 13:57:17,703 - INFO - � Cross-validating RLLD prediction (5 folds)
2025-08-26 13:57:17,713 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-26 13:57:17,713 - INFO -    � Fold 1/5
2025-08-26 13:57:17,748 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 13:57:18,055 - INFO -       ✅ Loaded model for fold 0 (RLLD_fold_0)
2025-08-26 13:57:18,465 - INFO -       � R²: 0.4948, MAE: 0.5307, RMSE: 0.6962
2025-08-26 13:57:18,475 - INFO -    � Fold 2/5
2025-08-26 13:57:18,502 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 13:57:18,719 - INFO -       ✅ Loaded model for fold 1 (RLLD_fold_1)
2025-08-26 13:57:19,076 - INFO -       � R²: 0.6390, MAE: 0.4566, RMSE: 0.5913
2025-08-26 13:57:19,076 - INFO -    � Fold 3/5
2025-08-26 13:57:19,106 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 13:57:19,303 - INFO -       ✅ Loaded model for fold 2 (RLLD_fold_2)
2025-08-26 13:57:19,660 - INFO -       � R²: 0.7199, MAE: 0.4099, RMSE: 0.5266
2025-08-26 13:57:19,660 - INFO -    � Fold 4/5
2025-08-26 13:57:19,692 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 13:57:19,913 - INFO -       ✅ Loaded model for fold 3 (RLLD_fold_3)
2025-08-26 13:57:20,277 - INFO -       � R²: 0.5293, MAE: 0.5211, RMSE: 0.6742
2025-08-26 13:57:20,277 - INFO -    � Fold 5/5
2025-08-26 13:57:20,309 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 13:57:20,530 - INFO -       ✅ Loaded model for fold 4 (RLLD_fold_4)
2025-08-26 13:57:20,887 - INFO -       � R²: 0.4087, MAE: 0.5826, RMSE: 0.7651
2025-08-26 13:57:20,887 - INFO -    � Overall Performance:
2025-08-26 13:57:20,887 - INFO -       R² (normalized) = 0.5583 ± 0.0547 (95% CI: [0.4064, 0.7102])
2025-08-26 13:57:20,897 - INFO -       MAE (normalized) = 0.5002 ± 0.0302
2025-08-26 13:57:20,897 - INFO -       RMSE (normalized) = 0.6507 ± 0.0416
2025-08-26 13:57:20,897 - INFO - � Analyzing overfitting for RLLD
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 13:57:20,917 - INFO -    � No significant overfitting (score: 4.6%)
2025-08-26 13:57:20,917 - INFO - � Estimating prediction uncertainty for RLLD
2025-08-26 13:57:21,084 - INFO -    � Created 283 sequences for RLLD prediction
2025-08-26 13:57:21,084 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 13:57:21,284 - INFO -    � Loading fold 0 for ensemble
2025-08-26 13:57:23,509 - INFO -    � Loading fold 1 for ensemble
2025-08-26 13:57:25,950 - INFO -    � Loading fold 2 for ensemble
2025-08-26 13:57:28,278 - INFO -    � Loading fold 3 for ensemble
2025-08-26 13:57:30,592 - INFO -    � Loading fold 4 for ensemble
2025-08-26 13:57:33,079 - INFO -    � Fold ensemble uncertainty ratio: 1.9607
2025-08-26 13:57:33,079 - INFO -    ⚠️  High uncertainty samples: 169117/181120
2025-08-26 13:57:33,119 - INFO - � Creating validation visualizations...
2025-08-26 13:57:40,363 - INFO -    ✅ Validation plot saved: validation_outputs\RLLD_validation_results.png
2025-08-26 14:00:18,291 - INFO - ✅ Validation completed for RLLD
2025-08-26 14:00:25,862 - INFO - � Validation results saved: validation_outputs\comprehensive_validation_results.json
2025-08-26 14:00:25,862 - INFO - � Validation report saved: validation_outputs\validation_report.md
2025-08-26 14:00:25,862 - INFO - � Comprehensive Model Validation Complete!
(imputeML) PS C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer> &
 C:/Users/<USER>/imputeML/Scripts/python.exe "c:/Users/<USER>/Documents/OneDrive - PT Pertamina (Persero)/13
_Python_PKB/4_GIT/MWLT/Init_transformer/multiwell/validation/model_validation_suite.py"
CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-26 17:30:56,510 - INFO - � Model Validator Initialized
2025-08-26 17:30:56,510 - INFO - � Device: cuda:0 (consistent with training)
2025-08-26 17:30:56,510 - INFO - � Starting Comprehensive Model Validation
2025-08-26 17:30:56,510 - INFO - � Loading data...
2025-08-26 17:30:56,510 - INFO -    � Loading same HDF5 data as training...
2025-08-26 17:30:56,520 - INFO -    ✅ Found data files in: C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero
)\13_Python_PKB\4_GIT\MWLT\Init_transformer
2025-08-26 17:30:56,520 - INFO -    � Processing A1.hdf5...
Processing A1.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5120,), range [165.99, 357.24]
  CNL: (5120,), range [4.37, 35.62]
  DEN: (5120,), range [2.00, 2.77]
  DEPTH: (5120,), range [1805.75, 2450.00]
  GR: (5120,), range [20.31, 149.43]
  RLLD: (5120,), range [0.84, 3.83]
2025-08-26 17:30:56,550 - INFO -    � Creating 13 windows from 5120 samples
2025-08-26 17:30:56,567 - INFO -    � Processing A2.hdf5...
Processing A2.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5194,), range [151.63, 357.28]
  CNL: (5194,), range [0.11, 55.20]
  DEN: (5194,), range [2.00, 2.86]
  DEPTH: (5194,), range [2653.38, 3313.88]
  GR: (5194,), range [22.42, 194.46]
  RLLD: (5194,), range [0.70, 3.84]
2025-08-26 17:30:56,579 - INFO -    � Creating 13 windows from 5194 samples
2025-08-26 17:30:56,591 - INFO -    ✅ Loaded real data with 18720 total samples
2025-08-26 17:30:56,591 - INFO -    ✅ Loaded same HDF5 data as training
2025-08-26 17:30:56,591 - INFO - � Loading trained models...
2025-08-26 17:30:56,601 - INFO - Checking for models in: enhanced_training_outputs
2025-08-26 17:30:56,601 - INFO -    ✅ Found AC fold 0 model
2025-08-26 17:30:56,605 - INFO -    ✅ Found AC fold 1 model
2025-08-26 17:30:56,607 - INFO -    ✅ Found AC fold 2 model
2025-08-26 17:30:56,610 - INFO -    ✅ Found AC fold 3 model
2025-08-26 17:30:56,612 - INFO -    ✅ Found AC fold 4 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found CNL fold 0 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found CNL fold 1 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found CNL fold 2 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found CNL fold 3 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found CNL fold 4 model
2025-08-26 17:30:56,613 - INFO -    ✅ Found DEN fold 0 model
2025-08-26 17:30:56,623 - INFO -    ✅ Found DEN fold 1 model
2025-08-26 17:30:56,625 - INFO -    ✅ Found DEN fold 2 model
2025-08-26 17:30:56,626 - INFO -    ✅ Found DEN fold 3 model
2025-08-26 17:30:56,626 - INFO -    ✅ Found DEN fold 4 model
2025-08-26 17:30:56,639 - INFO -    ✅ Found GR fold 0 model
2025-08-26 17:30:56,641 - INFO -    ✅ Found GR fold 1 model
2025-08-26 17:30:56,641 - INFO -    ✅ Found GR fold 2 model
2025-08-26 17:30:56,641 - INFO -    ✅ Found GR fold 3 model
2025-08-26 17:30:56,641 - INFO -    ✅ Found GR fold 4 model
2025-08-26 17:30:56,651 - INFO -    ✅ Found RLLD fold 0 model
2025-08-26 17:30:56,651 - INFO -    ✅ Found RLLD fold 1 model
2025-08-26 17:30:56,655 - INFO -    ✅ Found RLLD fold 2 model
2025-08-26 17:30:56,657 - INFO -    ✅ Found RLLD fold 3 model
2025-08-26 17:30:56,658 - INFO -    ✅ Found RLLD fold 4 model
2025-08-26 17:30:56,659 - INFO - Checking for models in: enhanced_training_outputs
2025-08-26 17:30:56,660 - INFO -    ✅ Found AC fold 0 model
2025-08-26 17:30:56,660 - INFO -    ✅ Found AC fold 1 model
2025-08-26 17:30:56,660 - INFO -    ✅ Found AC fold 2 model
2025-08-26 17:30:56,660 - INFO -    ✅ Found AC fold 3 model
2025-08-26 17:30:56,660 - INFO -    ✅ Found AC fold 4 model
2025-08-26 17:30:56,670 - INFO -    ✅ Found CNL fold 0 model
2025-08-26 17:30:56,673 - INFO -    ✅ Found CNL fold 1 model
2025-08-26 17:30:56,675 - INFO -    ✅ Found CNL fold 2 model
2025-08-26 17:30:56,676 - INFO -    ✅ Found CNL fold 3 model
2025-08-26 17:30:56,693 - INFO -    ✅ Found CNL fold 4 model
2025-08-26 17:30:56,696 - INFO -    ✅ Found DEN fold 0 model
2025-08-26 17:30:56,699 - INFO -    ✅ Found DEN fold 1 model
2025-08-26 17:30:56,702 - INFO -    ✅ Found DEN fold 2 model
2025-08-26 17:30:56,704 - INFO -    ✅ Found DEN fold 3 model
2025-08-26 17:30:56,706 - INFO -    ✅ Found DEN fold 4 model
2025-08-26 17:30:56,708 - INFO -    ✅ Found GR fold 0 model
2025-08-26 17:30:56,708 - INFO -    ✅ Found GR fold 1 model
2025-08-26 17:30:56,708 - INFO -    ✅ Found GR fold 2 model
2025-08-26 17:30:56,708 - INFO -    ✅ Found GR fold 3 model
2025-08-26 17:30:56,708 - INFO -    ✅ Found GR fold 4 model
2025-08-26 17:30:56,718 - INFO -    ✅ Found RLLD fold 0 model
2025-08-26 17:30:56,718 - INFO -    ✅ Found RLLD fold 1 model
2025-08-26 17:30:56,723 - INFO -    ✅ Found RLLD fold 2 model
2025-08-26 17:30:56,724 - INFO -    ✅ Found RLLD fold 3 model
2025-08-26 17:30:56,734 - INFO -    ✅ Found RLLD fold 4 model
2025-08-26 17:30:56,734 - INFO - Checking for models in: c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\1
3_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_training_outputs
2025-08-26 17:30:56,741 - INFO -    ✅ Found AC fold 0 model
2025-08-26 17:30:56,742 - INFO -    ✅ Found AC fold 1 model
2025-08-26 17:30:56,745 - INFO -    ✅ Found AC fold 2 model
2025-08-26 17:30:56,747 - INFO -    ✅ Found AC fold 3 model
2025-08-26 17:30:56,747 - INFO -    ✅ Found AC fold 4 model
2025-08-26 17:30:56,758 - INFO -
================================================================================
2025-08-26 17:30:56,758 - INFO - � VALIDATING AC PREDICTION MODEL
2025-08-26 17:30:56,758 - INFO - ================================================================================
2025-08-26 17:30:56,758 - INFO - � Cross-validating AC prediction (5 folds)
2025-08-26 17:30:56,758 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-26 17:30:56,768 - INFO -    � Fold 1/5
2025-08-26 17:30:56,817 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 17:30:57,147 - INFO -       ✅ Loaded model for fold 0 (AC_fold_0)
2025-08-26 17:31:01,903 - INFO -       � Physical units - R²: -2094.1064, MAE: 768.21 μs/ft, RMSE: 772.28 μs/ft
2025-08-26 17:31:01,913 - INFO -       � R²: -2094.1062, MAE: 10.2428, RMSE: 10.2971
2025-08-26 17:31:01,913 - INFO -    � Fold 2/5
2025-08-26 17:31:01,971 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 17:31:02,199 - INFO -       ✅ Loaded model for fold 1 (AC_fold_1)
2025-08-26 17:31:02,535 - INFO -       � Physical units - R²: -830.0766, MAE: 816.78 μs/ft, RMSE: 825.10 μs/ft
2025-08-26 17:31:02,535 - INFO -       � R²: -830.0767, MAE: 10.8904, RMSE: 11.0013
2025-08-26 17:31:02,535 - INFO -    � Fold 3/5
2025-08-26 17:31:02,579 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 17:31:02,801 - INFO -       ✅ Loaded model for fold 2 (AC_fold_2)
2025-08-26 17:31:03,129 - INFO -       � Physical units - R²: -402.5542, MAE: 727.78 μs/ft, RMSE: 730.54 μs/ft
2025-08-26 17:31:03,139 - INFO -       � R²: -402.5542, MAE: 9.7037, RMSE: 9.7405
2025-08-26 17:31:03,139 - INFO -    � Fold 4/5
2025-08-26 17:31:03,178 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 17:31:03,362 - INFO -       ✅ Loaded model for fold 3 (AC_fold_3)
2025-08-26 17:31:03,685 - INFO -       � Physical units - R²: -852.0988, MAE: 771.85 μs/ft, RMSE: 774.30 μs/ft
2025-08-26 17:31:03,695 - INFO -       � R²: -852.0987, MAE: 10.2914, RMSE: 10.3240
2025-08-26 17:31:03,695 - INFO -    � Fold 5/5
2025-08-26 17:31:03,732 - INFO -    � Created 49 sequences for AC prediction
2025-08-26 17:31:03,909 - INFO -       ✅ Loaded model for fold 4 (AC_fold_4)
2025-08-26 17:31:04,306 - INFO -       � Physical units - R²: -1346.5820, MAE: 802.80 μs/ft, RMSE: 809.58 μs/ft
2025-08-26 17:31:04,316 - INFO -       � R²: -1346.5820, MAE: 10.7040, RMSE: 10.7944
2025-08-26 17:31:04,326 - INFO -    � Overall Performance:
2025-08-26 17:31:04,326 - INFO -       R² (normalized) = -1105.0836 ± 288.9347 (95% CI: [-1907.2948, -302.8723])
2025-08-26 17:31:04,326 - INFO -       MAE (normalized) = 10.3664 ± 0.2060
2025-08-26 17:31:04,326 - INFO -       RMSE (normalized) = 10.4315 ± 0.2195
2025-08-26 17:31:04,326 - INFO -       R² (physical) = -1105.0836 ± 288.9347 (95% CI: [-1907.2950, -302.8722])
2025-08-26 17:31:04,326 - INFO -       MAE (physical) = 777.48 ± 15.45 μs/ft
2025-08-26 17:31:04,326 - INFO -       RMSE (physical) = 782.36 ± 16.46 μs/ft
2025-08-26 17:31:04,336 - INFO - � Analyzing overfitting for AC
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 17:31:04,336 - WARNING -    � Overfitting detected (score: 0.0%)
2025-08-26 17:31:04,336 - INFO - � Estimating prediction uncertainty for AC
2025-08-26 17:31:04,529 - INFO -    � Created 283 sequences for AC prediction
2025-08-26 17:31:04,529 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 17:31:04,713 - INFO -    � Loading fold 0 for ensemble
2025-08-26 17:31:07,003 - INFO -    � Loading fold 1 for ensemble
2025-08-26 17:31:09,058 - INFO -    � Loading fold 2 for ensemble
2025-08-26 17:31:11,330 - INFO -    � Loading fold 3 for ensemble
2025-08-26 17:31:13,628 - INFO -    � Loading fold 4 for ensemble
2025-08-26 17:31:15,833 - INFO -    � Fold ensemble uncertainty ratio: 0.0742
2025-08-26 17:31:15,843 - INFO -    ⚠️  High uncertainty samples: 28694/181120
2025-08-26 17:31:15,880 - INFO - � Creating validation visualizations...
2025-08-26 17:31:22,811 - INFO -    ✅ Validation plot saved: validation_outputs\AC_validation_results.png
2025-08-26 17:31:30,725 - INFO - ✅ Validation completed for AC
2025-08-26 17:31:30,727 - INFO -
================================================================================
2025-08-26 17:31:30,737 - INFO - � VALIDATING CNL PREDICTION MODEL
2025-08-26 17:31:30,788 - INFO - ================================================================================
2025-08-26 17:31:30,795 - INFO - � Cross-validating CNL prediction (5 folds)
2025-08-26 17:31:30,800 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-26 17:31:30,808 - INFO -    � Fold 1/5
2025-08-26 17:31:30,873 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 17:31:31,151 - INFO -       ✅ Loaded model for fold 0 (CNL_fold_0)
2025-08-26 17:31:31,672 - INFO -       � R²: 0.7170, MAE: 0.4052, RMSE: 0.5302
2025-08-26 17:31:31,672 - INFO -    � Fold 2/5
2025-08-26 17:31:31,716 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 17:31:31,926 - INFO -       ✅ Loaded model for fold 1 (CNL_fold_1)
2025-08-26 17:31:32,303 - INFO -       � R²: 0.8676, MAE: 0.2737, RMSE: 0.3628
2025-08-26 17:31:32,304 - INFO -    � Fold 3/5
2025-08-26 17:31:32,373 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 17:31:32,538 - INFO -       ✅ Loaded model for fold 2 (CNL_fold_2)
2025-08-26 17:31:32,944 - INFO -       � R²: 0.7755, MAE: 0.3544, RMSE: 0.4637
2025-08-26 17:31:32,944 - INFO -    � Fold 4/5
2025-08-26 17:31:33,013 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 17:31:33,191 - INFO -       ✅ Loaded model for fold 3 (CNL_fold_3)
2025-08-26 17:31:33,570 - INFO -       � R²: 0.7026, MAE: 0.3764, RMSE: 0.5206
2025-08-26 17:31:33,570 - INFO -    � Fold 5/5
2025-08-26 17:31:33,621 - INFO -    � Created 49 sequences for CNL prediction
2025-08-26 17:31:33,820 - INFO -       ✅ Loaded model for fold 4 (CNL_fold_4)
2025-08-26 17:31:34,202 - INFO -       � R²: 0.7144, MAE: 0.3311, RMSE: 0.4705
2025-08-26 17:31:34,202 - INFO -    � Overall Performance:
2025-08-26 17:31:34,202 - INFO -       R² (normalized) = 0.7554 ± 0.0308 (95% CI: [0.6700, 0.8409])
2025-08-26 17:31:34,202 - INFO -       MAE (normalized) = 0.3482 ± 0.0223
2025-08-26 17:31:34,202 - INFO -       RMSE (normalized) = 0.4695 ± 0.0298
2025-08-26 17:31:34,212 - INFO - � Analyzing overfitting for CNL
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 17:31:34,212 - INFO -    � No significant overfitting (score: 9.0%)
2025-08-26 17:31:34,212 - INFO - � Estimating prediction uncertainty for CNL
2025-08-26 17:31:34,467 - INFO -    � Created 283 sequences for CNL prediction
2025-08-26 17:31:34,467 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 17:31:34,622 - INFO -    � Loading fold 0 for ensemble
2025-08-26 17:31:36,901 - INFO -    � Loading fold 1 for ensemble
2025-08-26 17:31:39,477 - INFO -    � Loading fold 2 for ensemble
2025-08-26 17:31:41,716 - INFO -    � Loading fold 3 for ensemble
2025-08-26 17:31:44,025 - INFO -    � Loading fold 4 for ensemble
2025-08-26 17:31:46,317 - INFO -    � Fold ensemble uncertainty ratio: 1.6184
2025-08-26 17:31:46,317 - INFO -    ⚠️  High uncertainty samples: 146776/181120
2025-08-26 17:31:46,364 - INFO - � Creating validation visualizations...
2025-08-26 17:31:53,120 - INFO -    ✅ Validation plot saved: validation_outputs\CNL_validation_results.png
2025-08-26 17:32:04,164 - INFO - ✅ Validation completed for CNL
2025-08-26 17:32:04,164 - INFO -
================================================================================
2025-08-26 17:32:04,164 - INFO - � VALIDATING DEN PREDICTION MODEL
2025-08-26 17:32:04,224 - INFO - ================================================================================
2025-08-26 17:32:04,235 - INFO - � Cross-validating DEN prediction (5 folds)
2025-08-26 17:32:04,244 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-26 17:32:04,256 - INFO -    � Fold 1/5
2025-08-26 17:32:04,324 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 17:32:04,487 - INFO -       ✅ Loaded model for fold 0 (DEN_fold_0)
2025-08-26 17:32:04,878 - INFO -       � R²: -0.0110, MAE: 0.7910, RMSE: 0.9956
2025-08-26 17:32:04,879 - INFO -    � Fold 2/5
2025-08-26 17:32:04,928 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 17:32:05,099 - INFO -       ✅ Loaded model for fold 1 (DEN_fold_1)
2025-08-26 17:32:05,468 - INFO -       � R²: 0.3145, MAE: 0.6196, RMSE: 0.8138
2025-08-26 17:32:05,468 - INFO -    � Fold 3/5
2025-08-26 17:32:05,507 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 17:32:05,676 - INFO -       ✅ Loaded model for fold 2 (DEN_fold_2)
2025-08-26 17:32:06,029 - INFO -       � R²: 0.1308, MAE: 0.6159, RMSE: 0.8636
2025-08-26 17:32:06,029 - INFO -    � Fold 4/5
2025-08-26 17:32:06,066 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 17:32:06,227 - INFO -       ✅ Loaded model for fold 3 (DEN_fold_3)
2025-08-26 17:32:06,580 - INFO -       � R²: 0.3220, MAE: 0.5955, RMSE: 0.7949
2025-08-26 17:32:06,580 - INFO -    � Fold 5/5
2025-08-26 17:32:06,620 - INFO -    � Created 49 sequences for DEN prediction
2025-08-26 17:32:06,774 - INFO -       ✅ Loaded model for fold 4 (DEN_fold_4)
2025-08-26 17:32:07,107 - INFO -       � R²: 0.2738, MAE: 0.6072, RMSE: 0.8135
2025-08-26 17:32:07,107 - INFO -    � Overall Performance:
2025-08-26 17:32:07,117 - INFO -       R² (normalized) = 0.2060 ± 0.0643 (95% CI: [0.0276, 0.3844])
2025-08-26 17:32:07,117 - INFO -       MAE (normalized) = 0.6458 ± 0.0365
2025-08-26 17:32:07,117 - INFO -       RMSE (normalized) = 0.8562 ± 0.0367
2025-08-26 17:32:07,117 - INFO - � Analyzing overfitting for DEN
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 17:32:07,146 - WARNING -    � Overfitting detected (score: 5.0%)
2025-08-26 17:32:07,150 - INFO - � Estimating prediction uncertainty for DEN
2025-08-26 17:32:07,387 - INFO -    � Created 283 sequences for DEN prediction
2025-08-26 17:32:07,387 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 17:32:07,548 - INFO -    � Loading fold 0 for ensemble
2025-08-26 17:32:09,638 - INFO -    � Loading fold 1 for ensemble
2025-08-26 17:32:11,686 - INFO -    � Loading fold 2 for ensemble
2025-08-26 17:32:13,712 - INFO -    � Loading fold 3 for ensemble
2025-08-26 17:32:15,912 - INFO -    � Loading fold 4 for ensemble
2025-08-26 17:32:18,258 - INFO -    � Fold ensemble uncertainty ratio: 6.8677
2025-08-26 17:32:18,259 - INFO -    ⚠️  High uncertainty samples: 179242/181120
2025-08-26 17:32:18,317 - INFO - � Creating validation visualizations...
2025-08-26 17:32:25,091 - INFO -    ✅ Validation plot saved: validation_outputs\DEN_validation_results.png
2025-08-26 17:33:45,418 - INFO - ✅ Validation completed for DEN
2025-08-26 17:33:45,419 - INFO -
================================================================================
2025-08-26 17:33:45,421 - INFO - � VALIDATING GR PREDICTION MODEL
2025-08-26 17:33:45,422 - INFO - ================================================================================
2025-08-26 17:33:45,422 - INFO - � Cross-validating GR prediction (5 folds)
2025-08-26 17:33:45,432 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-26 17:33:45,432 - INFO -    � Fold 1/5
2025-08-26 17:33:45,478 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 17:33:45,856 - INFO -       ✅ Loaded model for fold 0 (GR_fold_0)
2025-08-26 17:33:46,315 - INFO -       � R²: 0.5457, MAE: 0.5067, RMSE: 0.6645
2025-08-26 17:33:46,315 - INFO -    � Fold 2/5
2025-08-26 17:33:46,352 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 17:33:46,536 - INFO -       ✅ Loaded model for fold 1 (GR_fold_1)
2025-08-26 17:33:46,910 - INFO -       � R²: 0.7212, MAE: 0.3952, RMSE: 0.5241
2025-08-26 17:33:46,910 - INFO -    � Fold 3/5
2025-08-26 17:33:46,974 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 17:33:47,129 - INFO -       ✅ Loaded model for fold 2 (GR_fold_2)
2025-08-26 17:33:47,487 - INFO -       � R²: 0.6191, MAE: 0.4814, RMSE: 0.6153
2025-08-26 17:33:47,487 - INFO -    � Fold 4/5
2025-08-26 17:33:47,548 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 17:33:47,724 - INFO -       ✅ Loaded model for fold 3 (GR_fold_3)
2025-08-26 17:33:48,101 - INFO -       � R²: 0.6716, MAE: 0.4323, RMSE: 0.5695
2025-08-26 17:33:48,111 - INFO -    � Fold 5/5
2025-08-26 17:33:48,151 - INFO -    � Created 49 sequences for GR prediction
2025-08-26 17:33:48,364 - INFO -       ✅ Loaded model for fold 4 (GR_fold_4)
2025-08-26 17:33:48,698 - INFO -       � R²: 0.4478, MAE: 0.5577, RMSE: 0.7250
2025-08-26 17:33:48,698 - INFO -    � Overall Performance:
2025-08-26 17:33:48,708 - INFO -       R² (normalized) = 0.6011 ± 0.0481 (95% CI: [0.4675, 0.7346])
2025-08-26 17:33:48,718 - INFO -       MAE (normalized) = 0.4747 ± 0.0284
2025-08-26 17:33:48,718 - INFO -       RMSE (normalized) = 0.6197 ± 0.0352
2025-08-26 17:33:48,718 - INFO - � Analyzing overfitting for GR
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 17:33:48,738 - WARNING -    � Overfitting detected (score: 5.5%)
2025-08-26 17:33:48,748 - INFO - � Estimating prediction uncertainty for GR
2025-08-26 17:33:48,968 - INFO -    � Created 283 sequences for GR prediction
2025-08-26 17:33:48,968 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 17:33:49,122 - INFO -    � Loading fold 0 for ensemble
2025-08-26 17:33:51,241 - INFO -    � Loading fold 1 for ensemble
2025-08-26 17:33:53,296 - INFO -    � Loading fold 2 for ensemble
2025-08-26 17:33:55,315 - INFO -    � Loading fold 3 for ensemble
2025-08-26 17:33:57,390 - INFO -    � Loading fold 4 for ensemble
2025-08-26 17:33:59,629 - INFO -    � Fold ensemble uncertainty ratio: 3.5508
2025-08-26 17:33:59,639 - INFO -    ⚠️  High uncertainty samples: 151247/181120
2025-08-26 17:33:59,692 - INFO - � Creating validation visualizations...
2025-08-26 17:34:06,319 - INFO -    ✅ Validation plot saved: validation_outputs\GR_validation_results.png
2025-08-26 17:34:11,695 - INFO - ✅ Validation completed for GR
2025-08-26 17:34:11,695 - INFO -
================================================================================
2025-08-26 17:34:11,695 - INFO - � VALIDATING RLLD PREDICTION MODEL
2025-08-26 17:34:11,745 - INFO - ================================================================================
2025-08-26 17:34:11,755 - INFO - � Cross-validating RLLD prediction (5 folds)
2025-08-26 17:34:11,763 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-26 17:34:11,771 - INFO -    � Fold 1/5
2025-08-26 17:34:11,834 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 17:34:12,091 - INFO -       ✅ Loaded model for fold 0 (RLLD_fold_0)
2025-08-26 17:34:12,501 - INFO -       � R²: 0.4948, MAE: 0.5307, RMSE: 0.6962
2025-08-26 17:34:12,501 - INFO -    � Fold 2/5
2025-08-26 17:34:12,531 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 17:34:12,691 - INFO -       ✅ Loaded model for fold 1 (RLLD_fold_1)
2025-08-26 17:34:13,059 - INFO -       � R²: 0.6390, MAE: 0.4566, RMSE: 0.5913
2025-08-26 17:34:13,060 - INFO -    � Fold 3/5
2025-08-26 17:34:13,089 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 17:34:13,296 - INFO -       ✅ Loaded model for fold 2 (RLLD_fold_2)
2025-08-26 17:34:13,703 - INFO -       � R²: 0.7199, MAE: 0.4099, RMSE: 0.5266
2025-08-26 17:34:13,713 - INFO -    � Fold 4/5
2025-08-26 17:34:13,745 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 17:34:13,922 - INFO -       ✅ Loaded model for fold 3 (RLLD_fold_3)
2025-08-26 17:34:14,269 - INFO -       � R²: 0.5293, MAE: 0.5211, RMSE: 0.6742
2025-08-26 17:34:14,269 - INFO -    � Fold 5/5
2025-08-26 17:34:14,319 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-26 17:34:14,499 - INFO -       ✅ Loaded model for fold 4 (RLLD_fold_4)
2025-08-26 17:34:14,842 - INFO -       � R²: 0.4087, MAE: 0.5826, RMSE: 0.7651
2025-08-26 17:34:14,842 - INFO -    � Overall Performance:
2025-08-26 17:34:14,842 - INFO -       R² (normalized) = 0.5583 ± 0.0547 (95% CI: [0.4064, 0.7102])
2025-08-26 17:34:14,842 - INFO -       MAE (normalized) = 0.5002 ± 0.0302
2025-08-26 17:34:14,842 - INFO -       RMSE (normalized) = 0.6507 ± 0.0416
2025-08-26 17:34:14,852 - INFO - � Analyzing overfitting for RLLD
c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\valida
tion\model_validation_suite.py:660: RankWarning: Polyfit may be poorly conditioned
  val_trend = float(np.polyfit(range(trend_start, len(val_losses)), val_losses[trend_start:], 1)[0])
2025-08-26 17:34:14,852 - INFO -    � No significant overfitting (score: 4.6%)
2025-08-26 17:34:14,852 - INFO - � Estimating prediction uncertainty for RLLD
2025-08-26 17:34:14,999 - INFO -    � Created 283 sequences for RLLD prediction
2025-08-26 17:34:14,999 - INFO -    � Using fold ensemble uncertainty estimation
2025-08-26 17:34:15,153 - INFO -    � Loading fold 0 for ensemble
2025-08-26 17:34:17,203 - INFO -    � Loading fold 1 for ensemble
2025-08-26 17:34:19,295 - INFO -    � Loading fold 2 for ensemble
2025-08-26 17:34:21,383 - INFO -    � Loading fold 3 for ensemble
2025-08-26 17:34:23,448 - INFO -    � Loading fold 4 for ensemble
2025-08-26 17:34:25,720 - INFO -    � Fold ensemble uncertainty ratio: 1.9607
2025-08-26 17:34:25,720 - INFO -    ⚠️  High uncertainty samples: 169117/181120
2025-08-26 17:34:25,765 - INFO - � Creating validation visualizations...
2025-08-26 17:34:32,552 - INFO -    ✅ Validation plot saved: validation_outputs\RLLD_validation_results.png
2025-08-26 17:34:39,591 - INFO - ✅ Validation completed for RLLD
2025-08-26 17:34:47,107 - INFO - � Validation results saved: validation_outputs\comprehensive_validation_results.json
2025-08-26 17:34:47,107 - INFO - � Validation report saved: validation_outputs\validation_report.md
2025-08-26 17:34:47,107 - INFO - � Comprehensive Model Validation Complete!