#!/usr/bin/env python3
"""
Production Model Selector
Selects the best model from cross-validation folds for production deployment

This script addresses the missing link between enhanced training outputs
and the prediction/validation scripts by:
1. Analyzing cross-validation results
2. Selecting the single best model per curve
3. Creating production model directory
4. Updating prediction scripts configuration

Author: AI Assistant
Date: 2025-08-23
"""

import os
import sys
import json
import shutil
import numpy as np
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ProductionModelSelector:
    """Selects and manages production models from cross-validation results"""
    
    def __init__(self, enhanced_results_dir: str = "enhanced_training_outputs"):
        self.enhanced_dir = Path(enhanced_results_dir)
        self.production_dir = Path("production_models")
        self.results_file = self.enhanced_dir / "enhanced_training_results.json"
        
        logger.info(f"🎯 Production Model Selector Initialized")
        logger.info(f"📂 Enhanced results: {self.enhanced_dir}")
        logger.info(f"📁 Production target: {self.production_dir}")
    
    def analyze_cross_validation_results(self) -> Dict:
        """Analyze enhanced training results to find best models"""
        logger.info("📊 Analyzing cross-validation results...")
        
        if not self.results_file.exists():
            logger.error(f"❌ Enhanced training results not found: {self.results_file}")
            return {}
        
        with open(self.results_file, 'r') as f:
            results = json.load(f)
        
        analysis = {}
        
        for curve, data in results.items():
            logger.info(f"🔍 Analyzing {curve} models...")
            
            if not data.get('fold_results') or not data['fold_results']:
                logger.warning(f"   ⚠️  No fold results found for {curve}")
                analysis[curve] = {'status': 'no_data'}
                continue
            
            fold_performances = []
            
            for fold_result in data['fold_results']:
                if not fold_result or not fold_result.get('training_results'):
                    continue
                
                tr = fold_result['training_results']
                fold_info = {
                    'fold': fold_result['fold'],
                    'val_loss': tr.get('best_val_loss', float('inf')),
                    'r2': tr.get('best_r2', -float('inf')),
                    'epochs': tr.get('total_epochs', 0),
                    'training_time': tr.get('total_time', 0),
                    'model_path': Path(tr.get('best_model_path', ''))
                }
                
                # Calculate stability score (lower is better)
                training_history = tr.get('training_history', {})
                if 'val_loss' in training_history:
                    val_losses = training_history['val_loss']
                    if len(val_losses) > 1:
                        # Calculate coefficient of variation for stability
                        val_loss_std = np.std(val_losses[-10:])  # Last 10 epochs
                        val_loss_mean = np.mean(val_losses[-10:])
                        fold_info['stability_score'] = val_loss_std / (val_loss_mean + 1e-8)
                    else:
                        fold_info['stability_score'] = float('inf')
                else:
                    fold_info['stability_score'] = float('inf')
                
                fold_performances.append(fold_info)
            
            if fold_performances:
                analysis[curve] = {
                    'status': 'analyzed',
                    'fold_performances': fold_performances,
                    'num_folds': len(fold_performances)
                }
                
                # Log performance summary
                logger.info(f"   📈 {curve}: {len(fold_performances)} folds analyzed")
                best_r2 = max(fp['r2'] for fp in fold_performances)
                best_loss = min(fp['val_loss'] for fp in fold_performances)
                logger.info(f"      Best R²: {best_r2:.4f}, Best Loss: {best_loss:.4f}")
            else:
                analysis[curve] = {'status': 'no_valid_folds'}
                logger.warning(f"   ⚠️  No valid fold performances for {curve}")
        
        return analysis
    
    def select_best_models(self, analysis: Dict) -> Dict:
        """Select the single best model for each curve based on multiple criteria"""
        logger.info("🏆 Selecting best models for production...")
        
        best_models = {}
        
        for curve, data in analysis.items():
            if data['status'] != 'analyzed':
                logger.warning(f"   ⚠️  Skipping {curve}: {data['status']}")
                continue
            
            fold_performances = data['fold_performances']
            
            # Multi-criteria selection
            scored_folds = []
            
            for fp in fold_performances:
                # Scoring criteria (normalized 0-1, higher is better)
                r2_score = max(0, fp['r2'])  # R² contribution
                loss_score = 1 / (1 + fp['val_loss'])  # Loss contribution (inverted)
                stability_score = 1 / (1 + fp['stability_score'])  # Stability contribution (inverted)
                
                # Weighted composite score
                composite_score = (
                    0.5 * r2_score +           # 50% weight on R²
                    0.3 * loss_score +         # 30% weight on validation loss
                    0.2 * stability_score      # 20% weight on stability
                )
                
                scored_folds.append({
                    **fp,
                    'r2_score': r2_score,
                    'loss_score': loss_score,
                    'stability_score': stability_score,
                    'composite_score': composite_score
                })
            
            # Select best fold
            best_fold = max(scored_folds, key=lambda x: x['composite_score'])
            
            best_models[curve] = {
                'selected_fold': best_fold['fold'],
                'model_path': best_fold['model_path'],
                'performance': {
                    'r2': best_fold['r2'],
                    'val_loss': best_fold['val_loss'],
                    'stability_score': best_fold['stability_score'],
                    'composite_score': best_fold['composite_score']
                },
                'selection_reason': self._get_selection_reason(best_fold, scored_folds),
                'all_fold_scores': scored_folds
            }
            
            logger.info(f"   🎯 {curve}: Selected Fold {best_fold['fold']} "
                       f"(R²: {best_fold['r2']:.4f}, Loss: {best_fold['val_loss']:.4f})")
            logger.info(f"      Reason: {best_models[curve]['selection_reason']}")
        
        return best_models
    
    def _get_selection_reason(self, best_fold: Dict, all_folds: List[Dict]) -> str:
        """Generate human-readable reason for model selection"""
        reasons = []
        
        # Check if best R²
        best_r2 = max(fold['r2'] for fold in all_folds)
        if abs(best_fold['r2'] - best_r2) < 1e-6:
            reasons.append("Highest R²")
        
        # Check if best validation loss
        best_loss = min(fold['val_loss'] for fold in all_folds)
        if abs(best_fold['val_loss'] - best_loss) < 1e-6:
            reasons.append("Lowest validation loss")
        
        # Check if best stability
        best_stability = max(fold['stability_score'] for fold in all_folds)
        if abs(best_fold['stability_score'] - best_stability) < 1e-6:
            reasons.append("Most stable training")
        
        # Check if best composite
        best_composite = max(fold['composite_score'] for fold in all_folds)
        if abs(best_fold['composite_score'] - best_composite) < 1e-6:
            reasons.append("Best overall performance")
        
        return " + ".join(reasons) if reasons else "Selected by composite score"
    
    def create_production_models(self, best_models: Dict) -> bool:
        """Create production model directory with selected models"""
        logger.info("🏭 Creating production models directory...")
        
        # Create production directory
        if self.production_dir.exists():
            logger.info(f"   🔄 Removing existing production directory...")
            shutil.rmtree(self.production_dir)
        
        self.production_dir.mkdir(exist_ok=True)
        
        success_count = 0
        
        for curve, model_info in best_models.items():
            logger.info(f"   📦 Creating production model for {curve}...")
            
            try:
                # Create curve-specific directory
                curve_dir = self.production_dir / f"{curve.lower()}_production_model"
                curve_dir.mkdir(exist_ok=True)
                
                # Copy best model files
                source_fold_dir = model_info['model_path'].parent
                
                # Copy model file
                source_model = source_fold_dir / "best_model.pth"
                target_model = curve_dir / "best_model.pth"
                if source_model.exists():
                    shutil.copy2(source_model, target_model)
                    logger.info(f"      ✅ Copied model: {source_model} → {target_model}")
                else:
                    logger.error(f"      ❌ Source model not found: {source_model}")
                    continue
                
                # Copy config file
                source_config = source_fold_dir / "training_config.json"
                target_config = curve_dir / "training_config.json"
                if source_config.exists():
                    shutil.copy2(source_config, target_config)
                    logger.info(f"      ✅ Copied config: {source_config} → {target_config}")
                
                # Copy training history
                source_history = source_fold_dir / "training_history.json"
                target_history = curve_dir / "training_history.json"
                if source_history.exists():
                    shutil.copy2(source_history, target_history)
                
                # Create selection report
                selection_report = {
                    'curve': curve,
                    'selected_fold': model_info['selected_fold'],
                    'source_path': str(source_fold_dir),
                    'selection_date': self._get_timestamp(),
                    'performance_metrics': model_info['performance'],
                    'selection_reason': model_info['selection_reason'],
                    'all_fold_comparison': [
                        {
                            'fold': fold['fold'],
                            'r2': fold['r2'],
                            'val_loss': fold['val_loss'],
                            'composite_score': fold['composite_score']
                        }
                        for fold in model_info['all_fold_scores']
                    ]
                }
                
                with open(curve_dir / "selection_report.json", 'w') as f:
                    json.dump(selection_report, f, indent=2, default=str)
                
                # Create markdown report
                self._create_markdown_report(curve_dir, selection_report)
                
                success_count += 1
                logger.info(f"      ✅ {curve} production model created successfully")
                
            except Exception as e:
                logger.error(f"      ❌ Failed to create {curve} production model: {e}")
        
        logger.info(f"🎉 Production models created: {success_count}/{len(best_models)} successful")
        return success_count == len(best_models)
    
    def _create_markdown_report(self, curve_dir: Path, report: Dict):
        """Create human-readable markdown report"""
        md_content = f"""# {report['curve']} Production Model Selection Report

**Selection Date**: {report['selection_date']}
**Selected Fold**: {report['selected_fold']}
**Source Path**: `{report['source_path']}`

## Performance Metrics

- **R² Score**: {report['performance_metrics']['r2']:.6f}
- **Validation Loss**: {report['performance_metrics']['val_loss']:.6f}
- **Stability Score**: {report['performance_metrics']['stability_score']:.6f}
- **Composite Score**: {report['performance_metrics']['composite_score']:.6f}

## Selection Reason

**{report['selection_reason']}**

## All Fold Comparison

| Fold | R² Score | Val Loss | Composite Score | Status |
|------|----------|----------|-----------------|---------|
"""
        
        selected_fold = report['selected_fold']
        for fold_data in report['all_fold_comparison']:
            status = "🏆 **SELECTED**" if fold_data['fold'] == selected_fold else ""
            md_content += f"| {fold_data['fold']} | {fold_data['r2']:.6f} | {fold_data['val_loss']:.6f} | {fold_data['composite_score']:.6f} | {status} |\n"
        
        md_content += f"""
## Model Usage

### For Prediction Scripts:
```python
model_path = "production_models/{report['curve'].lower()}_production_model/best_model.pth"
config_path = "production_models/{report['curve'].lower()}_production_model/training_config.json"
```

### For Validation Scripts:
```python
production_models_dir = Path("production_models")
{report['curve'].lower()}_model = production_models_dir / "{report['curve'].lower()}_production_model"
```

## Quality Assessment

"""
        
        # Quality assessment based on performance
        r2 = report['performance_metrics']['r2']
        if r2 > 0.8:
            md_content += "🟢 **EXCELLENT**: R² > 0.8 - Ready for production deployment\n"
        elif r2 > 0.6:
            md_content += "🟡 **GOOD**: R² > 0.6 - Acceptable for production with monitoring\n"
        elif r2 > 0.3:
            md_content += "🟠 **MODERATE**: R² > 0.3 - Consider additional training or feature engineering\n"
        else:
            md_content += "🔴 **POOR**: R² < 0.3 - Not recommended for production use\n"
        
        with open(curve_dir / "selection_report.md", 'w') as f:
            f.write(md_content)
    
    def _get_timestamp(self) -> str:
        """Get current timestamp"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    
    def update_prediction_scripts(self) -> bool:
        """Update prediction scripts to use production models"""
        logger.info("🔧 Updating prediction script configuration...")
        
        try:
            # Create configuration file for updated scripts
            config = {
                "model_selection_date": self._get_timestamp(),
                "model_source": "enhanced_training_cross_validation",
                "production_models_directory": str(self.production_dir),
                "available_models": [],
                "usage_instructions": {
                    "prediction_script": "Set trained_models_dir = Path('production_models')",
                    "validation_script": "Set models_dir = Path('production_models')"
                }
            }
            
            # List available production models
            if self.production_dir.exists():
                for model_dir in self.production_dir.iterdir():
                    if model_dir.is_dir():
                        curve = model_dir.name.replace('_production_model', '').upper()
                        config["available_models"].append({
                            "curve": curve,
                            "directory": str(model_dir),
                            "model_file": str(model_dir / "best_model.pth"),
                            "config_file": str(model_dir / "training_config.json")
                        })
            
            # Save configuration
            config_file = Path("production_model_config.json")
            with open(config_file, 'w') as f:
                json.dump(config, f, indent=2)
            
            logger.info(f"   ✅ Configuration saved: {config_file}")
            
            # Create update instructions
            instructions = f"""
# Production Model Update Instructions

## For Prediction Scripts
Update the `__init__` method in `ImprovedMultiCurvePredictor`:

```python
def __init__(self, device):
    self.device = device
    self.normalizer = VpDataNormalizer()
    # UPDATED: Use production models instead of old training outputs
    self.trained_models_dir = Path("production_models")  # Changed from production_training_outputs
    self.available_models = self._check_available_models()
```

## For Validation Scripts
Update the default models directory:

```python
def main():
    # UPDATED: Use production models directory
    config = {{
        'models_dir': 'production_models',  # Changed from production_training_outputs
        'output_dir': args.output_dir
    }}
```

## Model Loading Pattern
Both scripts should now look for models in this structure:
```
production_models/
├── ac_production_model/best_model.pth
├── den_production_model/best_model.pth
├── cnl_production_model/best_model.pth
├── gr_production_model/best_model.pth
└── rlld_production_model/best_model.pth
```
"""
            
            with open("PRODUCTION_MODEL_UPDATE_INSTRUCTIONS.md", 'w') as f:
                f.write(instructions)
            
            logger.info("   ✅ Update instructions created: PRODUCTION_MODEL_UPDATE_INSTRUCTIONS.md")
            
            return True
            
        except Exception as e:
            logger.error(f"   ❌ Failed to update prediction scripts: {e}")
            return False
    
    def generate_summary_report(self, best_models: Dict):
        """Generate comprehensive summary report"""
        logger.info("📋 Generating summary report...")
        
        report = f"""# Production Model Selection Summary

**Generated**: {self._get_timestamp()}
**Source**: Enhanced Training Cross-Validation Results
**Method**: Multi-criteria selection (R², Validation Loss, Stability)

## Selected Production Models

| Curve | Selected Fold | R² Score | Val Loss | Selection Reason | Quality |
|-------|---------------|----------|----------|------------------|---------|
"""
        
        for curve, model_info in best_models.items():
            r2 = model_info['performance']['r2']
            val_loss = model_info['performance']['val_loss']
            reason = model_info['selection_reason']
            
            if r2 > 0.8:
                quality = "🟢 Excellent"
            elif r2 > 0.6:
                quality = "🟡 Good"
            elif r2 > 0.3:
                quality = "🟠 Moderate"
            else:
                quality = "🔴 Poor"
            
            report += f"| {curve} | {model_info['selected_fold']} | {r2:.6f} | {val_loss:.6f} | {reason} | {quality} |\n"
        
        report += f"""

## Production Readiness Assessment

### Ready for Production (R² > 0.6):
"""
        ready_models = [curve for curve, info in best_models.items() 
                       if info['performance']['r2'] > 0.6]
        if ready_models:
            report += "- " + "\n- ".join(ready_models) + "\n"
        else:
            report += "- None\n"
        
        report += """
### Need Improvement (R² < 0.6):
"""
        improvement_models = [curve for curve, info in best_models.items() 
                            if info['performance']['r2'] <= 0.6]
        if improvement_models:
            report += "- " + "\n- ".join(improvement_models) + "\n"
        else:
            report += "- None\n"
        
        report += f"""

## Directory Structure Created

```
production_models/
"""
        for curve in best_models.keys():
            report += f"├── {curve.lower()}_production_model/\n"
            report += f"│   ├── best_model.pth\n"
            report += f"│   ├── training_config.json\n"
            report += f"│   ├── training_history.json\n"
            report += f"│   ├── selection_report.json\n"
            report += f"│   └── selection_report.md\n"
        
        report += """```

## Next Steps

1. **Update Prediction Scripts**: Change `trained_models_dir` to `production_models`
2. **Update Validation Scripts**: Change default `models_dir` to `production_models`
3. **Test Production Models**: Run prediction demos with new models
4. **Monitor Performance**: Set up production monitoring for model performance
5. **Model Versioning**: Consider implementing model versioning for future updates

## Files Created

- `production_model_config.json` - Configuration for updated scripts
- `PRODUCTION_MODEL_UPDATE_INSTRUCTIONS.md` - Detailed update instructions
- Individual selection reports in each model directory
"""
        
        summary_file = Path("PRODUCTION_MODEL_SELECTION_SUMMARY.md")
        with open(summary_file, 'w') as f:
            f.write(report)
        
        logger.info(f"   ✅ Summary report saved: {summary_file}")
    
    def run_complete_selection_process(self):
        """Run the complete model selection and production setup process"""
        logger.info("🚀 Starting Complete Production Model Selection Process")
        logger.info("="*80)
        
        try:
            # Step 1: Analyze cross-validation results
            analysis = self.analyze_cross_validation_results()
            if not analysis:
                logger.error("❌ No analysis results available")
                return False
            
            # Step 2: Select best models
            best_models = self.select_best_models(analysis)
            if not best_models:
                logger.error("❌ No best models selected")
                return False
            
            # Step 3: Create production models
            success = self.create_production_models(best_models)
            if not success:
                logger.error("❌ Failed to create all production models")
                return False
            
            # Step 4: Update script configurations
            self.update_prediction_scripts()
            
            # Step 5: Generate summary report
            self.generate_summary_report(best_models)
            
            logger.info("="*80)
            logger.info("🎉 Production Model Selection Process Completed Successfully!")
            logger.info(f"📁 Production models available in: {self.production_dir}")
            logger.info("📋 Check PRODUCTION_MODEL_SELECTION_SUMMARY.md for details")
            logger.info("🔧 Follow PRODUCTION_MODEL_UPDATE_INSTRUCTIONS.md to update scripts")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Production model selection process failed: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """Main function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Production Model Selector")
    parser.add_argument('--enhanced-dir', type=str, 
                       default='enhanced_training_outputs',
                       help='Directory containing enhanced training results')
    parser.add_argument('--production-dir', type=str,
                       default='production_models', 
                       help='Target directory for production models')
    
    args = parser.parse_args()
    
    # Create and run selector
    selector = ProductionModelSelector(args.enhanced_dir)
    selector.production_dir = Path(args.production_dir)
    
    success = selector.run_complete_selection_process()
    
    if success:
        print("\n🎉 SUCCESS: Production models ready for deployment!")
        exit(0)
    else:
        print("\n❌ FAILED: Production model selection failed!")
        exit(1)


if __name__ == "__main__":
    main()
