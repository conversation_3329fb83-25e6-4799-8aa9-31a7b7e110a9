"""
Comprehensive Well Log Prediction Test Examples
Demonstrates current VpTransformer capabilities and future multi-curve prediction scenarios

This script shows:
1. Current Vp prediction using VpTransformer with A1.hdf5 and A2.hdf5
2. Conceptual density prediction (showing how it would work with GeneralTransformer)
3. Conceptual RLLD prediction (showing how it would work with GeneralTransformer)
"""
import os
import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt
from typing import Dict, Tuple, Optional

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import VpDataNormalizer, VpDataset, create_improved_vp_data
    from vp_predictor import MWLT_Vp_Base, VpLoss
    from vp_predictor.las_processor import LASProcessor
    from vp_predictor.utils import get_device, cal_RMSE, cal_R2
    import torch
    import torch.nn as nn
    from torch.utils.data import DataLoader
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    print("Please ensure the vp_predictor package is properly installed")
    sys.exit(1)

class WellLogDataLoader:
    """Utility class to load and process HDF5 well log data"""
    
    def __init__(self):
        self.processor = LASProcessor()
        self.normalizer = VpDataNormalizer()
    
    def load_hdf5_data(self, file_path: str) -> Dict[str, np.ndarray]:
        """Load well log data from HDF5 file"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Data file not found: {file_path}")
        
        curves = {}
        with h5py.File(file_path, 'r') as f:
            print(f"📂 Loading data from: {os.path.basename(file_path)}")
            print(f"   Available curves: {list(f.keys())}")
            
            for curve_name in f.keys():
                data = f[curve_name][:]
                if data.ndim > 1:
                    data = data.squeeze()
                curves[curve_name] = data
                print(f"   {curve_name}: {data.shape}, range [{data.min():.2f}, {data.max():.2f}]")
        
        return curves
    
    def prepare_vp_prediction_data(self, curves: Dict[str, np.ndarray], 
                                   sequence_length: int = 720) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare data for Vp prediction using current VpTransformer"""
        # Extract input features (GR, CNL, DEN, RLLD) and target (AC)
        input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        target_curve = 'AC'
        
        # Resample to target length
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves:
                data = self._resample_curve(curves[curve_name], sequence_length)
                input_features.append(data)
            else:
                print(f"⚠️  Missing curve {curve_name}, using zeros")
                input_features.append(np.zeros(sequence_length))
        
        # Target data
        if target_curve in curves:
            target_data = self._resample_curve(curves[target_curve], sequence_length)
        else:
            print(f"⚠️  Missing target curve {target_curve}")
            target_data = np.zeros(sequence_length)
        
        return np.array(input_features), target_data
    
    def _resample_curve(self, data: np.ndarray, target_length: int) -> np.ndarray:
        """Resample curve data to target length using interpolation"""
        if len(data) == target_length:
            return data
        
        original_indices = np.linspace(0, len(data) - 1, len(data))
        target_indices = np.linspace(0, len(data) - 1, target_length)
        resampled = np.interp(target_indices, original_indices, data)
        
        return resampled

def test_case_1_vp_prediction():
    """
    Test Case 1: Sonic Velocity (Vp) Prediction using current VpTransformer
    Uses A1.hdf5 and A2.hdf5 as input data
    """
    print("\n" + "="*80)
    print("TEST CASE 1: SONIC VELOCITY (Vp) PREDICTION")
    print("="*80)
    print("Using current VpTransformer architecture")
    print("Input: GR, CNL, DEN, RLLD → Output: AC (Acoustic/Vp)")
    
    # Initialize data loader
    data_loader = WellLogDataLoader()
    device = get_device(device_id=0)
    
    # Load data from both files
    try:
        # Load A1.hdf5
        print(f"\n📊 Processing A1.hdf5...")
        curves_a1 = data_loader.load_hdf5_data("examples/A1.hdf5")
        input_a1, target_a1 = data_loader.prepare_vp_prediction_data(curves_a1)
        
        # Load A2.hdf5
        print(f"\n📊 Processing A2.hdf5...")
        curves_a2 = data_loader.load_hdf5_data("examples/A2.hdf5")
        input_a2, target_a2 = data_loader.prepare_vp_prediction_data(curves_a2)
        
        print(f"\n📈 Data Summary:")
        print(f"   A1 - Input shape: {input_a1.shape}, Target shape: {target_a1.shape}")
        print(f"   A2 - Input shape: {input_a2.shape}, Target shape: {target_a2.shape}")
        
        # Create model (demonstration - would need trained weights for actual prediction)
        print(f"\n🏗️  Creating VpTransformer model...")
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(device)
        
        # Model info
        param_count = sum(p.numel() for p in model.parameters())
        print(f"   Model parameters: {param_count:,}")
        print(f"   Device: {device}")
        
        # Demonstrate forward pass with synthetic prediction
        print(f"\n🔮 Demonstrating prediction process...")
        model.eval()
        with torch.no_grad():
            # Convert to tensors
            input_tensor_a1 = torch.FloatTensor(input_a1).unsqueeze(0).to(device)
            input_tensor_a2 = torch.FloatTensor(input_a2).unsqueeze(0).to(device)
            
            # Forward pass (random weights - for demonstration only)
            pred_a1 = model(input_tensor_a1)
            pred_a2 = model(input_tensor_a2)
            
            # Convert back to numpy
            pred_a1_np = pred_a1.squeeze().cpu().numpy()
            pred_a2_np = pred_a2.squeeze().cpu().numpy()
            
            print(f"✅ Prediction completed!")
            print(f"   A1 prediction range: {pred_a1_np.min():.1f} - {pred_a1_np.max():.1f}")
            print(f"   A2 prediction range: {pred_a2_np.min():.1f} - {pred_a2_np.max():.1f}")
            
            # Compare with actual values
            print(f"\n📊 Actual vs Predicted (A1):")
            print(f"   Actual AC range: {target_a1.min():.1f} - {target_a1.max():.1f} μs/ft")
            print(f"   Predicted range: {pred_a1_np.min():.1f} - {pred_a1_np.max():.1f} μs/ft")
            
            print(f"\n📊 Actual vs Predicted (A2):")
            print(f"   Actual AC range: {target_a2.min():.1f} - {target_a2.max():.1f} μs/ft")
            print(f"   Predicted range: {pred_a2_np.min():.1f} - {pred_a2_np.max():.1f} μs/ft")
            
            # Note about training
            print(f"\n⚠️  NOTE: This uses untrained model weights for demonstration.")
            print(f"   For actual predictions, you would need to:")
            print(f"   1. Train the model using create_improved_vp_data()")
            print(f"   2. Save the trained model checkpoint")
            print(f"   3. Load the checkpoint for inference")
        
        return {
            'model': model,
            'predictions': {'A1': pred_a1_np, 'A2': pred_a2_np},
            'targets': {'A1': target_a1, 'A2': target_a2},
            'inputs': {'A1': input_a1, 'A2': input_a2}
        }
        
    except Exception as e:
        print(f"❌ Test Case 1 failed: {e}")
        return None

def test_case_2_density_prediction_concept():
    """
    Test Case 2: Density Prediction (Conceptual)
    Shows how GeneralTransformer would handle density prediction
    Input: GR, CNL, AC, RLLD → Output: DEN
    """
    print("\n" + "="*80)
    print("TEST CASE 2: DENSITY PREDICTION (CONCEPTUAL)")
    print("="*80)
    print("Demonstrating how GeneralTransformer would work for density prediction")
    print("Input: GR, CNL, AC, RLLD → Output: DEN")
    
    # Load data
    data_loader = WellLogDataLoader()
    
    try:
        print(f"\n📊 Loading data for density prediction concept...")
        curves_a1 = data_loader.load_hdf5_data("examples/A1.hdf5")
        
        # For density prediction, we'd use different input curves
        input_curves = ['GR', 'CNL', 'AC', 'RLLD']  # AC as input, DEN as target
        target_curve = 'DEN'
        
        print(f"\n🔄 Conceptual data preparation for density prediction:")
        print(f"   Input curves: {input_curves}")
        print(f"   Target curve: {target_curve}")
        
        # Show how data would be prepared
        sequence_length = 720
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves_a1:
                data = data_loader._resample_curve(curves_a1[curve_name], sequence_length)
                input_features.append(data)
                print(f"   {curve_name}: range [{data.min():.2f}, {data.max():.2f}]")
        
        target_data = data_loader._resample_curve(curves_a1[target_curve], sequence_length)
        print(f"   {target_curve} (target): range [{target_data.min():.2f}, {target_data.max():.2f}] g/cm³")
        
        input_array = np.array(input_features)
        print(f"\n📈 Prepared data shape: {input_array.shape}")
        
        print(f"\n🏗️  Conceptual GeneralTransformer configuration:")
        print(f"   Input channels: 4 (GR, CNL, AC, RLLD)")
        print(f"   Output channels: 1 (DEN)")
        print(f"   Decoder activation: sigmoid (for bounded density values)")
        print(f"   Normalization: DEN mean=2.5, std=0.3, range=[1.5, 3.0]")
        
        print(f"\n✅ Density prediction concept demonstrated!")
        print(f"   This would require training a new model or using GeneralTransformer")
        
        return {
            'input_shape': input_array.shape,
            'target_shape': target_data.shape,
            'target_range': (target_data.min(), target_data.max())
        }
        
    except Exception as e:
        print(f"❌ Test Case 2 failed: {e}")
        return None

def test_case_3_rlld_prediction_concept():
    """
    Test Case 3: Resistivity (RLLD) Prediction (Conceptual)
    Shows how GeneralTransformer would handle resistivity prediction
    Input: GR, CNL, DEN, AC → Output: RLLD
    """
    print("\n" + "="*80)
    print("TEST CASE 3: RESISTIVITY (RLLD) PREDICTION (CONCEPTUAL)")
    print("="*80)
    print("Demonstrating how GeneralTransformer would work for resistivity prediction")
    print("Input: GR, CNL, DEN, AC → Output: RLLD")
    
    # Load data
    data_loader = WellLogDataLoader()
    
    try:
        print(f"\n📊 Loading data for resistivity prediction concept...")
        curves_a2 = data_loader.load_hdf5_data("examples/A2.hdf5")
        
        # For resistivity prediction, we'd use different input curves
        input_curves = ['GR', 'CNL', 'DEN', 'AC']  # RLLD as target
        target_curve = 'RLLD'
        
        print(f"\n🔄 Conceptual data preparation for resistivity prediction:")
        print(f"   Input curves: {input_curves}")
        print(f"   Target curve: {target_curve}")
        
        # Show how data would be prepared
        sequence_length = 720
        input_features = []
        for curve_name in input_curves:
            if curve_name in curves_a2:
                data = data_loader._resample_curve(curves_a2[curve_name], sequence_length)
                input_features.append(data)
                print(f"   {curve_name}: range [{data.min():.2f}, {data.max():.2f}]")
        
        target_data = data_loader._resample_curve(curves_a2[target_curve], sequence_length)
        print(f"   {target_curve} (target): range [{target_data.min():.2f}, {target_data.max():.2f}] ohm-m")
        
        input_array = np.array(input_features)
        print(f"\n📈 Prepared data shape: {input_array.shape}")
        
        print(f"\n🏗️  Conceptual GeneralTransformer configuration:")
        print(f"   Input channels: 4 (GR, CNL, DEN, AC)")
        print(f"   Output channels: 1 (RLLD)")
        print(f"   Preprocessing: log10 transform for resistivity")
        print(f"   Normalization: log-scale, mean=1.0, std=2.0")
        print(f"   Decoder activation: none (log-transformed output)")
        
        # Show log transformation concept
        log_target = np.log10(np.clip(target_data, 0.1, None))
        print(f"\n📊 Log-transformed RLLD:")
        print(f"   Original range: [{target_data.min():.2f}, {target_data.max():.2f}] ohm-m")
        print(f"   Log10 range: [{log_target.min():.2f}, {log_target.max():.2f}]")
        
        print(f"\n✅ Resistivity prediction concept demonstrated!")
        print(f"   This would require training a new model or using GeneralTransformer")
        
        return {
            'input_shape': input_array.shape,
            'target_shape': target_data.shape,
            'target_range': (target_data.min(), target_data.max()),
            'log_target_range': (log_target.min(), log_target.max())
        }
        
    except Exception as e:
        print(f"❌ Test Case 3 failed: {e}")
        return None

def main():
    """Run all test cases"""
    print("🚀 COMPREHENSIVE WELL LOG PREDICTION TEST")
    print("="*80)
    print("Testing current VpTransformer and conceptual multi-curve predictions")
    
    # Run test cases
    results = {}
    
    # Test Case 1: Current Vp prediction
    results['vp_prediction'] = test_case_1_vp_prediction()
    
    # Test Case 2: Conceptual density prediction
    results['density_concept'] = test_case_2_density_prediction_concept()
    
    # Test Case 3: Conceptual resistivity prediction
    results['rlld_concept'] = test_case_3_rlld_prediction_concept()
    
    # Summary
    print("\n" + "="*80)
    print("🎉 TEST SUMMARY")
    print("="*80)
    
    success_count = sum(1 for result in results.values() if result is not None)
    print(f"✅ Successful tests: {success_count}/3")
    
    if results['vp_prediction']:
        print(f"✅ Vp Prediction: Working with current VpTransformer")
    else:
        print(f"❌ Vp Prediction: Failed")
    
    if results['density_concept']:
        print(f"💡 Density Prediction: Concept demonstrated")
    else:
        print(f"❌ Density Prediction: Concept failed")
    
    if results['rlld_concept']:
        print(f"💡 RLLD Prediction: Concept demonstrated")
    else:
        print(f"❌ RLLD Prediction: Concept failed")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"1. Train VpTransformer model using the provided training data")
    print(f"2. Implement GeneralTransformer following the refactoring plan")
    print(f"3. Train models for density and resistivity prediction")
    print(f"4. Test multi-curve prediction capabilities")
    
    print(f"\n📚 REFERENCES:")
    print(f"- See refactor_general.md for GeneralTransformer implementation plan")
    print(f"- Current VpTransformer in vp_predictor/vp_model_improved.py")
    print(f"- Training data creation in vp_predictor/vp_model_improved.py:create_improved_vp_data()")

if __name__ == "__main__":
    main()
