# Fixing Validation vs Training Mismatch for Multi‑Curve (Multiwell)

This note explains why the validation suite currently reports lower R² than the training output and proposes targeted improvements to both the validator and the training loop. The goal is to ensure apples‑to‑apples evaluation, clearer reporting, and more stable generalization.

---

## Diagnosis: Why validation R² looks worse

- Cross‑validation split mismatch
  - Training uses sequential, contiguous folds (blocked splits), preserving depth/time structure.
  - Validation uses shuffled KFold over raw indices and then slices sequences from a scrambled series. This breaks temporal continuity and harms a transformer trained on contiguous sequences.

- Fold/model mismatch
  - Training saves one model per fold (e.g., enhanced_training_outputs/AC_fold_0/best_model.pth ... fold_4).
  - Validation loads only the first model it finds for a target (prefers fold_0) and evaluates it across all folds. This is not the same fold split and may over/underestimate performance.

- Normalization and metric scale
  - Training and validation both operate on normalized targets, but the validator reports only normalized‑scale R². For AC we can denormalize with VpDataNormalizer; for other curves we currently can’t because their target normalization is per‑sequence z‑score computed inside the dataset. This reduces interpretability and can cause confusion when comparing to physical‑unit expectations.

- Dataset fallback inconsistency
  - If importing CurveSpecificDataset fails, the validator silently falls back to a different dataset creation path with different normalization/quality filters. This further skews comparison.

---

## Improvements to the validation suite (recommended changes)

1) Use the same sequential folds as training
- Replace the shuffled KFold with contiguous range splits identical to EnhancedTrainingPipeline.create_cross_validation_folds.
- This preserves sequence continuity and matches the training distribution.

Example change in ModelValidator.perform_cross_validation:

```python
# BEFORE
kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)
for fold_idx, (train_idx, val_idx) in enumerate(kfold.split(range(n_samples))):
    ...

# AFTER (sequential blocked splits)
fold_size = n_samples // n_folds
folds = []
for fold_idx in range(n_folds):
    val_start = fold_idx * fold_size
    val_end = (fold_idx + 1) * fold_size if fold_idx < n_folds - 1 else n_samples
    val_idx = np.arange(val_start, val_end)
    train_idx = np.concatenate([np.arange(0, val_start), np.arange(val_end, n_samples)])
    folds.append((train_idx, val_idx))

for fold_idx, (train_idx, val_idx) in enumerate(folds):
    ...
```

2) Evaluate each fold with its corresponding fold model
- When scanning enhanced_training_outputs, collect all fold directories for a target (AC_fold_0..4, etc.).
- During cross‑validation, load the fold‑specific model for fold_idx if available; otherwise fallback to a default.

Example loading strategy in ModelValidator.load_trained_models:

```python
# Pseudocode: build per‑target dict of fold -> model
models = defaultdict(dict)
for model_dir in enhanced_training_outputs.iterdir():
    if not model_dir.is_dir():
        continue
    name = model_dir.name  # e.g., AC_fold_2
    if "_fold_" in name.lower():
        curve, _, fold_str = name.rpartition("_fold_")
        fold_idx = int(fold_str)
        models[curve.upper()][fold_idx] = {
            'model_path': model_dir/"best_model.pth",
            'config': json.load(open(model_dir/"training_config.json")),
            'directory': model_dir,
        }
# return: { 'AC': {0: {...}, 1: {...}, ...}, 'DEN': {...}, ... }
```

Then in perform_cross_validation, for each fold_idx use models[target_curve].get(fold_idx) if present.

3) Always use the exact same dataset logic as training
- Require importing CurveSpecificDataset; if it fails, raise to avoid silently switching to a different preprocessing pipeline.
- Pass the same sequence_length and keep the same overlap (step=64 inside the dataset).

```python
try:
    from multiwell.training.enhanced_multi_curve_training import CurveSpecificDataset
except Exception as e:
    raise RuntimeError("CurveSpecificDataset must be available for consistent validation") from e
```

4) Report metrics in normalized and physical units (when possible)
- For AC: compute R² in original units using VpDataNormalizer.denormalize_vp for both predictions and targets.
- Keep the normalized R² too so you can compare against training logs.

```python
# After you have predictions, actuals (normalized)
if target_curve == 'AC':
    preds_phys = self.normalizer.denormalize_vp(torch.tensor(predictions)).numpy()
    acts_phys  = self.normalizer.denormalize_vp(torch.tensor(actuals)).numpy()
    r2_phys = r2_score(acts_phys, preds_phys)
    # Include r2_phys in fold_result and in summary_statistics/report
```

- For non‑AC targets, add a TODO/note in the report explaining that denormalization requires storing target scaling (mean/std) used per sequence (see Training improvements below). Once available, you can compute physical‑unit R² the same way.

5) Add a “Consistency Check vs Training” section in the report
- Read training_history.json from each fold directory.
- Compare the fold’s best val_r2 from training with the validation R² now computed on the exact same fold. If the gap is large, flag it.

6) Better uncertainty estimation: ensemble of folds
- In estimate_prediction_uncertainty, instead of MC dropout (which depends on model having dropout layers), load all fold models for a target and compute ensemble predictions over the same inputs. Use their variance/standard deviation as epistemic uncertainty. Keep MC dropout as optional.

```python
# Pseudocode inside estimate_prediction_uncertainty
fold_models = models_by_curve[target_curve]  # dict: fold_idx -> model
preds_per_fold = []
for fold_idx, m in fold_models.items():
    model = self.create_model_instance(...)
    self.load_model_weights(model, m['model_path'])
    preds, acts = self._predict_on_dataset(model, test_dataset)
    preds_per_fold.append(preds)
# uncertainty = std across folds at each point
preds_stack = np.stack(preds_per_fold, axis=0)
std_across_models = preds_stack.std(axis=0)
```

7) Improve plots and JSON artifacts
- In the visualization, add both normalized and physical‑unit R² (for AC) in the figure text.
- Save per‑fold CSV/NPY snippets of predictions vs actuals for quick inspection.
- Extend comprehensive_validation_results.json to include r2_phys (AC), and to record which fold model was used per fold.

---

## Improvements to the training pipeline (to support robust validation)

1) Persist fold boundaries and indices
- When creating folds in create_cross_validation_folds, write a fold_splits.json next to outputs:

```json
{
  "n_folds": 5,
  "total_length": N,
  "folds": [
    {"fold": 0, "val_start": s0, "val_end": e0},
    ...
  ]
}
```

- The validator can read this and guarantee identical splits.

2) Persist target normalization to enable physical‑unit metrics for all curves
- Instead of per‑sequence z‑score inside the dataset (which cannot be inverted later), compute curve‑specific scalers from the training fold only (e.g., mean/std for CNL, DEN, GR; log+z for RLLD), store them in training_config.json, and apply the same scaler in both training and validation.
- Update CurveSpecificDataset to accept an optional target_scaler per curve and use it; return targets normalized with this scaler. Then you can denormalize during reporting for every curve.

3) Add learning‑rate scheduling
- Add ReduceLROnPlateau(monitor=val_loss, factor=0.5, patience=10, min_lr=1e-6) to improve convergence and reduce overfitting.

4) Consider early‑stopping tie‑breaker on R²
- Keep primary monitor on val_loss for stability, but if val_loss plateaus within delta, prefer checkpoints with higher val_r2.
- One easy approach is to keep early stopping on loss, but also save a best_by_r2 checkpoint for diagnostics.

5) Batch‑size and overlap control
- Current step_size=64 creates many highly correlated windows. This is fine for training, but for validation it inflates effective sample count. Consider using a larger step_size (e.g., 128–256) during validation-only datasets to reduce correlation when computing metrics.

6) Group‑wise cross‑validation when well IDs are available
- If wells can be identified, prefer GroupKFold (group=well_id) to avoid cross‑well leakage. Persist well IDs and use group splits both for training and validation.

---

## Minimal patch sketch (validator) – sequential folds + per‑fold models + AC physical R²

Below is a compact sketch showing how to wire three key changes. Adapt to your codebase as needed.

```python
# In load_trained_models: collect per‑curve, per‑fold
from collections import defaultdict
models = defaultdict(dict)
...
if "_fold_" in model_dir.name.lower():
    curve = model_dir.name.split("_fold_")[0].upper()
    fold_idx = int(model_dir.name.split("_fold_")[1])
    models[curve][fold_idx] = {
        'model_path': model_file,
        'config_path': config_file,
        'config': model_config,
        'directory': model_dir,
    }
...
return models

# In perform_cross_validation: sequential folds + fold‑specific model
fold_size = n_samples // n_folds
folds = [(i*fold_size, (i+1)*fold_size if i<n_folds-1 else n_samples) for i in range(n_folds)]
for fold_idx, (s, e) in enumerate(folds):
    val_idx = np.arange(s, e)
    train_idx = np.concatenate([np.arange(0, s), np.arange(e, n_samples)])
    train_data = {k: v[train_idx] for k, v in data_dict.items()}
    val_data   = {k: v[val_idx]   for k, v in data_dict.items()}

    # Dataset (must match training)
    val_dataset = CurveSpecificDataset(...)

    # Load fold‑specific model if available
    model_info_fold = None
    if isinstance(model_info, dict) and target_curve in model_info and isinstance(model_info[target_curve], dict):
        model_info_fold = model_info[target_curve].get(fold_idx)

    model = self.create_model_instance(target_curve, input_curves).to(self.device)
    if model_info_fold and self.load_model_weights(model, model_info_fold['model_path']):
        logger.info(f"Loaded {target_curve} fold {fold_idx}")
    elif isinstance(model_info, dict) and 'model_path' in model_info and self.load_model_weights(model, model_info['model_path']):
        logger.info("Loaded default model")

    preds, acts = self._predict_on_dataset(model, val_dataset)

    r2 = r2_score(acts, preds)
    r2_phys = None
    if target_curve == 'AC':
        preds_phys = self.normalizer.denormalize_vp(torch.tensor(preds)).numpy()
        acts_phys  = self.normalizer.denormalize_vp(torch.tensor(acts)).numpy()
        r2_phys = r2_score(acts_phys, preds_phys)

    fold_results.append({
        'fold': fold_idx+1,
        'r2': r2,
        'r2_phys': r2_phys,
        ...
    })
```

Note: To pass the per‑curve per‑fold model mapping into perform_cross_validation, you can change run_comprehensive_validation to pass the whole nested dict from load_trained_models.

---

## Expected results after changes

- Validation R² should closely track training R² per fold because:
  - The same fold boundaries are used.
  - The correct fold model is evaluated on its own hold‑out segment.
  - Temporal continuity is preserved in the validation sequences.
- The report will better communicate performance:
  - Normalized R² for parity with training logs.
  - Physical‑unit R² at least for AC (and for all curves once scalers are persisted).
  - Clear consistency checks between training‑time metrics and post‑hoc validation.
- Uncertainty estimates become more meaningful via fold‑ensemble variance (with optional MC dropout).

---

## Prioritized action items

1) Validator
- Switch to sequential folds (blocked splits) and use fold‑specific models per fold.
- Enforce using CurveSpecificDataset; fail fast on fallback.
- Add AC physical‑unit R²; extend JSON/report accordingly.

2) Training
- Save fold_splits.json and curve‑specific target scalers per fold in training_config.json.
- Optionally add ReduceLROnPlateau and a “best_by_r2” checkpoint.

3) Nice‑to‑have
- Fold‑ensemble uncertainty.
- Per‑fold predictions vs actuals artifacts for quick debugging.

Once these are in place, you should see more faithful validation metrics and clearer, more actionable reports.
