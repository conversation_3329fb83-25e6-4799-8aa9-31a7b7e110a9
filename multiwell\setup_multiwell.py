#!/usr/bin/env python3
"""
MWLT MultiWell Setup Script
Initialize the organized multiwell environment
"""

import os
import sys
from pathlib import Path

def check_dependencies():
    """Check if required dependencies are available"""
    print("🔍 Checking Dependencies...")
    
    required_packages = [
        ('torch', 'PyTorch'),
        ('numpy', 'NumPy'),
        ('matplotlib', 'Matplotlib'),
        ('sklearn', 'Scikit-learn'),
        ('scipy', 'SciPy')
    ]
    
    missing = []
    for package, name in required_packages:
        try:
            __import__(package)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} (missing)")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️  Missing packages: {', '.join(missing)}")
        print("Install with: pip install -r requirements.txt")
        return False
    
    return True

def check_vp_predictor():
    """Check if vp_predictor package is accessible"""
    print("\n🔍 Checking MWLT Core Package...")
    
    # Add parent directory to path to access vp_predictor
    parent_dir = Path(__file__).parent.parent
    sys.path.insert(0, str(parent_dir))
    
    try:
        import vp_predictor
        print("   ✅ vp_predictor package accessible")
        
        # Check key components
        from vp_predictor import VpDataNormalizer
        print("   ✅ VpDataNormalizer")
        
        from vp_predictor.vp_model_improved import MWLT_Vp_Base
        print("   ✅ MWLT_Vp_Base model")
        
        from vp_predictor.core.dataset import GeneralWellLogDataset
        print("   ✅ GeneralWellLogDataset")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ vp_predictor package not accessible: {e}")
        return False

def check_directory_structure():
    """Verify the multiwell directory structure"""
    print("\n🔍 Checking Directory Structure...")
    
    base_dir = Path(__file__).parent
    expected_dirs = ['training', 'prediction', 'validation', 'utils', 'demos']
    expected_files = [
        'training/enhanced_multi_curve_training.py',
        'validation/model_validation_suite.py',
        'prediction/improved_multi_curve_prediction_demo.py',
        'demos/demo_curve_prediction.py',
        'utils/simple_test.py'
    ]
    
    # Check directories
    for dir_name in expected_dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            print(f"   ✅ {dir_name}/")
        else:
            print(f"   ❌ {dir_name}/ (missing)")
    
    # Check key files
    for file_path in expected_files:
        full_path = base_dir / file_path
        if full_path.exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (missing)")

def create_output_directories():
    """Create output directories if they don't exist"""
    print("\n📁 Creating Output Directories...")
    
    base_dir = Path(__file__).parent.parent
    output_dirs = [
        'enhanced_training_outputs',
        'validation_outputs', 
        'prediction_outputs',
        'test_outputs'
    ]
    
    for dir_name in output_dirs:
        dir_path = base_dir / dir_name
        if not dir_path.exists():
            dir_path.mkdir(exist_ok=True)
            print(f"   ✅ Created {dir_name}/")
        else:
            print(f"   ℹ️  {dir_name}/ already exists")

def run_quick_test():
    """Run a quick functionality test"""
    print("\n🧪 Running Quick Functionality Test...")
    
    try:
        # Test import paths
        parent_dir = Path(__file__).parent.parent
        sys.path.insert(0, str(parent_dir))
        
        from vp_predictor import VpDataNormalizer
        from vp_predictor.utils import get_device
        
        # Test basic functionality
        normalizer = VpDataNormalizer()
        device = get_device()
        
        print(f"   ✅ Normalizer created successfully")
        print(f"   ✅ Device detected: {device}")
        print("   ✅ Quick test passed!")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Quick test failed: {e}")
        return False

def show_next_steps():
    """Show next steps after setup"""
    print("\n" + "="*80)
    print("🎉 MWLT MultiWell Setup Complete!")
    print("="*80)
    print()
    print("🚀 Quick Start Options:")
    print()
    print("1. Launch Training Pipeline:")
    print("   cd training")
    print("   python enhanced_multi_curve_training.py")
    print()
    print("2. Run Model Validation:")
    print("   cd validation") 
    print("   python model_validation_suite.py")
    print()
    print("3. Test Predictions:")
    print("   cd prediction")
    print("   python improved_multi_curve_prediction_demo.py")
    print()
    print("4. Interactive Demos:")
    print("   cd demos")
    print("   python demo_curve_prediction.py")
    print()
    print("5. Use Launcher Script:")
    print("   python multiwell_launcher.py train")
    print("   python multiwell_launcher.py validate")
    print("   python multiwell_launcher.py predict")
    print()
    print("📚 For more details, see: README.md")
    print()

def main():
    """Main setup function"""
    print("="*80)
    print("🏗️  MWLT MultiWell Environment Setup")
    print("="*80)
    
    # Run all checks
    deps_ok = check_dependencies()
    core_ok = check_vp_predictor() 
    check_directory_structure()
    create_output_directories()
    test_ok = run_quick_test()
    
    # Summary
    print("\n" + "="*80)
    print("📋 Setup Summary:")
    print(f"   Dependencies: {'✅ OK' if deps_ok else '❌ Issues'}")
    print(f"   Core Package: {'✅ OK' if core_ok else '❌ Issues'}")
    print(f"   Quick Test: {'✅ Passed' if test_ok else '❌ Failed'}")
    
    if deps_ok and core_ok and test_ok:
        print("\n🎊 Setup completed successfully!")
        show_next_steps()
    else:
        print("\n⚠️  Setup completed with issues. Please address the problems above.")
        print("\nCommon solutions:")
        print("- Install missing packages: pip install -r requirements.txt")
        print("- Ensure you're in the correct directory")
        print("- Check that vp_predictor package is in parent directory")

if __name__ == "__main__":
    main()
