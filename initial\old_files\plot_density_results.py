"""
Enhanced plotting script for Density prediction results
Creates comprehensive visualizations similar to the Vp plotting functionality
"""
import matplotlib.pyplot as plt
import numpy as np
import os
import h5py
from datetime import datetime
from utils import cal_RMSE, cal_R2

def create_density_comparison_plots(results_dir="../result_base1/pred_val", save_dir="../density_prediction_results"):
    """
    Create comprehensive comparison plots for Density prediction results
    Similar to the Vp plotting functionality but for density predictions
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    if not os.path.exists(results_dir):
        print(f"❌ Results directory {results_dir} not found!")
        return None
    
    # Find all result files
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.hdf5')]
    
    if not result_files:
        print(f"❌ No result files found in {results_dir}!")
        return None
    
    print(f"📊 Found {len(result_files)} result files for plotting")
    
    # Load and process results
    results = []
    for result_file in result_files:
        file_path = os.path.join(results_dir, result_file)
        
        try:
            with h5py.File(file_path, 'r') as f:
                real_data = f['real'][:]
                pred_data = f['pred'][:]
                
                # Calculate metrics
                rmse = cal_RMSE(pred_data.flatten(), real_data.flatten())
                r2 = cal_R2(pred_data.flatten(), real_data.flatten())
                
                results.append({
                    'file': result_file,
                    'actual': real_data.flatten(),
                    'prediction': pred_data.flatten(),
                    'rmse': rmse,
                    'r2': r2,
                    'actual_range': [real_data.min(), real_data.max()],
                    'pred_range': [pred_data.min(), pred_data.max()]
                })
                
                print(f"  ✅ {result_file}: RMSE={rmse:.4f}, R²={r2:.4f}")
                
        except Exception as e:
            print(f"  ❌ Error loading {result_file}: {e}")
            continue
    
    if not results:
        print("❌ No valid results to plot!")
        return None
    
    # Set up the plotting style
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (15, 10)
    plt.rcParams['font.size'] = 10
    
    # Create a comprehensive figure with multiple subplots
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle('Density (DEN) Prediction Results - MWLT Transformer', fontsize=16, fontweight='bold')
    
    # Number of files
    n_files = len(results)
    
    # Individual file comparisons (top row)
    for i, result in enumerate(results):
        ax1 = plt.subplot(3, max(n_files, 3), i + 1)
        
        actual = result['actual']
        predicted = result['prediction']
        
        # Create depth array (assuming uniform sampling)
        depth = np.arange(len(actual))
        
        ax1.plot(actual, depth, 'b-', label='Actual DEN', linewidth=2, alpha=0.8)
        ax1.plot(predicted, depth, 'r--', label='Predicted DEN', linewidth=2, alpha=0.8)
        ax1.set_xlabel('Density (g/cm³)')
        ax1.set_ylabel('Sample Index')
        ax1.set_title(f'{result["file"]}\nRMSE: {result["rmse"]:.4f}, R²: {result["r2"]:.3f}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()  # Invert y-axis to simulate depth
    
    # Scatter plot comparison (middle row, left)
    ax2 = plt.subplot(3, 3, 4)
    all_actual = np.concatenate([r['actual'] for r in results])
    all_predicted = np.concatenate([r['prediction'] for r in results])
    
    ax2.scatter(all_actual, all_predicted, alpha=0.6, s=20)
    
    # Perfect prediction line
    min_val = min(all_actual.min(), all_predicted.min())
    max_val = max(all_actual.max(), all_predicted.max())
    ax2.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
    
    ax2.set_xlabel('Actual Density (g/cm³)')
    ax2.set_ylabel('Predicted Density (g/cm³)')
    ax2.set_title('Actual vs Predicted Scatter Plot')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # Error distribution (middle row, center)
    ax3 = plt.subplot(3, 3, 5)
    errors = all_predicted - all_actual
    ax3.hist(errors, bins=50, alpha=0.7, color='skyblue', edgecolor='black')
    ax3.axvline(0, color='red', linestyle='--', linewidth=2, label='Zero Error')
    ax3.set_xlabel('Prediction Error (g/cm³)')
    ax3.set_ylabel('Frequency')
    ax3.set_title(f'Error Distribution\nMean Error: {errors.mean():.4f}')
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # Performance metrics (middle row, right)
    ax4 = plt.subplot(3, 3, 6)
    file_names = [r['file'].replace('.hdf5', '') for r in results]
    rmse_values = [r['rmse'] for r in results]
    r2_values = [r['r2'] for r in results]
    
    x_pos = np.arange(len(file_names))
    
    # Create twin axis for R²
    ax4_twin = ax4.twinx()
    
    bars1 = ax4.bar(x_pos - 0.2, rmse_values, 0.4, label='RMSE', color='lightcoral', alpha=0.8)
    bars2 = ax4_twin.bar(x_pos + 0.2, r2_values, 0.4, label='R²', color='lightblue', alpha=0.8)
    
    ax4.set_xlabel('Files')
    ax4.set_ylabel('RMSE', color='red')
    ax4_twin.set_ylabel('R² Score', color='blue')
    ax4.set_title('Performance Metrics by File')
    ax4.set_xticks(x_pos)
    ax4.set_xticklabels(file_names, rotation=45, ha='right')
    
    # Add value labels on bars
    for bar, value in zip(bars1, rmse_values):
        height = bar.get_height()
        ax4.text(bar.get_x() + bar.get_width()/2., height + 0.001,
                f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    
    for bar, value in zip(bars2, r2_values):
        height = bar.get_height()
        ax4_twin.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                     f'{value:.3f}', ha='center', va='bottom', fontsize=8)
    
    # Overall statistics (bottom row)
    ax5 = plt.subplot(3, 1, 3)
    ax5.axis('off')
    
    # Calculate overall statistics
    overall_rmse = cal_RMSE(all_predicted, all_actual)
    overall_r2 = cal_R2(all_predicted, all_actual)
    mean_error = errors.mean()
    std_error = errors.std()
    
    stats_text = f"""
    📊 OVERALL PERFORMANCE STATISTICS
    
    🎯 Prediction Accuracy:
       • Overall RMSE: {overall_rmse:.4f} g/cm³
       • Overall R² Score: {overall_r2:.4f}
       • Mean Absolute Error: {np.abs(errors).mean():.4f} g/cm³
    
    📈 Error Analysis:
       • Mean Error: {mean_error:.4f} g/cm³
       • Standard Deviation: {std_error:.4f} g/cm³
       • Error Range: [{errors.min():.4f}, {errors.max():.4f}] g/cm³
    
    📋 Data Summary:
       • Total Samples: {len(all_actual):,}
       • Files Processed: {len(results)}
       • Actual Range: [{all_actual.min():.3f}, {all_actual.max():.3f}] g/cm³
       • Predicted Range: [{all_predicted.min():.3f}, {all_predicted.max():.3f}] g/cm³
    
    🏆 Performance Assessment:
       • {'🎉 EXCELLENT' if overall_r2 > 0.9 else '✅ GOOD' if overall_r2 > 0.7 else '⚠️ FAIR' if overall_r2 > 0.5 else '❌ POOR'} prediction quality
       • Model shows {'high' if overall_r2 > 0.8 else 'moderate' if overall_r2 > 0.6 else 'low'} correlation with actual values
    """
    
    ax5.text(0.05, 0.95, stats_text, transform=ax5.transAxes, fontsize=11,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    # Adjust layout
    plt.tight_layout()
    
    # Save the plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = os.path.join(save_dir, f'density_prediction_analysis_{timestamp}.png')
    plt.savefig(plot_filename, dpi=300, bbox_inches='tight')
    
    print(f"📊 Comprehensive plot saved: {plot_filename}")
    
    # Save summary report
    report_filename = os.path.join(save_dir, f'density_prediction_report_{timestamp}.txt')
    with open(report_filename, 'w') as f:
        f.write("DENSITY PREDICTION ANALYSIS REPORT\n")
        f.write("=" * 50 + "\n\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("INDIVIDUAL FILE RESULTS:\n")
        f.write("-" * 30 + "\n")
        for result in results:
            f.write(f"File: {result['file']}\n")
            f.write(f"  RMSE: {result['rmse']:.4f} g/cm³\n")
            f.write(f"  R² Score: {result['r2']:.4f}\n")
            f.write(f"  Actual Range: [{result['actual_range'][0]:.3f}, {result['actual_range'][1]:.3f}] g/cm³\n")
            f.write(f"  Predicted Range: [{result['pred_range'][0]:.3f}, {result['pred_range'][1]:.3f}] g/cm³\n\n")
        
        f.write("OVERALL STATISTICS:\n")
        f.write("-" * 20 + "\n")
        f.write(f"Overall RMSE: {overall_rmse:.4f} g/cm³\n")
        f.write(f"Overall R² Score: {overall_r2:.4f}\n")
        f.write(f"Mean Error: {mean_error:.4f} g/cm³\n")
        f.write(f"Error Std Dev: {std_error:.4f} g/cm³\n")
        f.write(f"Total Samples: {len(all_actual):,}\n")
        f.write(f"Files Processed: {len(results)}\n")
    
    print(f"📄 Analysis report saved: {report_filename}")
    
    # Show the plot
    plt.show()
    
    return plot_filename

def main():
    """Main function for standalone execution"""
    print("🎨 Creating Density Prediction Visualization...")
    
    # Check if results exist
    results_dir = "../result_base1/pred_val"
    if not os.path.exists(results_dir):
        print(f"❌ Results directory {results_dir} not found!")
        print("   Please run the standard density prediction test first (option 1 in run_test.bat)")
        return
    
    # Create plots
    plot_file = create_density_comparison_plots(results_dir)
    
    if plot_file:
        print(f"\n✅ Density prediction visualization completed!")
        print(f"📊 Main plot: {plot_file}")
        print(f"📁 Check the ../density_prediction_results/ directory for all outputs")
    else:
        print("\n❌ Failed to create visualization")

if __name__ == "__main__":
    main()
