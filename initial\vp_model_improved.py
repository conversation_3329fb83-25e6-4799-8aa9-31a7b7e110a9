"""
Improved model architecture specifically for Vp (Sonic Velocity) prediction
Addresses the scale mismatch and activation function issues
Complete module with training, evaluation, and inference capabilities
"""
import os
import argparse
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py
from sklearn.model_selection import train_test_split

from model import Input_Embedding, PositionalEncoding, TransformerBlock, ResCNN
from utils import get_device, save_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from las_processor import LASProcessor

class VpDecoder(nn.Module):
    """
    Decoder specifically designed for Vp prediction
    Outputs normalized values that are denormalized during inference
    FIXED: Removed sigmoid activation to eliminate artificial bounds constraints
    """
    def __init__(self, res_num=4, out_channels=1, feature_num=64):
        super().__init__()
        self.rescnn = nn.ModuleList([
            ResCNN(in_channels=feature_num, out_channels=feature_num,
                   kernel_size=11, padding=5, stride=1)
            for i in range(res_num)
        ])

        # Improved output layer without hard-coded range constraints
        self.out_layer = nn.Sequential(
            nn.Conv1d(in_channels=feature_num, out_channels=feature_num//2,
                     kernel_size=11, padding=5, stride=1),
            nn.ReLU(),
            nn.Conv1d(in_channels=feature_num//2, out_channels=out_channels,
                     kernel_size=11, padding=5, stride=1)
            # REMOVED: ReLU and Linear layers that force artificial constraints
            # REMOVED: Hard-coded Vp range scaling parameters
        )

    def forward(self, x):
        for model in self.rescnn:
            x = model(x)

        x = self.out_layer(x)

        # SOLUTION: Output normalized values, denormalize during inference
        # This allows the model to learn the proper range naturally without
        # artificial constraints from sigmoid saturation
        return x  # Returns normalized Vp values

class VpTransformer(nn.Module):
    """
    Complete transformer model for Vp prediction with proper scaling
    """
    def __init__(self, in_channels=4, out_channels=1, feature_num=64, res_num=4, 
                 encoder_num=4, use_pe=True, dim=64, seq_len=640, num_heads=4, 
                 mlp_ratio=4., qkv_bias=False, drop=0., attn_drop=0., position_drop=0.,
                 act_layer=nn.GELU, norm_layer=nn.LayerNorm):
        super().__init__()
        
        self.feature_embedding = Input_Embedding(in_channels=in_channels, 
                                                feature_num=feature_num, res_num=res_num)
        self.position_embedding = PositionalEncoding(d_model=dim, dropout=position_drop, 
                                                    seq_len=seq_len)
        self.use_pe = use_pe
        self.transformer_encoder = TransformerBlock(block_num=encoder_num, dim=dim, 
                                                   num_heads=num_heads, mlp_ratio=mlp_ratio, 
                                                   qkv_bias=qkv_bias, drop=drop, attn_drop=attn_drop,
                                                   act_layer=act_layer, norm_layer=norm_layer)
        
        # Use Vp-specific decoder
        self.decoder = VpDecoder(out_channels=out_channels, feature_num=feature_num, 
                                res_num=res_num)

    def forward(self, x):
        # [B,in_channels,L] --> [B,feature_num,L]
        x = self.feature_embedding(x)
        # [B,feature_num,L]--> [B,L,feature_num]
        x = x.transpose(-2, -1)
        if self.use_pe:
            x = self.position_embedding(x)
        # [B, L, feature_num] --> [B, L, feature_num]
        x = self.transformer_encoder(x)
        # [B,  L, feature_num] --> [B, feature_num,L]
        x = x.transpose(-2, -1)
        x = self.decoder(x)
        return x

def MWLT_Vp_Small(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    """Small Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=2, encoder_num=2, 
                        num_heads=2, **kwargs)

def MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64, **kwargs):
    """Base Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=4, encoder_num=4, 
                        num_heads=4, **kwargs)

def MWLT_Vp_Large(in_channels=4, out_channels=1, feature_num=128, **kwargs):
    """Large Vp-specific transformer model"""
    return VpTransformer(in_channels=in_channels, out_channels=out_channels,
                        feature_num=feature_num, res_num=6, encoder_num=6, 
                        num_heads=8, **kwargs)

class VpDataNormalizer:
    """
    Proper data normalization for Vp prediction
    """
    def __init__(self):
        # Input curve normalization parameters
        self.input_stats = {
            'GR': {'mean': 75.0, 'std': 50.0, 'min': 0, 'max': 200},
            'CNL': {'mean': 20.0, 'std': 15.0, 'min': 0, 'max': 60},
            'DEN': {'mean': 2.5, 'std': 0.3, 'min': 1.5, 'max': 3.0},
            'RLLD': {'mean': 10.0, 'std': 50.0, 'min': 0.1, 'max': 1000}  # Log scale
        }
        
        # Vp normalization parameters
        self.vp_stats = {'mean': 200.0, 'std': 75.0, 'min': 40, 'max': 400}
    
    def normalize_inputs(self, curves_dict):
        """Normalize input curves to [-1, 1] range"""
        normalized = {}
        
        for curve_name, data in curves_dict.items():
            if curve_name in self.input_stats:
                stats = self.input_stats[curve_name]
                
                if curve_name == 'RLLD':
                    # Log transform for resistivity
                    data = torch.log10(torch.clamp(data, min=0.1))
                    normalized[curve_name] = (data - 1.0) / 2.0  # Normalize log values
                else:
                    # Standard normalization
                    normalized[curve_name] = (data - stats['mean']) / stats['std']
                    normalized[curve_name] = torch.clamp(normalized[curve_name], -3, 3) / 3
            else:
                normalized[curve_name] = data
                
        return normalized
    
    def normalize_vp(self, vp_data):
        """Normalize Vp values for training"""
        return (vp_data - self.vp_stats['mean']) / self.vp_stats['std']
    
    def denormalize_vp(self, normalized_vp):
        """Convert normalized Vp back to physical units"""
        return normalized_vp * self.vp_stats['std'] + self.vp_stats['mean']

# Loss function specifically for Vp prediction
class VpLoss(nn.Module):
    """
    Custom loss function for Vp prediction that handles the physical constraints
    """
    def __init__(self, alpha=1.0, beta=0.1):
        super().__init__()
        self.mse_loss = nn.MSELoss()
        self.alpha = alpha  # Weight for MSE loss
        self.beta = beta    # Weight for physical constraint loss
        
    def forward(self, pred, target):
        # Standard MSE loss
        mse = self.mse_loss(pred, target)
        
        # Physical constraint loss (penalize unrealistic values)
        constraint_loss = torch.mean(torch.relu(pred - 500) + torch.relu(20 - pred))
        
        return self.alpha * mse + self.beta * constraint_loss

class VpDataset(torch.utils.data.Dataset):
    """
    Improved dataset for Vp prediction with proper normalization
    """
    def __init__(self, input_data, target_data, normalizer, total_seqlen=720, effect_seqlen=640, transform=False):
        self.input_data = input_data
        self.target_data = target_data
        self.normalizer = normalizer
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        self.transform = transform
        
    def __len__(self):
        return len(self.input_data)
    
    def __getitem__(self, idx):
        # Get input curves and target
        inputs = self.input_data[idx]  # Shape: [4, 720] for GR, CNL, DEN, RLLD
        target = self.target_data[idx]  # Shape: [720] for AC
        
        # Apply data augmentation if requested
        if self.transform and self.total_seqlen > self.effect_seqlen:
            start_idx = np.random.randint(0, self.total_seqlen - self.effect_seqlen + 1)
            inputs = inputs[:, start_idx:start_idx + self.effect_seqlen]
            target = target[start_idx:start_idx + self.effect_seqlen]
        else:
            inputs = inputs[:, :self.effect_seqlen]
            target = target[:self.effect_seqlen]
        
        return torch.FloatTensor(inputs), torch.FloatTensor(target).unsqueeze(0)

def find_data_files():
    """
    Auto-detect the location of A1.hdf5 and A2.hdf5 files
    """
    # Possible locations to search
    possible_paths = [
        # Current directory and parent
        '.',
        '..',
        # Your specific path
        r"C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\main\Transformer",
        # Relative paths from code directory
        os.path.join('..'),
        os.path.join('..', '..'),
    ]

    found_files = []
    for base_path in possible_paths:
        a1_path = os.path.join(base_path, 'A1.hdf5')
        a2_path = os.path.join(base_path, 'A2.hdf5')

        if os.path.exists(a1_path) and os.path.exists(a2_path):
            found_files = [a1_path, a2_path]
            print(f"Found data files in: {os.path.abspath(base_path)}")
            break

    if not found_files:
        print("Could not find A1.hdf5 and A2.hdf5 files!")
        print("Searched in:")
        for path in possible_paths:
            print(f"  - {os.path.abspath(path)}")
        print("\nPlease ensure A1.hdf5 and A2.hdf5 are in one of these locations.")

    return found_files

def create_improved_vp_data():
    """
    Create improved training data with proper normalization and augmentation
    """
    processor = LASProcessor()
    normalizer = VpDataNormalizer()

    print("Creating improved Vp training data...")

    # Auto-detect data file locations
    input_files = find_data_files()
    if not input_files:
        return np.array([]), np.array([])
    all_inputs = []
    all_targets = []
    
    for file_path in input_files:
        if not os.path.exists(file_path):
            print(f"Warning: {file_path} not found, skipping...")
            continue
            
        print(f"Processing {file_path}...")
        curves = processor.process_hdf5_to_las_format(file_path)
        
        # Create multiple overlapping windows for data augmentation
        sequence_length = 720
        step_size = 360  # 50% overlap
        
        data_length = len(curves['AC'])
        num_windows = max(1, (data_length - sequence_length) // step_size + 1)
        
        for i in range(num_windows):
            start_idx = i * step_size
            end_idx = start_idx + sequence_length
            
            if end_idx > data_length:
                start_idx = data_length - sequence_length
                end_idx = data_length
            
            # Extract window
            window_curves = {}
            for curve_name, data in curves.items():
                window_curves[curve_name] = data[start_idx:end_idx]
            
            # Normalize inputs
            input_features = []
            for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
                if curve_name in window_curves:
                    data = torch.FloatTensor(window_curves[curve_name])
                    if curve_name == 'RLLD':
                        # Log transform for resistivity
                        data = torch.log10(torch.clamp(data, min=0.1))
                        normalized = (data - 1.0) / 2.0
                    else:
                        stats = normalizer.input_stats[curve_name]
                        normalized = (data - stats['mean']) / stats['std']
                        normalized = torch.clamp(normalized, -3, 3) / 3
                    input_features.append(normalized.numpy())
                else:
                    input_features.append(np.zeros(sequence_length))
            
            # Target (AC) - keep in original units for now
            target = window_curves.get('AC', np.zeros(sequence_length))
            
            all_inputs.append(np.array(input_features))
            all_targets.append(target)
    
    print(f"Created {len(all_inputs)} training samples")
    return np.array(all_inputs), np.array(all_targets)

def train_improved_vp_model(config=None):
    """
    Train improved Vp model with proper architecture and normalization
    """
    # Default configuration
    if config is None:
        config = {
            'model_type': 'base',
            'batch_size': 8,
            'learning_rate': 1e-4,
            'epochs': 200,
            'patience': 50,
            'device': 0,
            'save_path': os.path.join('..', 'vp_improved_training')
        }
    
    # Setup
    device = get_device(config['device'])
    os.makedirs(config['save_path'], exist_ok=True)
    
    # Create improved training data
    input_data, target_data = create_improved_vp_data()
    
    if len(input_data) == 0:
        print("Error: No training data created!")
        return None
    
    # Split data
    train_inputs, val_inputs, train_targets, val_targets = train_test_split(
        input_data, target_data, test_size=0.2, random_state=42
    )
    
    print(f"Training samples: {len(train_inputs)}")
    print(f"Validation samples: {len(val_inputs)}")
    
    # Create datasets
    normalizer = VpDataNormalizer()
    train_dataset = VpDataset(train_inputs, train_targets, normalizer, transform=True)
    val_dataset = VpDataset(val_inputs, val_targets, normalizer, transform=False)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=config['batch_size'], shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=1, shuffle=False)
    
    # Create improved model
    if config['model_type'] == 'small':
        model = MWLT_Vp_Small()
    elif config['model_type'] == 'base':
        model = MWLT_Vp_Base()
    else:
        model = MWLT_Vp_Large()
    
    model = model.to(device)
    
    # Setup training
    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])
    criterion = VpLoss()
    early_stopping = EarlyStopping(patience=config['patience'], 
                                  path=os.path.join(config['save_path'], 'best_vp_improved_model.pth'))
    
    print(f"\nStarting improved Vp training on {device}")
    print(f"Model: {config['model_type']}, Samples: {len(train_inputs)}")
    
    # Training loop
    for epoch in range(1, config['epochs'] + 1):
        # Training
        model.train()
        train_loss = 0.0
        
        for batch_idx, (inputs, targets) in enumerate(train_loader):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            train_loss += loss.item()
        
        train_loss /= len(train_loader)
        
        # Validation
        model.eval()
        val_loss = 0.0
        val_predictions = []
        val_actuals = []
        
        with torch.no_grad():
            for inputs, targets in val_loader:
                inputs, targets = inputs.to(device), targets.to(device)
                outputs = model(inputs)
                loss = criterion(outputs, targets)
                val_loss += loss.item()
                
                val_predictions.extend(outputs.cpu().numpy().flatten())
                val_actuals.extend(targets.cpu().numpy().flatten())
        
        val_loss /= len(val_loader)
        
        # Calculate metrics
        val_rmse = cal_RMSE(np.array(val_predictions), np.array(val_actuals))
        val_r2 = cal_R2(np.array(val_predictions), np.array(val_actuals))
        
        print(f"Epoch {epoch:3d}: Train Loss: {train_loss:.6f}, "
              f"Val Loss: {val_loss:.6f}, RMSE: {val_rmse:.2f}, R²: {val_r2:.4f}")
        
        # Early stopping - save complete state dictionary
        state = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "loss": val_loss,
            "epoch": epoch,
            "rmse": val_rmse,
            "r2": val_r2
        }
        early_stopping(state, model)
        if early_stopping.early_stop:
            print(f"Early stopping at epoch {epoch}")
            break
    
    print(f"\nTraining completed! Best model saved to {config['save_path']}")
    return config['save_path']

def evaluate_vp_model(model_path, test_data_path=None):
    """
    Evaluate trained Vp model on test data
    """
    device = get_device()
    
    # Load model
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device)
        model = MWLT_Vp_Base()
        model.load_state_dict(checkpoint)
        model = model.to(device)
        model.eval()
        print(f"Loaded model from {model_path}")
    else:
        print(f"Model file not found: {model_path}")
        return None
    
    # If no test data specified, use validation split from training data
    if test_data_path is None:
        input_data, target_data = create_improved_vp_data()
        if len(input_data) == 0:
            print("Error: No test data created!")
            return None
        
        # Use last 20% as test data
        _, test_inputs, _, test_targets = train_test_split(
            input_data, target_data, test_size=0.2, random_state=42
        )
    else:
        # Load specific test data
        test_inputs, test_targets = create_improved_vp_data()  # Modify as needed
    
    # Create test dataset
    normalizer = VpDataNormalizer()
    test_dataset = VpDataset(test_inputs, test_targets, normalizer, transform=False)
    test_loader = DataLoader(test_dataset, batch_size=1, shuffle=False)
    
    # Evaluate
    predictions = []
    actuals = []
    
    with torch.no_grad():
        for inputs, targets in test_loader:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            
            predictions.extend(outputs.cpu().numpy().flatten())
            actuals.extend(targets.cpu().numpy().flatten())
    
    # Calculate metrics
    predictions = np.array(predictions)
    actuals = np.array(actuals)
    
    rmse = cal_RMSE(predictions, actuals)
    r2 = cal_R2(predictions, actuals)
    
    print(f"\nEvaluation Results:")
    print(f"Test RMSE: {rmse:.2f}")
    print(f"Test R²: {r2:.4f}")
    print(f"Mean Actual: {actuals.mean():.2f}")
    print(f"Mean Predicted: {predictions.mean():.2f}")
    print(f"Std Actual: {actuals.std():.2f}")
    print(f"Std Predicted: {predictions.std():.2f}")
    
    return {
        'rmse': rmse,
        'r2': r2,
        'predictions': predictions,
        'actuals': actuals
    }

def predict_vp_from_hdf5(model_path, hdf5_file, output_path=None):
    """
    Use trained model to predict Vp from HDF5 file
    """
    device = get_device()
    
    # Load model
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device)
        model = MWLT_Vp_Base()
        model.load_state_dict(checkpoint)
        model = model.to(device)
        model.eval()
        print(f"Loaded model from {model_path}")
    else:
        print(f"Model file not found: {model_path}")
        return None
    
    # Process input file
    processor = LASProcessor()
    normalizer = VpDataNormalizer()
    
    print(f"Processing {hdf5_file}...")
    curves = processor.process_hdf5_to_las_format(hdf5_file)
    
    # Prepare input data
    sequence_length = 640
    input_features = []
    
    for curve_name in ['GR', 'CNL', 'DEN', 'RLLD']:
        if curve_name in curves:
            data = torch.FloatTensor(curves[curve_name][:sequence_length])
            if curve_name == 'RLLD':
                # Log transform for resistivity
                data = torch.log10(torch.clamp(data, min=0.1))
                normalized = (data - 1.0) / 2.0
            else:
                stats = normalizer.input_stats[curve_name]
                normalized = (data - stats['mean']) / stats['std']
                normalized = torch.clamp(normalized, -3, 3) / 3
            input_features.append(normalized.numpy())
        else:
            input_features.append(np.zeros(sequence_length))
    
    # Create input tensor
    input_tensor = torch.FloatTensor(np.array(input_features)).unsqueeze(0).to(device)
    
    # Predict
    with torch.no_grad():
        prediction_normalized = model(input_tensor)
        # UPDATED: Denormalize predictions to get physical units
        # This converts normalized model output back to μs/ft units
        prediction_tensor = torch.from_numpy(prediction_normalized.cpu().numpy())
        prediction_physical = normalizer.denormalize_vp(prediction_tensor)
        vp_prediction = prediction_physical.numpy().flatten()
    
    print(f"Vp prediction range: {vp_prediction.min():.2f} - {vp_prediction.max():.2f}")
    
    # Save results if output path specified
    if output_path:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        np.save(output_path, vp_prediction)
        print(f"Prediction saved to {output_path}")
    
    return vp_prediction

# Main execution function
def main():
    """
    Main function for command-line usage
    """
    parser = argparse.ArgumentParser(description='Improved Vp Model Training and Inference')
    parser.add_argument('--mode', type=str, choices=['train', 'evaluate', 'predict'], 
                       default='train', help='Operation mode')
    parser.add_argument('--model_type', type=str, choices=['small', 'base', 'large'], 
                       default='base', help='Model size')
    parser.add_argument('--model_path', type=str, help='Path to trained model')
    parser.add_argument('--input_file', type=str, help='Input HDF5 file for prediction')
    parser.add_argument('--output_file', type=str, help='Output file for predictions')
    parser.add_argument('--epochs', type=int, default=200, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=8, help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=1e-4, help='Learning rate')
    
    args = parser.parse_args()
    
    if args.mode == 'train':
        config = {
            'model_type': args.model_type,
            'batch_size': args.batch_size,
            'learning_rate': args.learning_rate,
            'epochs': args.epochs,
            'patience': 50,
            'device': 0,
            'save_path': os.path.join('..', f'vp_improved_{args.model_type}_training')
        }
        train_improved_vp_model(config)
        
    elif args.mode == 'evaluate':
        if not args.model_path:
            print("Error: --model_path required for evaluation")
            return
        evaluate_vp_model(args.model_path)
        
    elif args.mode == 'predict':
        if not args.model_path or not args.input_file:
            print("Error: --model_path and --input_file required for prediction")
            return
        predict_vp_from_hdf5(args.model_path, args.input_file, args.output_file)

if __name__ == "__main__":
    main()
