# MWLT Advanced Training Guide
## Complete Guide for Multi-Curve Training Scenarios

**Version**: Phase 2 Complete  
**Last Updated**: 2025-08-10  
**Target Audience**: Advanced users, researchers, production deployments

---

## Table of Contents

1. [Quick Start Guide](#quick-start-guide)
2. [Curve-Specific Training](#curve-specific-training)
3. [Hyperparameter Tuning](#hyperparameter-tuning)
4. [Advanced Scenarios](#advanced-scenarios)
5. [Troubleshooting Guide](#troubleshooting-guide)
6. [Performance Optimization](#performance-optimization)
7. [Production Deployment](#production-deployment)

---

## Quick Start Guide

### Basic Training Setup

```python
from vp_predictor import (
    GeneralWellLogTransformer, GeneralDataNormalizer, 
    GeneralWellLogDataset, GeneralTrainingManager,
    get_training_template, get_model_template
)

# 1. Prepare your data
data_dict = {
    'GR': your_gamma_ray_data,      # Shape: (n_samples, sequence_length)
    'CNL': your_neutron_data,       # Shape: (n_samples, sequence_length)
    'DEN': your_density_data,       # Shape: (n_samples, sequence_length)
    'AC': your_sonic_data,          # Shape: (n_samples, sequence_length)
    'RLLD': your_resistivity_data   # Shape: (n_samples, sequence_length)
}

# 2. Define input/output configuration
input_curves = ['GR', 'CNL', 'AC', 'RLLD']
target_curve = 'DEN'  # Predict density

# 3. Create model and normalizer
model = GeneralWellLogTransformer.from_template('density_prediction')
normalizer = GeneralDataNormalizer(input_curves, [target_curve])

# 4. Create dataset
dataset = GeneralWellLogDataset(
    data_dict=data_dict,
    input_curves=input_curves,
    target_curve=target_curve,
    normalizer=normalizer
)

# 5. Setup training
training_config = get_training_template('density_training')
trainer = GeneralTrainingManager(
    model=model,
    train_dataset=dataset,
    training_config=training_config
)

# 6. Train model
results = trainer.train()
```

### Data Requirements

**Minimum Requirements**:
- At least 50 training samples per curve
- Sequence length ≥ 320 points
- Data within physics ranges (see curve specifications below)

**Recommended**:
- 500+ training samples for production models
- Sequence length 640-720 points
- Multiple wells for better generalization

---

## Curve-Specific Training

### 1. Density (DEN) Prediction

**Physics Range**: 1.5 - 3.0 g/cm³  
**Best Input Curves**: GR, CNL, AC, RLLD  
**Recommended Configuration**:

```python
# Optimal hyperparameters for density prediction
training_config = {
    'target_curve': 'DEN',
    'optimizer': 'adam',
    'learning_rate': 1e-4,
    'max_epochs': 100,
    'batch_size': 8,
    'loss_type': 'mse',
    'physics_constraints': True,
    'early_stopping_patience': 15
}

# Model configuration
model_config = {
    'encoder_layers': 6,
    'attention_heads': 8,
    'feature_dim': 256,
    'dropout': 0.1
}
```

**Training Tips**:
- Density is generally stable and converges well
- Use physics constraints to prevent unrealistic predictions
- Monitor for overfitting with small datasets

### 2. Neutron Porosity (CNL) Prediction

**Physics Range**: 0 - 60%  
**Best Input Curves**: GR, DEN, AC, RLLD  
**Recommended Configuration**:

```python
training_config = {
    'target_curve': 'CNL',
    'optimizer': 'adam',
    'learning_rate': 8e-5,  # Slightly lower LR
    'max_epochs': 120,      # May need more epochs
    'batch_size': 8,
    'loss_type': 'huber',   # Robust to outliers
    'physics_constraints': True,
    'early_stopping_patience': 20
}
```

**Training Tips**:
- Neutron logs can be noisy; use robust loss functions
- Consider data quality filtering (quality_threshold=0.8)
- May require longer training due to noise

### 3. Gamma Ray (GR) Prediction

**Physics Range**: 0 - 200 API  
**Best Input Curves**: CNL, DEN, AC, RLLD  
**Recommended Configuration**:

```python
training_config = {
    'target_curve': 'GR',
    'optimizer': 'adam',
    'learning_rate': 1.2e-4,
    'max_epochs': 80,       # Usually converges faster
    'batch_size': 12,       # Can use larger batches
    'loss_type': 'mse',
    'physics_constraints': True,
    'early_stopping_patience': 12
}
```

**Training Tips**:
- GR typically has good signal-to-noise ratio
- Converges relatively quickly
- Good for testing new configurations

### 4. Resistivity (RLLD) Prediction

**Physics Range**: 0.1 - 1000 ohm-m  
**Best Input Curves**: GR, CNL, DEN, AC  
**Recommended Configuration**:

```python
training_config = {
    'target_curve': 'RLLD',
    'optimizer': 'adam',
    'learning_rate': 5e-5,  # Lower LR for log-scale data
    'max_epochs': 150,      # Needs more epochs
    'batch_size': 6,        # Smaller batches
    'loss_type': 'mae',     # Better for log-scale
    'physics_constraints': True,
    'early_stopping_patience': 25
}
```

**Training Tips**:
- Resistivity spans multiple orders of magnitude
- Use log-scale normalization (handled automatically)
- Requires careful hyperparameter tuning
- Monitor training curves closely

### 5. Sonic Velocity (AC) Prediction

**Physics Range**: 40 - 400 μs/ft  
**Best Input Curves**: GR, CNL, DEN, RLLD  
**Recommended Configuration**:

```python
training_config = {
    'target_curve': 'AC',
    'optimizer': 'adam',
    'learning_rate': 1e-4,
    'max_epochs': 100,
    'batch_size': 8,
    'loss_type': 'mse',
    'physics_constraints': True,
    'early_stopping_patience': 15
}
```

**Training Tips**:
- Well-established training (original VP predictor)
- Good baseline for comparing other curves
- Generally stable and reliable

---

## Hyperparameter Tuning

### Learning Rate Guidelines

| Curve Type | Recommended LR | Range | Notes |
|------------|----------------|-------|-------|
| DEN | 1e-4 | 5e-5 to 2e-4 | Stable, standard rate |
| CNL | 8e-5 | 3e-5 to 1.5e-4 | Lower due to noise |
| GR | 1.2e-4 | 8e-5 to 2e-4 | Can handle higher rates |
| RLLD | 5e-5 | 2e-5 to 1e-4 | Lower for log-scale |
| AC | 1e-4 | 5e-5 to 2e-4 | Well-tested baseline |

### Batch Size Guidelines

- **Small datasets** (< 200 samples): batch_size = 4-6
- **Medium datasets** (200-1000 samples): batch_size = 8-12
- **Large datasets** (> 1000 samples): batch_size = 12-16

### Model Architecture Tuning

```python
# Small model (fast training, less capacity)
small_config = {
    'encoder_layers': 4,
    'attention_heads': 4,
    'feature_dim': 128,
    'dropout': 0.1
}

# Base model (balanced)
base_config = {
    'encoder_layers': 6,
    'attention_heads': 8,
    'feature_dim': 256,
    'dropout': 0.1
}

# Large model (high capacity, slower)
large_config = {
    'encoder_layers': 8,
    'attention_heads': 12,
    'feature_dim': 512,
    'dropout': 0.15
}
```

### Systematic Hyperparameter Search

```python
def hyperparameter_search(data_dict, input_curves, target_curve):
    """Systematic hyperparameter search for optimal configuration"""
    
    # Define search space
    learning_rates = [5e-5, 1e-4, 2e-4]
    batch_sizes = [6, 8, 12]
    loss_types = ['mse', 'mae', 'huber']
    
    best_config = None
    best_score = float('inf')
    
    for lr in learning_rates:
        for batch_size in batch_sizes:
            for loss_type in loss_types:
                config = {
                    'target_curve': target_curve,
                    'learning_rate': lr,
                    'batch_size': batch_size,
                    'loss_type': loss_type,
                    'max_epochs': 20  # Quick evaluation
                }
                
                # Train and evaluate
                score = quick_train_and_evaluate(
                    data_dict, input_curves, target_curve, config
                )
                
                if score < best_score:
                    best_score = score
                    best_config = config
    
    return best_config, best_score
```

---

## Advanced Scenarios

### 1. Missing Data Handling

```python
# Configure robust missing data handling
dataset = GeneralWellLogDataset(
    data_dict=data_dict,
    input_curves=input_curves,
    target_curve=target_curve,
    normalizer=normalizer,
    missing_data_strategy='interpolation',  # or 'masking', 'skip'
    quality_threshold=0.7,  # Accept samples with 70%+ valid data
    transform=True
)

# Use robust loss function
training_config = get_training_template('robust_training')
training_config.update({
    'loss_type': 'huber',  # Less sensitive to outliers
    'physics_constraints': True,
    'gradient_clipping': 1.0
})
```

### 2. Multi-Well Training

```python
def prepare_multi_well_data(well_files, input_curves, target_curve):
    """Combine data from multiple wells for robust training"""
    
    combined_data = {curve: [] for curve in input_curves + [target_curve]}
    
    for well_file in well_files:
        well_data = load_well_data(well_file)
        
        # Validate data quality
        if validate_well_data(well_data, input_curves + [target_curve]):
            for curve in input_curves + [target_curve]:
                combined_data[curve].append(well_data[curve])
    
    # Concatenate all wells
    for curve in combined_data:
        combined_data[curve] = np.concatenate(combined_data[curve], axis=0)
    
    return combined_data
```

### 3. Transfer Learning

```python
# Start with pre-trained AC model and adapt for density
base_model = GeneralWellLogTransformer.from_template('vp_prediction')

# Load pre-trained weights
checkpoint = torch.load('pretrained_ac_model.pth')
base_model.load_state_dict(checkpoint['model_state_dict'], strict=False)

# Modify for density prediction
density_model = GeneralWellLogTransformer(
    input_curves=['GR', 'CNL', 'AC', 'RLLD'],
    output_curves=['DEN'],
    model_config=base_model.get_model_info()['config']
)

# Copy encoder weights
density_model.encoder.load_state_dict(base_model.encoder.state_dict())

# Fine-tune with lower learning rate
training_config = get_training_template('density_training')
training_config['learning_rate'] = 1e-5  # Lower for fine-tuning
```

### 4. Custom Loss Functions

```python
class GeologyAwareLoss(nn.Module):
    """Custom loss function incorporating geological knowledge"""
    
    def __init__(self, target_curve, geology_weight=0.1):
        super().__init__()
        self.target_curve = target_curve
        self.geology_weight = geology_weight
        self.base_loss = GeneralWellLogLoss(target_curve)
    
    def forward(self, predictions, targets):
        # Base physics-aware loss
        base_loss = self.base_loss(predictions, targets)
        
        # Add geological consistency penalty
        geology_penalty = self.compute_geology_penalty(predictions, targets)
        
        return base_loss + self.geology_weight * geology_penalty
    
    def compute_geology_penalty(self, predictions, targets):
        # Example: penalize unrealistic density-porosity relationships
        if self.target_curve == 'DEN':
            # Density should generally decrease with increasing porosity
            density_grad = torch.diff(predictions, dim=-1)
            porosity_grad = torch.diff(targets, dim=-1)  # Assuming porosity context
            
            # Penalize positive correlation (geologically unlikely)
            penalty = torch.relu(density_grad * porosity_grad).mean()
            return penalty
        
        return torch.tensor(0.0)
```

---

## Troubleshooting Guide

### Common Training Issues

#### 1. Training Loss Not Decreasing

**Symptoms**: Loss plateaus immediately or decreases very slowly

**Possible Causes & Solutions**:
- **Learning rate too low**: Increase by 2-5x
- **Learning rate too high**: Decrease by 2-5x
- **Poor data quality**: Check for NaN values, outliers
- **Insufficient model capacity**: Increase encoder_layers or feature_dim
- **Poor normalization**: Verify curve configurations

```python
# Debug training setup
def debug_training_setup(model, dataset, training_config):
    # Check data statistics
    sample_input, sample_target = dataset[0]
    print(f"Input range: {sample_input.min():.3f} to {sample_input.max():.3f}")
    print(f"Target range: {sample_target.min():.3f} to {sample_target.max():.3f}")
    
    # Check model output
    model.eval()
    with torch.no_grad():
        output = model(sample_input.unsqueeze(0))
        print(f"Model output range: {output.min():.3f} to {output.max():.3f}")
    
    # Check loss computation
    loss_fn = GeneralWellLogLoss(target_curve=training_config['target_curve'])
    loss = loss_fn(output, sample_target.unsqueeze(0))
    print(f"Initial loss: {loss.item():.6f}")
```

#### 2. Overfitting

**Symptoms**: Training loss decreases but validation loss increases

**Solutions**:
- Increase dropout rate (0.1 → 0.2)
- Reduce model size
- Add more training data
- Increase early stopping patience
- Use data augmentation

#### 3. Unstable Training

**Symptoms**: Loss oscillates wildly or explodes

**Solutions**:
- Reduce learning rate
- Add gradient clipping
- Check for NaN values in data
- Use more stable optimizer (Adam instead of SGD)

#### 4. Poor Prediction Quality

**Symptoms**: High RMSE, low R², unrealistic predictions

**Solutions**:
- Enable physics constraints
- Increase training epochs
- Improve data quality
- Use appropriate loss function for curve type
- Check input curve selection

### Performance Diagnostics

```python
def diagnose_training_performance(trainer, dataset):
    """Comprehensive training performance diagnostics"""
    
    # Data quality check
    print("=== Data Quality Analysis ===")
    for i in range(min(10, len(dataset))):
        sample_input, sample_target = dataset[i]
        nan_count = torch.isnan(sample_input).sum() + torch.isnan(sample_target).sum()
        if nan_count > 0:
            print(f"Sample {i}: {nan_count} NaN values found")
    
    # Model capacity analysis
    print("\n=== Model Analysis ===")
    total_params = sum(p.numel() for p in trainer.model.parameters())
    trainable_params = sum(p.numel() for p in trainer.model.parameters() if p.requires_grad)
    print(f"Total parameters: {total_params:,}")
    print(f"Trainable parameters: {trainable_params:,}")
    
    # Training dynamics
    print("\n=== Training Dynamics ===")
    # Run a few training steps and monitor gradients
    trainer.model.train()
    for i, (inputs, targets) in enumerate(trainer.train_loader):
        if i >= 3:  # Just check first few batches
            break
            
        outputs = trainer.model(inputs)
        loss = trainer.loss_fn(outputs, targets)
        loss.backward()
        
        # Check gradient norms
        total_norm = 0
        for p in trainer.model.parameters():
            if p.grad is not None:
                param_norm = p.grad.data.norm(2)
                total_norm += param_norm.item() ** 2
        total_norm = total_norm ** (1. / 2)
        
        print(f"Batch {i}: Loss = {loss.item():.6f}, Grad norm = {total_norm:.6f}")
        
        trainer.optimizer.zero_grad()
```

---

## Performance Optimization

### Memory Optimization

```python
# Reduce memory usage for large datasets
training_config = {
    'batch_size': 4,           # Smaller batches
    'gradient_accumulation': 2, # Accumulate gradients
    'mixed_precision': True,    # Use FP16
    'dataloader_workers': 2,    # Parallel data loading
}

# Enable gradient checkpointing for large models
model = GeneralWellLogTransformer.from_template(
    'density_prediction',
    gradient_checkpointing=True
)
```

### Speed Optimization

```python
# Optimize for training speed
training_config = {
    'compile_model': True,      # Use torch.compile (PyTorch 2.0+)
    'dataloader_workers': 4,    # More parallel workers
    'pin_memory': True,         # Faster GPU transfer
    'persistent_workers': True, # Keep workers alive
}

# Use optimized model configurations
fast_model_config = {
    'encoder_layers': 4,        # Fewer layers
    'attention_heads': 4,       # Fewer heads
    'feature_dim': 128,         # Smaller features
}
```

### Distributed Training

```python
# Multi-GPU training setup
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel

def setup_distributed_training():
    # Initialize distributed training
    dist.init_process_group(backend='nccl')
    
    # Create model and move to GPU
    model = GeneralWellLogTransformer.from_template('density_prediction')
    model = model.to(f'cuda:{dist.get_rank()}')
    model = DistributedDataParallel(model)
    
    # Use distributed sampler
    train_sampler = torch.utils.data.distributed.DistributedSampler(train_dataset)
    train_loader = DataLoader(train_dataset, sampler=train_sampler, batch_size=8)
    
    return model, train_loader
```

---

## Production Deployment

### Model Export and Optimization

```python
# Export trained model for production
def export_production_model(trained_model, export_path):
    """Export model for production deployment"""
    
    # Switch to evaluation mode
    trained_model.eval()
    
    # Create example input
    example_input = torch.randn(1, 4, 640)  # [batch, channels, sequence]
    
    # Export to TorchScript
    traced_model = torch.jit.trace(trained_model, example_input)
    traced_model.save(f"{export_path}_traced.pt")
    
    # Export to ONNX (for cross-platform deployment)
    torch.onnx.export(
        trained_model,
        example_input,
        f"{export_path}.onnx",
        export_params=True,
        opset_version=11,
        input_names=['input'],
        output_names=['output'],
        dynamic_axes={
            'input': {0: 'batch_size', 2: 'sequence_length'},
            'output': {0: 'batch_size', 2: 'sequence_length'}
        }
    )
    
    print(f"Model exported to {export_path}.onnx and {export_path}_traced.pt")
```

### Production Inference Pipeline

```python
class ProductionPredictor:
    """Production-ready prediction pipeline"""
    
    def __init__(self, model_path, normalizer_path):
        # Load optimized model
        self.model = torch.jit.load(model_path)
        self.model.eval()
        
        # Load normalizer
        with open(normalizer_path, 'rb') as f:
            self.normalizer = pickle.load(f)
        
        # Setup device
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = self.model.to(self.device)
    
    def predict(self, input_data):
        """Fast production prediction"""
        # Normalize inputs
        normalized_input = self.normalizer.normalize_inputs(input_data)
        
        # Convert to tensor
        input_tensor = torch.FloatTensor(normalized_input).unsqueeze(0).to(self.device)
        
        # Predict
        with torch.no_grad():
            output = self.model(input_tensor)
        
        # Denormalize output
        prediction = self.normalizer.denormalize_predictions(output.cpu().numpy())
        
        return prediction.squeeze()
    
    def batch_predict(self, batch_inputs, batch_size=32):
        """Efficient batch prediction"""
        results = []
        
        for i in range(0, len(batch_inputs), batch_size):
            batch = batch_inputs[i:i+batch_size]
            batch_results = [self.predict(inp) for inp in batch]
            results.extend(batch_results)
        
        return results
```

### Model Validation and Testing

```python
def validate_production_model(model_path, test_data):
    """Comprehensive production model validation"""
    
    predictor = ProductionPredictor(model_path, 'normalizer.pkl')
    
    # Performance metrics
    predictions = []
    targets = []
    
    for input_data, target in test_data:
        pred = predictor.predict(input_data)
        predictions.append(pred)
        targets.append(target)
    
    predictions = np.array(predictions)
    targets = np.array(targets)
    
    # Calculate metrics
    rmse = np.sqrt(np.mean((predictions - targets) ** 2))
    mae = np.mean(np.abs(predictions - targets))
    r2 = 1 - np.sum((targets - predictions) ** 2) / np.sum((targets - np.mean(targets)) ** 2)
    
    # Physics validation
    physics_violations = check_physics_constraints(predictions)
    
    print(f"Production Model Validation Results:")
    print(f"RMSE: {rmse:.4f}")
    print(f"MAE: {mae:.4f}")
    print(f"R²: {r2:.4f}")
    print(f"Physics violations: {physics_violations:.2%}")
    
    return {
        'rmse': rmse,
        'mae': mae,
        'r2': r2,
        'physics_violations': physics_violations
    }
```

---

## Best Practices Summary

### Data Preparation
1. **Quality Control**: Remove or interpolate missing data
2. **Physics Validation**: Ensure data within expected ranges
3. **Multi-Well**: Use data from multiple wells for robustness
4. **Sequence Length**: Use 640-720 points for optimal performance

### Model Configuration
1. **Start Simple**: Begin with base model templates
2. **Curve-Specific**: Use appropriate templates for each curve type
3. **Physics Constraints**: Always enable for realistic predictions
4. **Validation**: Use proper train/validation splits

### Training Process
1. **Monitor Closely**: Watch training and validation curves
2. **Early Stopping**: Prevent overfitting with patience
3. **Learning Rate**: Start with recommended values, tune as needed
4. **Checkpointing**: Save best models during training

### Production Deployment
1. **Model Export**: Use TorchScript or ONNX for deployment
2. **Validation**: Thoroughly test before production
3. **Monitoring**: Track prediction quality in production
4. **Updates**: Retrain periodically with new data

---

*This guide covers the complete Phase 2 MWLT training capabilities. For additional support, refer to the API documentation and example scripts.*
