#!/usr/bin/env python3
"""
Test script to verify the missing data fix
"""

import sys
import numpy as np
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent))

from examples.production_training_examples import ProductionTrainingExamples

def test_introduce_missing_data():
    """Test the _introduce_missing_data method with 1D data"""
    print("🧪 Testing _introduce_missing_data method...")
    
    # Create a mock trainer instance
    trainer = ProductionTrainingExamples(
        data_dir=str(Path(__file__).parent / "examples"),
        output_dir="test_outputs"
    )
    
    # Create test data (1D arrays as expected from _prepare_training_data)
    test_data = {
        'GR': np.random.randn(1000),
        'CNL': np.random.randn(1000),
        'AC': np.random.randn(1000),
        'DEN': np.random.randn(1000)
    }
    
    print(f"Original data shapes:")
    for curve, data in test_data.items():
        print(f"  {curve}: {data.shape}")
    
    # Test the method
    try:
        modified_data = trainer._introduce_missing_data(test_data, missing_fraction=0.1)
        print("✅ _introduce_missing_data method works correctly!")
        
        # Check results
        print(f"\nModified data shapes:")
        for curve, data in modified_data.items():
            print(f"  {curve}: {data.shape}")
            n_missing = np.sum(np.isnan(data))
            print(f"    Missing values: {n_missing}/{len(data)} ({n_missing/len(data)*100:.1f}%)")
            
        return True
        
    except Exception as e:
        print(f"❌ Error in _introduce_missing_data: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """Test edge cases for the missing data method"""
    print("\n🧪 Testing edge cases...")
    
    trainer = ProductionTrainingExamples(
        data_dir=str(Path(__file__).parent / "examples"),
        output_dir="test_outputs"
    )
    
    # Test with empty data
    empty_data = {'GR': np.array([])}
    try:
        result = trainer._introduce_missing_data(empty_data, missing_fraction=0.1)
        print("✅ Empty data handled correctly")
    except Exception as e:
        print(f"❌ Empty data test failed: {e}")
        return False
    
    # Test with very small data
    small_data = {'GR': np.array([1.0, 2.0, 3.0])}
    try:
        result = trainer._introduce_missing_data(small_data, missing_fraction=0.1)
        print("✅ Small data handled correctly")
    except Exception as e:
        print(f"❌ Small data test failed: {e}")
        return False
    
    # Test with 2D data (should raise error)
    bad_data = {'GR': np.random.randn(10, 5)}
    try:
        result = trainer._introduce_missing_data(bad_data, missing_fraction=0.1)
        print("❌ 2D data should have raised an error!")
        return False
    except ValueError as e:
        print(f"✅ 2D data correctly rejected: {e}")
    except Exception as e:
        print(f"❌ Unexpected error with 2D data: {e}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 Testing Missing Data Fix")
    print("="*50)
    
    success = True
    success &= test_introduce_missing_data()
    success &= test_edge_cases()
    
    if success:
        print("\n🎉 All tests passed! The fix should work correctly.")
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
