"""
Production Training Examples for Multi-Curve MWLT
==================================================

This module provides comprehensive, real-world training examples demonstrating
how to train models for each supported curve type using the GeneralTrainingManager
and actual well log data.

Supported Training Scenarios:
1. Density (DEN) prediction from GR, CNL, AC, RLLD
2. Neutron Porosity (CNL) prediction from GR, DEN, AC, RLLD  
3. Gamma Ray (GR) prediction from CNL, DEN, AC, RLLD
4. Resistivity (RLLD) prediction from GR, CNL, DEN, AC
5. Advanced multi-input scenarios with missing data handling

Each example includes:
- Data preparation and validation
- Model configuration and setup
- Training with curve-specific optimization
- Evaluation and performance analysis
- Model saving and deployment preparation
"""

import os
import sys
import numpy as np
import torch
import h5py
import logging
from pathlib import Path
from typing import Dict, List, Tuple, Optional, Any
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import (
        GeneralWellLogTransformer, GeneralDataNormalizer, GeneralWellLogDataset,
        GeneralTrainingManager, GeneralWellLogLoss, CurveSpecificLossFactory,
        get_model_template, get_training_template, get_data_template,
        create_vp_trainer, create_density_trainer, create_neutron_loss,
        CURVE_CONFIGURATIONS, validate_training_config
    )
    from vp_predictor.utils import get_device, save_checkpoint
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    sys.exit(1)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ProductionTrainingExamples:
    """
    Production-ready training examples for all supported curve types
    """
    
    def __init__(self, data_dir: str = ".", output_dir: str = "training_outputs"):
        """
        Initialize production training examples
        
        Args:
            data_dir: Directory containing well log data files (A1.hdf5, A2.hdf5)
            output_dir: Directory to save trained models and results
        """
        self.data_dir = Path(data_dir)
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.device = get_device()
        
        # Load and prepare data
        self.well_data = self._load_well_data()
        logger.info(f"Loaded data from {len(self.well_data)} wells")
        
    def _load_well_data(self) -> Dict[str, Dict[str, np.ndarray]]:
        """Load well log data from HDF5 files"""
        well_data = {}
        
        for data_file in ['A1.hdf5', 'A2.hdf5']:
            file_path = self.data_dir / data_file
            if file_path.exists():
                well_name = data_file.replace('.hdf5', '')
                curves = {}
                
                with h5py.File(file_path, 'r') as f:
                    for curve_name in f.keys():
                        data = f[curve_name][:]
                        if data.ndim > 1:
                            data = data.squeeze()
                        curves[curve_name] = data
                
                well_data[well_name] = curves
                logger.info(f"Loaded {well_name}: {list(curves.keys())}")
            else:
                logger.warning(f"Data file not found: {file_path}")
        
        return well_data
    
    def _prepare_training_data(self, input_curves: List[str], target_curve: str, 
                             sequence_length: int = 720) -> Dict[str, np.ndarray]:
        """
        Prepare combined training data from all wells
        
        Args:
            input_curves: List of input curve names
            target_curve: Target curve name
            sequence_length: Sequence length for windowing
            
        Returns:
            Combined data dictionary
        """
        # NOTE: GeneralWellLogDataset performs its own windowing. Here we
        # concatenate raw 1D series per curve across all wells, trimmed to a
        # common length per well. This avoids passing pre-windowed data.
        combined_data = {curve: [] for curve in input_curves + [target_curve]}

        for well_name, curves in self.well_data.items():
            # Check if all required curves are available
            missing_curves = [c for c in input_curves + [target_curve] if c not in curves]
            if missing_curves:
                logger.warning(f"Well {well_name} missing curves: {missing_curves}")
                continue

            # Find minimum aligned length across required curves in this well
            min_length = min(len(curves[curve]) for curve in input_curves + [target_curve])

            # Append trimmed raw series (no windowing here)
            for curve in input_curves + [target_curve]:
                combined_data[curve].append(curves[curve][:min_length])

        # Concatenate per-curve series across wells
        for curve in combined_data:
            if combined_data[curve]:
                combined_data[curve] = np.concatenate(combined_data[curve])
            else:
                combined_data[curve] = np.array([])

        logger.info(f"Prepared total series length: {len(combined_data[target_curve])}")
        return combined_data
    
    def train_density_prediction(self, save_model: bool = True) -> Dict[str, Any]:
        """
        Train a model for bulk density (DEN) prediction
        
        Scenario: Predict density from gamma ray, neutron, sonic, and resistivity
        Input: GR, CNL, AC, RLLD → Output: DEN
        
        Returns:
            Training results and model information
        """
        logger.info("🏗️ Starting Density Prediction Training")
        logger.info("="*60)
        
        # Configuration
        input_curves = ['GR', 'CNL', 'AC', 'RLLD']
        target_curve = 'DEN'
        
        # Prepare data
        data_dict = self._prepare_training_data(input_curves, target_curve)
        if len(data_dict[target_curve]) == 0:
            raise ValueError("No training data available for density prediction")
        
        # Split data
        train_data, val_data = self._split_data(data_dict, test_size=0.2)
        
        # Create model and normalizer
        model = GeneralWellLogTransformer.from_template('density_prediction')
        normalizer = GeneralDataNormalizer(input_curves, [target_curve])
        
        # Create datasets
        train_dataset = GeneralWellLogDataset(
            train_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('standard_sequence'),
            transform=True
        )
        
        val_dataset = GeneralWellLogDataset(
            val_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('standard_sequence'),
            transform=False
        )
        
        # Setup training
        training_config = get_training_template('density_training')
        training_config['max_epochs'] = 100  # Reasonable for production
        training_config['save_dir'] = str(self.output_dir / 'density_model')
        
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )
        
        # Train model
        logger.info(f"Training density model with {len(train_dataset)} train, {len(val_dataset)} val samples")
        training_results = trainer.train_model(save_path=training_config['save_dir'])
        
        # Evaluate model
        evaluation_results = trainer.evaluate_model()
        
        # Save model if requested (path provided by training manager)
        model_path = training_results.get('best_model_path')
        if save_model and model_path:
            logger.info(f"Model saved to: {model_path}")
        
        results = {
            'model_type': 'density_prediction',
            'input_curves': input_curves,
            'target_curve': target_curve,
            'training_samples': len(train_dataset),
            'validation_samples': len(val_dataset),
            'training_results': training_results,
            'evaluation_results': evaluation_results,
            'model_path': str(model_path) if (save_model and model_path) else None
        }
        
        logger.info("✅ Density prediction training completed successfully")
        return results
    
    def train_neutron_prediction(self, save_model: bool = True) -> Dict[str, Any]:
        """
        Train a model for neutron porosity (CNL) prediction
        
        Scenario: Predict neutron porosity from gamma ray, density, sonic, and resistivity
        Input: GR, DEN, AC, RLLD → Output: CNL
        
        Returns:
            Training results and model information
        """
        logger.info("🏗️ Starting Neutron Porosity Prediction Training")
        logger.info("="*60)
        
        # Configuration
        input_curves = ['GR', 'DEN', 'AC', 'RLLD']
        target_curve = 'CNL'
        
        # Prepare data
        data_dict = self._prepare_training_data(input_curves, target_curve)
        if len(data_dict[target_curve]) == 0:
            raise ValueError("No training data available for neutron prediction")
        
        # Split data
        train_data, val_data = self._split_data(data_dict, test_size=0.2)
        
        # Create model and normalizer
        model = GeneralWellLogTransformer(
            input_curves=input_curves,
            output_curves=[target_curve],
            model_config=get_model_template('vp_prediction')['model_config']  # Reuse base config
        )
        normalizer = GeneralDataNormalizer(input_curves, [target_curve])
        
        # Create datasets with robust handling (neutron can be noisy)
        train_dataset = GeneralWellLogDataset(
            train_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            transform=True
        )
        
        val_dataset = GeneralWellLogDataset(
            val_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            transform=False
        )
        
        # Setup training with robust loss for neutron (can have outliers)
        training_config = get_training_template('neutron_training')
        training_config['max_epochs'] = 120  # Neutron may need more epochs
        training_config['save_dir'] = str(self.output_dir / 'neutron_model')
        
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )
        
        # Train model
        logger.info(f"Training neutron model with {len(train_dataset)} train, {len(val_dataset)} val samples")
        training_results = trainer.train_model(save_path=training_config['save_dir'])
        
        # Evaluate model
        evaluation_results = trainer.evaluate_model()
        
        # Save model if requested (path provided by training manager)
        model_path = training_results.get('best_model_path')
        if save_model and model_path:
            logger.info(f"Model saved to: {model_path}")
        
        results = {
            'model_type': 'neutron_prediction',
            'input_curves': input_curves,
            'target_curve': target_curve,
            'training_samples': len(train_dataset),
            'validation_samples': len(val_dataset),
            'training_results': training_results,
            'evaluation_results': evaluation_results,
            'model_path': str(model_path) if save_model else None
        }
        
        logger.info("✅ Neutron porosity prediction training completed successfully")
        return results
    
    def _split_data(self, data_dict: Dict[str, np.ndarray], test_size: float = 0.2) -> Tuple[Dict, Dict]:
        """Split data into train and validation sets"""
        # Get indices for splitting
        n_samples = len(list(data_dict.values())[0])
        indices = np.arange(n_samples)
        train_idx, val_idx = train_test_split(indices, test_size=test_size, random_state=42)
        
        # Split data
        train_data = {curve: data[train_idx] for curve, data in data_dict.items()}
        val_data = {curve: data[val_idx] for curve, data in data_dict.items()}
        
        return train_data, val_data

    def train_gamma_ray_prediction(self, save_model: bool = True) -> Dict[str, Any]:
        """
        Train a model for gamma ray (GR) prediction

        Scenario: Predict gamma ray from neutron, density, sonic, and resistivity
        Input: CNL, DEN, AC, RLLD → Output: GR

        Returns:
            Training results and model information
        """
        logger.info("🏗️ Starting Gamma Ray Prediction Training")
        logger.info("="*60)

        # Configuration
        input_curves = ['CNL', 'DEN', 'AC', 'RLLD']
        target_curve = 'GR'

        # Prepare data
        data_dict = self._prepare_training_data(input_curves, target_curve)
        if len(data_dict[target_curve]) == 0:
            raise ValueError("No training data available for gamma ray prediction")

        # Split data
        train_data, val_data = self._split_data(data_dict, test_size=0.2)

        # Create model and normalizer
        model = GeneralWellLogTransformer(
            input_curves=input_curves,
            output_curves=[target_curve],
            model_config=get_model_template('vp_prediction')['model_config']
        )
        normalizer = GeneralDataNormalizer(input_curves, [target_curve])

        # Create datasets
        train_dataset = GeneralWellLogDataset(
            train_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('standard_sequence'),
            transform=True
        )

        val_dataset = GeneralWellLogDataset(
            val_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('standard_sequence'),
            transform=False
        )

        # Setup training
        training_config = get_training_template('gamma_ray_training')
        training_config['max_epochs'] = 80  # GR typically converges faster
        training_config['save_dir'] = str(self.output_dir / 'gamma_ray_model')

        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )

        # Train model
        logger.info(f"Training gamma ray model with {len(train_dataset)} train, {len(val_dataset)} val samples")
        training_results = trainer.train_model(save_path=training_config['save_dir'])

        # Evaluate model
        evaluation_results = trainer.evaluate_model()

        # Save model if requested (path provided by training manager)
        model_path = training_results.get('best_model_path')
        if save_model and model_path:
            logger.info(f"Model saved to: {model_path}")

        results = {
            'model_type': 'gamma_ray_prediction',
            'input_curves': input_curves,
            'target_curve': target_curve,
            'training_samples': len(train_dataset),
            'validation_samples': len(val_dataset),
            'training_results': training_results,
            'evaluation_results': evaluation_results,
            'model_path': str(model_path) if save_model else None
        }

        logger.info("✅ Gamma ray prediction training completed successfully")
        return results

    def train_resistivity_prediction(self, save_model: bool = True) -> Dict[str, Any]:
        """
        Train a model for resistivity (RLLD) prediction

        Scenario: Predict resistivity from gamma ray, neutron, density, and sonic
        Input: GR, CNL, DEN, AC → Output: RLLD

        Returns:
            Training results and model information
        """
        logger.info("🏗️ Starting Resistivity Prediction Training")
        logger.info("="*60)

        # Configuration
        input_curves = ['GR', 'CNL', 'DEN', 'AC']
        target_curve = 'RLLD'

        # Prepare data
        data_dict = self._prepare_training_data(input_curves, target_curve)
        if len(data_dict[target_curve]) == 0:
            raise ValueError("No training data available for resistivity prediction")

        # Split data
        train_data, val_data = self._split_data(data_dict, test_size=0.2)

        # Create model and normalizer
        model = GeneralWellLogTransformer(
            input_curves=input_curves,
            output_curves=[target_curve],
            model_config=get_model_template('vp_prediction')['model_config']
        )
        normalizer = GeneralDataNormalizer(input_curves, [target_curve])

        # Create datasets with robust handling (resistivity has wide range)
        train_dataset = GeneralWellLogDataset(
            train_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            transform=True
        )

        val_dataset = GeneralWellLogDataset(
            val_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            transform=False
        )

        # Setup training with resistivity-specific configuration
        training_config = get_training_template('resistivity_training')
        training_config['max_epochs'] = 150  # Resistivity may need more epochs due to log-scale
        training_config['save_dir'] = str(self.output_dir / 'resistivity_model')

        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )

        # Train model
        logger.info(f"Training resistivity model with {len(train_dataset)} train, {len(val_dataset)} val samples")
        training_results = trainer.train_model(save_path=training_config['save_dir'])

        # Evaluate model
        evaluation_results = trainer.evaluate_model()

        # Save model if requested (path provided by training manager)
        model_path = training_results.get('best_model_path')
        if save_model and model_path:
            logger.info(f"Model saved to: {model_path}")

        results = {
            'model_type': 'resistivity_prediction',
            'input_curves': input_curves,
            'target_curve': target_curve,
            'training_samples': len(train_dataset),
            'validation_samples': len(val_dataset),
            'training_results': training_results,
            'evaluation_results': evaluation_results,
            'model_path': str(model_path) if save_model else None
        }

        logger.info("✅ Resistivity prediction training completed successfully")
        return results

    def train_all_curve_types(self, save_models: bool = True) -> Dict[str, Any]:
        """
        Train models for all supported curve types in sequence

        Returns:
            Combined results from all training scenarios
        """
        logger.info("🚀 Starting Comprehensive Multi-Curve Training")
        logger.info("="*80)

        all_results = {}

        try:
            # Train density prediction
            all_results['density'] = self.train_density_prediction(save_models)

            # Train neutron prediction
            all_results['neutron'] = self.train_neutron_prediction(save_models)

            # Train gamma ray prediction
            all_results['gamma_ray'] = self.train_gamma_ray_prediction(save_models)

            # Train resistivity prediction
            all_results['resistivity'] = self.train_resistivity_prediction(save_models)

            # Generate summary report
            self._generate_training_summary(all_results)

        except Exception as e:
            logger.error(f"Training failed: {e}")
            raise

        logger.info("🎉 All curve type training completed successfully!")
        return all_results

    def train_with_missing_data_scenario(self, target_curve: str = 'DEN') -> Dict[str, Any]:
        """
        Advanced scenario: Train with missing data handling

        Demonstrates robust training when some input curves have missing sections
        """
        logger.info(f"🔧 Advanced Training: Missing Data Scenario for {target_curve}")
        logger.info("="*60)

        # Configuration for missing data scenario
        input_curves = ['GR', 'CNL', 'AC', 'RLLD']
        if target_curve in input_curves:
            input_curves.remove(target_curve)

        # Prepare data with artificial missing sections
        data_dict = self._prepare_training_data(input_curves, target_curve)

        # Introduce missing data artificially for testing
        data_dict = self._introduce_missing_data(data_dict, missing_fraction=0.1)

        # Split data
        train_data, val_data = self._split_data(data_dict, test_size=0.2)

        # Create model and normalizer
        model = GeneralWellLogTransformer(
            input_curves=input_curves,
            output_curves=[target_curve],
            model_config=get_model_template('missing_section_fill')['model_config']
        )
        normalizer = GeneralDataNormalizer(input_curves, [target_curve])

        # Create datasets with robust missing data handling
        train_dataset = GeneralWellLogDataset(
            train_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            quality_threshold=0.6,  # Lower threshold for missing data
            transform=True
        )

        val_dataset = GeneralWellLogDataset(
            val_data, input_curves, target_curve, normalizer,
            sequence_config=get_data_template('robust_sequence'),
            missing_data_strategy='interpolation',
            quality_threshold=0.6,
            transform=False
        )

        # Setup robust training configuration
        training_config = get_training_template('robust_training')
        training_config['target_curve'] = target_curve
        training_config['max_epochs'] = 100
        training_config['save_dir'] = str(self.output_dir / f'missing_data_{target_curve}_model')

        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )

        # Train model
        logger.info(f"Training {target_curve} model with missing data handling")
        training_results = trainer.train_model(save_path=training_config['save_dir'])

        # Evaluate model
        evaluation_results = trainer.evaluate_model()

        results = {
            'model_type': f'{target_curve}_missing_data',
            'input_curves': input_curves,
            'target_curve': target_curve,
            'training_samples': len(train_dataset),
            'validation_samples': len(val_dataset),
            'missing_data_fraction': 0.1,
            'training_results': training_results,
            'evaluation_results': evaluation_results
        }

        logger.info("✅ Missing data scenario training completed successfully")
        return results

    def _introduce_missing_data(self, data_dict: Dict[str, np.ndarray],
                              missing_fraction: float = 0.1) -> Dict[str, np.ndarray]:
        """
        Introduce artificial missing data for testing robust training

        Args:
            data_dict: Dictionary of curve names to 1D numpy arrays
            missing_fraction: Fraction of data points to set as missing (NaN)

        Returns:
            Modified data dictionary with artificial missing values
        """
        modified_data = {}

        for curve, data in data_dict.items():
            # Validate input data shape
            if data.ndim != 1:
                raise ValueError(f"Expected 1D data for curve '{curve}', got shape {data.shape}")

            if len(data) == 0:
                logger.warning(f"Empty data for curve '{curve}', skipping missing data introduction")
                modified_data[curve] = data.copy()
                continue

            modified_data[curve] = data.copy()

            # Calculate number of missing points
            n_total = len(data)
            n_missing = int(n_total * missing_fraction)

            if n_missing == 0:
                logger.info(f"No missing data to introduce for curve '{curve}' (fraction too small)")
                continue

            # Create random indices for missing data
            # Use replace=False to avoid setting the same index multiple times
            missing_indices = np.random.choice(n_total, size=n_missing, replace=False)

            # Set selected indices to NaN
            modified_data[curve][missing_indices] = np.nan

            logger.info(f"Introduced {n_missing}/{n_total} ({missing_fraction*100:.1f}%) missing values for curve '{curve}'")

        return modified_data

    def _generate_training_summary(self, all_results: Dict[str, Any]):
        """Generate a comprehensive training summary report"""
        summary_path = self.output_dir / 'training_summary.txt'

        with open(summary_path, 'w') as f:
            f.write("MWLT Multi-Curve Training Summary Report\n")
            f.write("="*50 + "\n\n")

            for curve_type, results in all_results.items():
                f.write(f"{curve_type.upper()} PREDICTION\n")
                f.write("-" * 30 + "\n")
                f.write(f"Target Curve: {results['target_curve']}\n")
                f.write(f"Input Curves: {', '.join(results['input_curves'])}\n")
                f.write(f"Training Samples: {results['training_samples']}\n")
                f.write(f"Validation Samples: {results['validation_samples']}\n")

                if 'evaluation_results' in results:
                    eval_results = results['evaluation_results']
                    if 'rmse' in eval_results:
                        f.write(f"Final RMSE: {eval_results['rmse']:.4f}\n")
                    if 'r2' in eval_results:
                        f.write(f"Final R²: {eval_results['r2']:.4f}\n")

                if results.get('model_path'):
                    f.write(f"Model Path: {results['model_path']}\n")

                f.write("\n")

        logger.info(f"Training summary saved to: {summary_path}")


def main():
    """
    Main function demonstrating all production training examples
    """
    print("🚀 MWLT Production Training Examples")
    print("="*80)
    print("Comprehensive training demonstrations for all supported curve types")

    # Initialize training examples
    trainer = ProductionTrainingExamples(
        data_dir=str(Path(__file__).parent),  # Use examples/ directory containing A1.hdf5, A2.hdf5
        output_dir="production_training_outputs"
    )

    try:
        # Option 1: Train all curve types
        print("\n📋 Option 1: Train all curve types")
        all_results = trainer.train_all_curve_types(save_models=True)

        # Option 2: Advanced missing data scenario
        print("\n📋 Option 2: Advanced missing data scenario")
        missing_data_results = trainer.train_with_missing_data_scenario(target_curve='DEN')

        print("\n🎉 All training examples completed successfully!")
        print(f"📁 Results saved in: {trainer.output_dir}")

        # Print summary
        print("\n📊 Training Summary:")
        for curve_type, results in all_results.items():
            print(f"  {curve_type}: {results['training_samples']} samples, "
                  f"Target: {results['target_curve']}")

    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
