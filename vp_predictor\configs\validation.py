"""
Configuration validation and error handling for General Well Log Transformer

Provides comprehensive validation for curve configurations, model templates,
and training parameters to ensure system reliability and catch errors early.
"""

import torch
from typing import Dict, List, Tuple, Any, Optional


class ConfigurationError(Exception):
    """Base exception for configuration-related errors"""
    pass


class CurveConfigurationError(ConfigurationError):
    """Raised when curve configuration is invalid"""
    pass


class ModelConfigurationError(ConfigurationError):
    """Raised when model configuration is invalid"""
    pass


class TrainingConfigurationError(ConfigurationError):
    """Raised when training configuration is invalid"""
    pass


def validate_curve_config(curve_config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate a single curve configuration
    
    Args:
        curve_config: Curve configuration dictionary
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Required fields
    required_fields = ['name', 'unit', 'type', 'physics_range', 'normalization']
    for field in required_fields:
        if field not in curve_config:
            messages.append(f"Missing required field: {field}")
            is_valid = False
    
    if not is_valid:
        return is_valid, messages
    
    # Validate curve type
    valid_types = ['input', 'output', 'both']
    if curve_config['type'] not in valid_types:
        messages.append(f"Invalid curve type: {curve_config['type']}. "
                       f"Must be one of {valid_types}")
        is_valid = False
    
    # Validate physics range
    physics_range = curve_config['physics_range']
    if not isinstance(physics_range, (tuple, list)) or len(physics_range) != 2:
        messages.append("Physics range must be a tuple/list of 2 values")
        is_valid = False
    elif physics_range[0] >= physics_range[1]:
        messages.append("Physics range minimum must be less than maximum")
        is_valid = False
    
    # Validate normalization configuration
    norm_config = curve_config['normalization']
    if not isinstance(norm_config, dict):
        messages.append("Normalization must be a dictionary")
        is_valid = False
    else:
        # Check required normalization fields
        if 'method' not in norm_config:
            messages.append("Normalization missing 'method' field")
            is_valid = False
        else:
            valid_methods = ['zscore', 'minmax', 'log_zscore', 'robust']
            if norm_config['method'] not in valid_methods:
                messages.append(f"Invalid normalization method: {norm_config['method']}. "
                               f"Must be one of {valid_methods}")
                is_valid = False
    
    # Validate activation function
    if 'activation' in curve_config:
        valid_activations = ['none', 'sigmoid', 'relu', 'tanh', 'softplus']
        if curve_config['activation'] not in valid_activations:
            messages.append(f"Invalid activation: {curve_config['activation']}. "
                           f"Must be one of {valid_activations}")
            is_valid = False
    
    # Validate preprocessing
    if 'preprocessing' in curve_config and curve_config['preprocessing'] is not None:
        valid_preprocessing = ['log10', 'log', 'sqrt', 'square']
        if curve_config['preprocessing'] not in valid_preprocessing:
            messages.append(f"Invalid preprocessing: {curve_config['preprocessing']}. "
                           f"Must be one of {valid_preprocessing}")
            is_valid = False
    
    return is_valid, messages


def validate_model_config(model_config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate model architecture configuration
    
    Args:
        model_config: Model configuration dictionary
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Required fields
    required_fields = ['res_blocks', 'encoder_layers', 'attention_heads', 'feature_dim']
    for field in required_fields:
        if field not in model_config:
            messages.append(f"Missing required field: {field}")
            is_valid = False
    
    if not is_valid:
        return is_valid, messages
    
    # Validate integer parameters
    int_params = {
        'res_blocks': (1, 12),
        'encoder_layers': (1, 16),
        'attention_heads': (1, 16),
        'feature_dim': (8, 512),
        'sequence_length': (64, 2048)
    }
    
    for param, (min_val, max_val) in int_params.items():
        if param in model_config:
            value = model_config[param]
            if not isinstance(value, int) or value < min_val or value > max_val:
                messages.append(f"Invalid {param}: {value}. "
                               f"Must be integer between {min_val} and {max_val}")
                is_valid = False
    
    # Validate feature_dim is divisible by attention_heads
    if 'feature_dim' in model_config and 'attention_heads' in model_config:
        if model_config['feature_dim'] % model_config['attention_heads'] != 0:
            messages.append("Feature dimension must be divisible by number of attention heads")
            is_valid = False
    
    # Validate model size consistency
    if 'model_size' in model_config:
        size = model_config['model_size']
        if size not in ['small', 'base', 'large']:
            messages.append(f"Invalid model size: {size}. Must be 'small', 'base', or 'large'")
            is_valid = False
    
    return is_valid, messages


def validate_training_config(training_config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate training configuration
    
    Args:
        training_config: Training configuration dictionary
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Validate learning rate
    if 'learning_rate' in training_config:
        lr = training_config['learning_rate']
        if not isinstance(lr, (int, float)) or lr <= 0 or lr > 1:
            messages.append(f"Invalid learning rate: {lr}. Must be between 0 and 1")
            is_valid = False
    
    # Validate batch size
    if 'batch_size' in training_config:
        bs = training_config['batch_size']
        if not isinstance(bs, int) or bs < 1 or bs > 512:
            messages.append(f"Invalid batch size: {bs}. Must be integer between 1 and 512")
            is_valid = False
    
    # Validate epochs
    if 'max_epochs' in training_config:
        epochs = training_config['max_epochs']
        if not isinstance(epochs, int) or epochs < 1 or epochs > 10000:
            messages.append(f"Invalid max epochs: {epochs}. Must be integer between 1 and 10000")
            is_valid = False
    
    # Validate patience
    if 'early_stopping_patience' in training_config:
        patience = training_config['early_stopping_patience']
        if not isinstance(patience, int) or patience < 1:
            messages.append(f"Invalid patience: {patience}. Must be positive integer")
            is_valid = False
    
    # Validate optimizer
    if 'optimizer' in training_config:
        valid_optimizers = ['adam', 'adamw', 'sgd', 'rmsprop']
        opt = training_config['optimizer'].lower()
        if opt not in valid_optimizers:
            messages.append(f"Invalid optimizer: {opt}. Must be one of {valid_optimizers}")
            is_valid = False
    
    # Validate loss weights
    if 'loss_weights' in training_config:
        weights = training_config['loss_weights']
        if not isinstance(weights, dict):
            messages.append("Loss weights must be a dictionary")
            is_valid = False
        else:
            for curve, weight in weights.items():
                if not isinstance(weight, (int, float)) or weight < 0:
                    messages.append(f"Invalid loss weight for {curve}: {weight}. "
                                   f"Must be non-negative number")
                    is_valid = False
    
    return is_valid, messages


def validate_data_config(data_config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate data processing configuration
    
    Args:
        data_config: Data configuration dictionary
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Validate validation split
    if 'validation_split' in data_config:
        split = data_config['validation_split']
        if not isinstance(split, (int, float)) or split < 0 or split >= 1:
            messages.append(f"Invalid validation split: {split}. Must be between 0 and 1")
            is_valid = False
    
    # Validate sequence lengths
    if 'total_sequence_length' in data_config:
        total_len = data_config['total_sequence_length']
        if not isinstance(total_len, int) or total_len < 64 or total_len > 4096:
            messages.append(f"Invalid total sequence length: {total_len}. "
                           f"Must be integer between 64 and 4096")
            is_valid = False
    
    if 'effective_sequence_length' in data_config:
        eff_len = data_config['effective_sequence_length']
        if not isinstance(eff_len, int) or eff_len < 32 or eff_len > 2048:
            messages.append(f"Invalid effective sequence length: {eff_len}. "
                           f"Must be integer between 32 and 2048")
            is_valid = False
    
    # Check sequence length consistency
    if ('total_sequence_length' in data_config and 
        'effective_sequence_length' in data_config):
        total_len = data_config['total_sequence_length']
        eff_len = data_config['effective_sequence_length']
        if eff_len > total_len:
            messages.append("Effective sequence length cannot be greater than total length")
            is_valid = False
    
    return is_valid, messages


def validate_full_config(config: Dict[str, Any]) -> Tuple[bool, List[str]]:
    """
    Validate a complete model configuration including all components
    
    Args:
        config: Complete configuration dictionary
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Check high-level structure
    required_sections = ['input_curves', 'output_curves', 'model_config', 
                        'training_config', 'data_config']
    
    for section in required_sections:
        if section not in config:
            messages.append(f"Missing configuration section: {section}")
            is_valid = False
    
    if not is_valid:
        return is_valid, messages
    
    # Validate input/output curves
    if not isinstance(config['input_curves'], list) or len(config['input_curves']) == 0:
        messages.append("Input curves must be non-empty list")
        is_valid = False
    
    if not isinstance(config['output_curves'], list) or len(config['output_curves']) == 0:
        messages.append("Output curves must be non-empty list")
        is_valid = False
    
    # Check for curve overlap
    if is_valid:
        input_set = set(config['input_curves'])
        output_set = set(config['output_curves'])
        overlap = input_set & output_set
        if overlap:
            messages.append(f"Curves cannot be both input and output: {list(overlap)}")
            is_valid = False
    
    # Validate individual configuration sections
    model_valid, model_messages = validate_model_config(config['model_config'])
    training_valid, training_messages = validate_training_config(config['training_config'])
    data_valid, data_messages = validate_data_config(config['data_config'])
    
    messages.extend(model_messages)
    messages.extend(training_messages)
    messages.extend(data_messages)
    
    is_valid = is_valid and model_valid and training_valid and data_valid
    
    return is_valid, messages


def validate_device_config(device: str) -> Tuple[bool, List[str]]:
    """
    Validate device configuration and availability
    
    Args:
        device: Device specification ('auto', 'cpu', 'cuda', 'cuda:0', etc.)
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    if device == 'auto':
        # Auto selection is always valid
        pass
    elif device == 'cpu':
        # CPU is always available
        pass
    elif device.startswith('cuda'):
        if not torch.cuda.is_available():
            messages.append("CUDA requested but not available")
            is_valid = False
        elif ':' in device:
            # Specific GPU requested
            try:
                gpu_id = int(device.split(':')[1])
                if gpu_id >= torch.cuda.device_count():
                    messages.append(f"GPU {gpu_id} not available. "
                                   f"Available GPUs: 0-{torch.cuda.device_count()-1}")
                    is_valid = False
            except ValueError:
                messages.append(f"Invalid GPU ID format: {device}")
                is_valid = False
    else:
        messages.append(f"Unknown device type: {device}")
        is_valid = False
    
    return is_valid, messages


def create_validation_report(config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Create comprehensive validation report for a configuration
    
    Args:
        config: Configuration to validate
        
    Returns:
        dict: Validation report with detailed results
    """
    report = {
        'overall_valid': True,
        'sections': {},
        'summary': {
            'total_errors': 0,
            'total_warnings': 0
        }
    }
    
    # Validate each section
    sections_to_validate = [
        ('model_config', validate_model_config),
        ('training_config', validate_training_config),
        ('data_config', validate_data_config)
    ]
    
    for section_name, validator in sections_to_validate:
        if section_name in config:
            is_valid, messages = validator(config[section_name])
            report['sections'][section_name] = {
                'valid': is_valid,
                'messages': messages,
                'error_count': len([m for m in messages if 'Invalid' in m or 'Missing' in m]),
                'warning_count': len([m for m in messages if 'Warning' in m])
            }
            
            if not is_valid:
                report['overall_valid'] = False
            
            report['summary']['total_errors'] += report['sections'][section_name]['error_count']
            report['summary']['total_warnings'] += report['sections'][section_name]['warning_count']
    
    # Overall validation
    is_valid, messages = validate_full_config(config)
    report['sections']['overall'] = {
        'valid': is_valid,
        'messages': messages,
        'error_count': len([m for m in messages if 'Invalid' in m or 'Missing' in m]),
        'warning_count': len([m for m in messages if 'Warning' in m])
    }
    
    if not is_valid:
        report['overall_valid'] = False
    
    report['summary']['total_errors'] += report['sections']['overall']['error_count']
    report['summary']['total_warnings'] += report['sections']['overall']['warning_count']
    
    return report


def print_validation_report(report: Dict[str, Any], verbose: bool = True):
    """
    Print a formatted validation report
    
    Args:
        report: Validation report from create_validation_report
        verbose: If True, print detailed messages
    """
    print(f"Configuration Validation Report")
    print(f"=" * 50)
    print(f"Overall Valid: {'✅' if report['overall_valid'] else '❌'}")
    print(f"Total Errors: {report['summary']['total_errors']}")
    print(f"Total Warnings: {report['summary']['total_warnings']}")
    print()
    
    if verbose:
        for section_name, section_report in report['sections'].items():
            status = '✅' if section_report['valid'] else '❌'
            print(f"{section_name.upper()}: {status}")
            
            if section_report['messages']:
                for message in section_report['messages']:
                    if 'Invalid' in message or 'Missing' in message:
                        print(f"  ❌ {message}")
                    elif 'Warning' in message:
                        print(f"  ⚠️ {message}")
                    else:
                        print(f"  ℹ️ {message}")
            print()