"""
Training script for dedicated Vp (Sonic Velocity) prediction model
Based on the existing transformer architecture but optimized for sonic prediction
"""
import os
import argparse
import time
import pandas as pd
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np

# Import existing modules
from utils import get_device, save_checkpoint, load_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from model import MWLT_Small, MWLT_Base, MWLT_Large
from dataset import WellDataset
from las_processor import LASProcessor

def create_vp_training_data():
    """
    Create training and validation datasets for Vp prediction from A1.hdf5 and A2.hdf5
    """
    print("=== Creating Vp Training Data ===")
    
    processor = LASProcessor()
    
    # Create directories
    train_dir = "../data_vp_training/train"
    val_dir = "../data_vp_training/val"
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    # Process A1.hdf5 and A2.hdf5
    source_files = ['../A1.hdf5', '../A2.hdf5']
    
    for i, source_file in enumerate(source_files):
        if not os.path.exists(source_file):
            print(f"Warning: {source_file} not found, skipping...")
            continue
        
        print(f"Processing {source_file}...")
        
        # Load and process data
        curves = processor.process_hdf5_to_las_format(source_file)
        
        # Create multiple training samples by sliding window
        total_length = len(curves['AC'])
        window_size = 720
        step_size = 360  # 50% overlap
        
        sample_count = 0
        for start_idx in range(0, total_length - window_size + 1, step_size):
            end_idx = start_idx + window_size
            
            # Extract window
            sample_curves = {}
            for curve_name, data in curves.items():
                sample_curves[curve_name] = data[start_idx:end_idx]
            
            # Prepare for model (resample to exactly 720 points)
            input_features, target_ac = processor.prepare_for_prediction(sample_curves, 720)
            
            # Create filename
            file_prefix = f"A{i+1}_sample_{sample_count:03d}"
            
            # Decide train/val split (80/20)
            if sample_count % 5 == 0:  # 20% for validation
                output_dir = val_dir
            else:
                output_dir = train_dir
            
            output_file = os.path.join(output_dir, f"{file_prefix}.hdf5")
            
            # Save as HDF5 file compatible with WellDataset
            import h5py
            with h5py.File(output_file, 'w') as f:
                # Input curves
                curve_names = ['GR', 'CNL', 'DEN', 'RLLD']
                for j, curve_name in enumerate(curve_names):
                    f.create_dataset(curve_name, data=input_features[j].astype(np.float32))
                
                # Target curve (AC)
                f.create_dataset('AC', data=target_ac.squeeze().astype(np.float32))
                
                # Metadata
                f.attrs['source_file'] = source_file
                f.attrs['window_start'] = start_idx
                f.attrs['window_end'] = end_idx
                f.attrs['sample_id'] = sample_count
            
            sample_count += 1
        
        print(f"Created {sample_count} samples from {source_file}")
    
    # Count final datasets
    train_files = len([f for f in os.listdir(train_dir) if f.endswith('.hdf5')])
    val_files = len([f for f in os.listdir(val_dir) if f.endswith('.hdf5')])
    
    print(f"\nDataset created:")
    print(f"  Training samples: {train_files}")
    print(f"  Validation samples: {val_files}")
    print(f"  Train directory: {train_dir}")
    print(f"  Val directory: {val_dir}")
    
    return train_dir, val_dir

def train_vp_model(args):
    """
    Main training function for Vp prediction model
    """
    print("=== Training Dedicated Vp Prediction Model ===")
    
    # Device setup with improved detection
    device = get_device(int(args.device))
    
    # Create save directory
    if not os.path.exists(args.save_path):
        os.makedirs(args.save_path)
    
    # Save hyperparameters
    argsDict = args.__dict__
    with open(os.path.join(args.save_path, "hyperparameters.txt"), "w") as f:
        f.write("-" * 20 + " VP MODEL TRAINING " + "-" * 20 + "\n")
        f.write(f"Training started: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write("-" * 60 + "\n")
        for eachArg, value in argsDict.items():
            f.write(f"{eachArg:20s}: {str(value)}\n")
        f.write("-" * 60 + "\n")
    
    # Create training data if needed
    if not os.path.exists(args.train_files_path) or not os.path.exists(args.val_files_path):
        print("Training data not found. Creating from A1.hdf5 and A2.hdf5...")
        train_dir, val_dir = create_vp_training_data()
        args.train_files_path = train_dir
        args.val_files_path = val_dir
    
    # Prepare datasets
    print(f"\nLoading datasets...")
    print(f"  Train path: {args.train_files_path}")
    print(f"  Val path: {args.val_files_path}")
    
    train_dataset = WellDataset(
        root_path=args.train_files_path,
        input_curves=args.input_curves,
        output_curves=args.output_curves,
        transform=args.transform,
        total_seqlen=args.total_seqlen,
        effect_seqlen=args.effect_seqlen
    )
    
    val_dataset = WellDataset(
        root_path=args.val_files_path,
        input_curves=args.input_curves,
        output_curves=args.output_curves,
        transform=False,  # No augmentation for validation
        total_seqlen=args.total_seqlen,
        effect_seqlen=args.effect_seqlen
    )
    
    print(f"  Training samples: {len(train_dataset)}")
    print(f"  Validation samples: {len(val_dataset)}")
    
    # Create data loaders
    train_loader = DataLoader(dataset=train_dataset, batch_size=args.batch_size, shuffle=True)
    val_loader = DataLoader(dataset=val_dataset, batch_size=1, shuffle=False)
    
    # Build model
    print(f"\nBuilding model: {args.model_type}")
    print(f"  Input channels: {len(args.input_curves)} {args.input_curves}")
    print(f"  Output channels: {len(args.output_curves)} {args.output_curves}")
    
    if args.model_type == "small":
        model = MWLT_Small(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    elif args.model_type == "base":
        model = MWLT_Base(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    elif args.model_type == "large":
        model = MWLT_Large(
            in_channels=len(args.input_curves),
            out_channels=len(args.output_curves),
            feature_num=args.feature_num,
            use_pe=args.use_pe,
            drop=args.drop,
            attn_drop=args.attn_drop,
            position_drop=args.position_drop
        )
    else:
        raise ValueError(f"Unknown model type: {args.model_type}")
    
    model = model.to(device)
    
    # Print model info
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    print(f"  Total parameters: {total_params:,}")
    print(f"  Trainable parameters: {trainable_params:,}")
    
    # Setup optimizer and loss
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    criterion = nn.MSELoss().to(device)
    
    # Early stopping
    early_stopping = EarlyStopping(
        patience=args.patience,
        path=os.path.join(args.save_path, "best_vp_model.pth"),
        verbose=True
    )
    
    start_epoch = 1
    
    # Continue training if specified
    if args.continue_train and args.checkpoint_path:
        print(f"Loading checkpoint: {args.checkpoint_path}")
        model_dict, epoch, loss = load_checkpoint(args.checkpoint_path, device)
        model.load_state_dict(model_dict)
        start_epoch = epoch + 1
        print(f"Resuming from epoch {start_epoch}")
    
    # Training loop
    print(f"\nStarting training...")
    print(f"  Device: {device}")
    print(f"  Epochs: {start_epoch} to {args.epochs}")
    print(f"  Batch size: {args.batch_size}")
    print(f"  Learning rate: {args.learning_rate}")
    print("-" * 80)
    
    training_log = {"epoch": [], "train_loss": [], "val_loss": [], "val_rmse": [], "val_r2": []}
    
    for epoch in range(start_epoch, args.epochs + 1):
        epoch_start_time = time.time()
        
        # Training phase
        model.train()
        train_total_loss = 0.0
        
        for step, (conditions, targets) in enumerate(train_loader):
            conditions = conditions.to(device)
            targets = targets.to(device)
            
            # Forward pass
            predictions = model(conditions)
            loss = criterion(predictions, targets)
            
            # Backward pass
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_total_loss += loss.item()
        
        train_loss_epoch = train_total_loss / len(train_loader)
        
        # Validation phase
        model.eval()
        val_total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for conditions, targets in val_loader:
                conditions = conditions.to(device)
                targets = targets.to(device)
                
                predictions = model(conditions)
                loss = criterion(predictions, targets)
                val_total_loss += loss.item()
                
                # Collect for metrics
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        val_loss_epoch = val_total_loss / len(val_loader)
        
        # Calculate additional metrics
        all_predictions = np.concatenate(all_predictions, axis=0).flatten()
        all_targets = np.concatenate(all_targets, axis=0).flatten()
        val_rmse = cal_RMSE(all_predictions, all_targets)
        val_r2 = cal_R2(all_predictions, all_targets)
        
        # Log results
        training_log["epoch"].append(epoch)
        training_log["train_loss"].append(train_loss_epoch)
        training_log["val_loss"].append(val_loss_epoch)
        training_log["val_rmse"].append(val_rmse)
        training_log["val_r2"].append(val_r2)
        
        # Save training log
        df_log = pd.DataFrame(training_log)
        df_log.to_csv(os.path.join(args.save_path, "training_log.csv"), index=False)
        
        # Print progress
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch [{epoch:4d}/{args.epochs}] | "
              f"Train Loss: {train_loss_epoch:.6f} | "
              f"Val Loss: {val_loss_epoch:.6f} | "
              f"Val RMSE: {val_rmse:.4f} | "
              f"Val R²: {val_r2:.4f} | "
              f"Time: {epoch_time:.2f}s")
        
        # Save checkpoint and check early stopping
        state = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "loss": val_loss_epoch,
            "epoch": epoch,
            "rmse": val_rmse,
            "r2": val_r2
        }
        
        early_stopping(state, model)
        
        if early_stopping.early_stop:
            print(f"Early stopping triggered at epoch {epoch}")
            break
    
    print("-" * 80)
    print("Training completed!")
    print(f"Best model saved to: {os.path.join(args.save_path, 'best_vp_model.pth')}")
    print(f"Training log saved to: {os.path.join(args.save_path, 'training_log.csv')}")

def main():
    parser = argparse.ArgumentParser(description="Train dedicated Vp prediction model")
    
    # Model configuration
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"],
                       help="Model architecture size")
    parser.add_argument("--feature_num", type=int, default=64, help="Feature dimensions")
    parser.add_argument("--use_pe", type=bool, default=True, help="Use position embedding")
    parser.add_argument("--drop", type=float, default=0.1, help="Dropout rate")
    parser.add_argument("--attn_drop", type=float, default=0.1, help="Attention dropout rate")
    parser.add_argument("--position_drop", type=float, default=0.1, help="Position dropout rate")
    
    # Data configuration - OPTIMIZED FOR VP PREDICTION
    parser.add_argument("--input_curves", default=["GR", "CNL", "DEN", "RLLD"], type=list,
                       help="Input curves for Vp prediction")
    parser.add_argument("--output_curves", default=["AC"], type=list,
                       help="Output curve (AC for sonic velocity)")
    parser.add_argument("--total_seqlen", default=720, type=int, help="Total sequence length")
    parser.add_argument("--effect_seqlen", default=640, type=int, help="Effective sequence length")
    parser.add_argument("--transform", default=True, type=bool, help="Use data augmentation")
    
    # Training configuration
    parser.add_argument("--batch_size", type=int, default=16, help="Batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-4, help="Learning rate")
    parser.add_argument("--epochs", type=int, default=1000, help="Training epochs")
    parser.add_argument("--patience", type=int, default=100, help="Early stopping patience")
    
    # Paths
    parser.add_argument("--train_files_path", default="../data_vp_training/train", type=str,
                       help="Training data path")
    parser.add_argument("--val_files_path", default="../data_vp_training/val", type=str,
                       help="Validation data path")
    parser.add_argument("--save_path", type=str, default="../vp_model_training",
                       help="Model save directory")
    
    # Device and continuation
    parser.add_argument("--device", type=str, default="0", help="GPU device ID")
    parser.add_argument("--continue_train", type=bool, default=False, help="Continue training")
    parser.add_argument("--checkpoint_path", type=str, default=None, help="Checkpoint path")
    
    args = parser.parse_args()
    train_vp_model(args)

if __name__ == "__main__":
    main()
