{"learning_rate": 8e-05, "weight_decay": 2e-05, "batch_size": 12, "max_epochs": 120, "patience": 40, "optimizer": "adamw", "scheduler": "cosine", "scheduler_params": {"T_max": 100, "eta_min": 1e-07}, "loss_config": {"type": "curve_specific", "custom_params": {"loss_type": "huber", "constraint_weight": 0.8, "robust_loss_delta": 1.5, "physics_constraints": true}}, "save_best_only": true, "save_frequency": 10, "validation_frequency": 1, "gradient_clipping": 1.2, "mixed_precision": false, "description": "Optimized configuration for neutron porosity (CNL) prediction", "target_curve": "CNL", "save_dir": "production_training_outputs\\neutron_model"}