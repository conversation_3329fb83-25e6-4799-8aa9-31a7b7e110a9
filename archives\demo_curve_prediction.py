"""
Demonstration of General Well Log Transformer - Multi-Curve Prediction

This demo showcases the flexible prediction capabilities implemented in Phase 2:
- Predict different curve types (VP, Density, Neutron, Gamma Ray, Resistivity)
- Use different input combinations
- Apply curve-specific configurations and physics constraints
- Compare with specialized models

Current Status: Phase 2 Complete
Ready for Phase 3: API Development
"""

import sys
import os
import numpy as np
import torch
import logging
import matplotlib.pyplot as plt
from typing import Dict, List, Any

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Device detection
def get_device():
    """Get the appropriate device (CPU/GPU) for computations"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        logger.info(f"CUDA available. Using GPU: {device}")
        logger.info(f"GPU Name: {torch.cuda.get_device_name(0)}")
        logger.info(f"GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")
    else:
        device = torch.device('cpu')
        logger.info("CUDA not available. Using CPU")
    return device

def create_realistic_well_data(num_points: int = 2000) -> Dict[str, np.ndarray]:
    """
    Create realistic synthetic well log data with proper curve relationships
    
    This simulates a real well with geological relationships between curves:
    - Shale intervals: High GR, high neutron, low resistivity
    - Clean sand: Low GR, low neutron, high resistivity
    - Tight intervals: Variable density, high acoustic
    """
    logger.info(f"Creating {num_points} points of realistic synthetic well data...")
    
    # Create depth array
    depth = np.linspace(2000, 2500, num_points)  # 500ft interval
    
    # Create geological layers with realistic relationships
    data = {}
    
    # Base trends with depth
    depth_trend = (depth - 2000) / 500  # 0 to 1
    
    # Gamma Ray (0-200 API) - geological indicator
    shale_layers = np.sin(depth_trend * 8) > 0.3  # Alternating shale/sand
    clean_sand = np.sin(depth_trend * 12) > 0.6   # Clean sand layers
    
    gr_base = np.where(shale_layers, 
                      np.random.uniform(80, 150, num_points),    # Shale: high GR
                      np.random.uniform(20, 60, num_points))     # Sand: low GR
    gr_base = np.where(clean_sand, 
                      np.random.uniform(10, 30, num_points),     # Clean sand: very low GR
                      gr_base)
    data['GR'] = np.clip(gr_base + np.random.normal(0, 10, num_points), 0, 200)
    
    # Neutron Porosity (0-40%) - porosity indicator
    porosity_factor = 1.0 - (data['GR'] / 200.0)  # Lower GR = higher porosity potential
    cnl_base = porosity_factor * 30 + np.random.uniform(5, 15, num_points)
    cnl_base = np.where(shale_layers,
                       cnl_base + np.random.uniform(10, 20, num_points),  # Shale effect
                       cnl_base)
    data['CNL'] = np.clip(cnl_base + np.random.normal(0, 3, num_points), 0, 60)
    
    # Bulk Density (1.8-2.8 g/cm3) - lithology indicator
    # Lower porosity = higher density, shale = lower density
    porosity_est = data['CNL'] / 40.0  # Approximate porosity from neutron
    den_base = 2.65 - porosity_est * 0.65  # Quartz matrix density
    den_base = np.where(shale_layers,
                       den_base - 0.2,  # Shale is less dense
                       den_base)
    data['DEN'] = np.clip(den_base + np.random.normal(0, 0.05, num_points), 1.5, 3.0)
    
    # Resistivity (0.5-1000 ohm-m) - fluid indicator
    # Shale: low resistivity, clean sand with hydrocarbons: high resistivity
    rlld_base = np.where(shale_layers,
                        np.random.lognormal(0.5, 1, num_points),      # Shale: low resistivity
                        np.random.lognormal(2, 1.5, num_points))     # Sand: higher resistivity
    rlld_base = np.where(clean_sand,
                        rlld_base * np.random.uniform(5, 20, num_points),  # Hydrocarbon zones
                        rlld_base)
    data['RLLD'] = np.clip(rlld_base, 0.1, 1000)
    
    # Acoustic/Vp (60-140 μs/ft) - porosity and lithology indicator
    # Higher porosity = higher acoustic transit time (slower)
    compaction_factor = depth_trend * 0.3  # Compaction with depth
    ac_base = 60 + porosity_est * 40 - compaction_factor * 20
    ac_base = np.where(shale_layers,
                      ac_base + np.random.uniform(10, 30, num_points),  # Shale: slower
                      ac_base)
    data['AC'] = np.clip(ac_base + np.random.normal(0, 5, num_points), 40, 400)
    data['VP'] = data['AC'].copy()  # VP and AC are equivalent
    
    logger.info("✓ Realistic well data created with geological relationships")
    logger.info(f"  GR range: {data['GR'].min():.1f} - {data['GR'].max():.1f} API")
    logger.info(f"  CNL range: {data['CNL'].min():.1f} - {data['CNL'].max():.1f} %")
    logger.info(f"  DEN range: {data['DEN'].min():.2f} - {data['DEN'].max():.2f} g/cm³")
    logger.info(f"  RLLD range: {data['RLLD'].min():.1f} - {data['RLLD'].max():.1f} ohm-m")
    logger.info(f"  VP range: {data['VP'].min():.1f} - {data['VP'].max():.1f} μs/ft")
    
    return data

def demo_vp_prediction():
    """Demo 1: VP Prediction (Backward Compatible)"""
    logger.info("\n" + "="*60)
    logger.info("🎯 DEMO 1: VP (Sonic Velocity) Prediction")
    logger.info("="*60)
    logger.info("Configuration: Standard VP prediction from GR, CNL, DEN, RLLD")
    logger.info("Expected: Backward compatible with VpTransformer")
    
    try:
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDataNormalizer, 
            GeneralWellLogDataset, GeneralTrainingManager,
            get_model_template, get_training_template
        )
        
        # Create synthetic well data
        well_data = create_realistic_well_data(3000)
        
        # Setup VP prediction (backward compatible)
        model_config = get_model_template('vp_prediction')
        logger.info(f"Model config: {model_config}")
        
        # Create model and normalizer
        device = get_device()
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        model = model.to(device)
        normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'], 
            output_curves=['VP']
        )
        
        # Create dataset
        dataset = GeneralWellLogDataset(
            data_dict=well_data,
            input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
            target_curve='VP',
            normalizer=normalizer,
            sequence_config={'length': 480, 'stride': 160}
        )
        
        logger.info(f"✓ VP prediction setup complete")
        logger.info(f"  Dataset: {len(dataset)} samples")
        logger.info(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # Test prediction with a sample
        sample_input, sample_target = dataset[0]
        sample_input = sample_input.to(device)
        sample_target = sample_target.to(device)
        model.eval()
        with torch.no_grad():
            prediction = model(sample_input.unsqueeze(0))
            
        # Denormalize for physical interpretation
        pred_physical = normalizer.denormalize_predictions(prediction, ['VP'])
        target_physical = normalizer.denormalize_predictions(sample_target.unsqueeze(0), ['VP'])
        
        logger.info(f"✓ Sample prediction complete")
        logger.info(f"  Input shape: {sample_input.shape}")
        logger.info(f"  Predicted VP range: {pred_physical.min():.1f} - {pred_physical.max():.1f} μs/ft")
        logger.info(f"  Target VP range: {target_physical.min():.1f} - {target_physical.max():.1f} μs/ft")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ VP prediction demo failed: {e}")
        return False

def safe_dataset_access(dataset, desired_idx=0):
    """Safely access dataset with bounds checking"""
    if len(dataset) == 0:
        raise ValueError("Dataset is empty")
    safe_idx = min(desired_idx, len(dataset) - 1)
    return dataset[safe_idx]

def ensure_device_consistency(model, *tensors):
    """Ensure model and tensors are on the same device"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    
    moved_tensors = []
    for tensor in tensors:
        if isinstance(tensor, torch.Tensor):
            moved_tensors.append(tensor.to(device))
        else:
            moved_tensors.append(tensor)
    
    return model, moved_tensors

def demo_density_prediction():
    """Demo 2: Density Prediction"""
    logger.info("\n" + "="*60)
    logger.info("🎯 DEMO 2: Density Prediction")
    logger.info("="*60)
    logger.info("Configuration: Density prediction from GR, CNL, AC, RLLD")
    logger.info("Expected: Physics constraints, different loss function")
    
    try:
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDataNormalizer,
            GeneralWellLogDataset, GeneralWellLogLoss,
            get_training_template
        )
        
        # Create synthetic well data
        well_data = create_realistic_well_data(2500)
        
        # Setup density prediction
        training_config = get_training_template('density_training')
        logger.info(f"Training config target: {training_config['target_curve']}")
        
        # Create model with different input/output configuration
        device = get_device()
        model = GeneralWellLogTransformer(
            input_channels=4,   # GR, CNL, AC, RLLD
            output_channels=1,  # DEN
            model_size='base'
        )
        model = model.to(device)
        
        normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'CNL', 'AC', 'RLLD'],
            output_curves=['DEN']
        )
        
        # Create dataset for density prediction
        dataset = GeneralWellLogDataset(
            data_dict=well_data,
            input_curves=['GR', 'CNL', 'AC', 'RLLD'],
            target_curve='DEN',
            normalizer=normalizer,
            sequence_config={'length': 400, 'stride': 100}
        )
        
        # Create density-specific loss function
        loss_fn = GeneralWellLogLoss(
            target_curve='DEN',
            loss_type='mse',
            constraint_weight=0.5,  # Lower constraint weight for density
            physics_constraints=True
        )
        
        logger.info(f"✓ Density prediction setup complete")
        logger.info(f"  Dataset: {len(dataset)} samples")
        logger.info(f"  Physics range: {loss_fn.physics_range}")
        
        # Test sample prediction with safe access
        sample_input, sample_target = safe_dataset_access(dataset, desired_idx=5)
        model, (sample_input, sample_target) = ensure_device_consistency(model, sample_input, sample_target)
        
        model.eval()
        with torch.no_grad():
            prediction = model(sample_input.unsqueeze(0))
            
        # Test loss computation
        loss_components = loss_fn(prediction, sample_target.unsqueeze(0))
        
        logger.info(f"✓ Sample density prediction complete")
        logger.info(f"  Loss components: {list(loss_components.keys())}")
        logger.info(f"  Base loss: {loss_components['base_loss']:.4f}")
        logger.info(f"  Constraint loss: {loss_components['constraint_loss']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Density prediction demo failed: {e}")
        return False

def demo_neutron_prediction():
    """Demo 3: Neutron Prediction with Robust Loss"""
    logger.info("\n" + "="*60)
    logger.info("🎯 DEMO 3: Neutron Porosity Prediction")
    logger.info("="*60)
    logger.info("Configuration: CNL prediction from GR, DEN, AC with robust loss")
    logger.info("Expected: Huber loss for outlier resistance")
    
    try:
        from vp_predictor import (
            GeneralWellLogDataset, GeneralDataNormalizer,
            CurveSpecificLossFactory, get_training_template
        )
        
        # Create well data with some outliers
        well_data = create_realistic_well_data(2000)
        
        # Add some neutron outliers (common in real data)
        outlier_indices = np.random.choice(len(well_data['CNL']), size=50, replace=False)
        well_data['CNL'][outlier_indices] += np.random.uniform(-10, 15, 50)
        well_data['CNL'] = np.clip(well_data['CNL'], 0, 60)
        
        logger.info(f"Added {len(outlier_indices)} neutron outliers for robust testing")
        
        # Setup neutron prediction
        training_config = get_training_template('neutron_training')
        logger.info(f"Loss type: {training_config.get('loss_config', {}).get('type', 'default')}")
        
        normalizer = GeneralDataNormalizer(
            input_curves=['GR', 'DEN', 'AC'],
            output_curves=['CNL']
        )
        
        dataset = GeneralWellLogDataset(
            data_dict=well_data,
            input_curves=['GR', 'DEN', 'AC'],
            target_curve='CNL', 
            normalizer=normalizer,
            sequence_config={'length': 320, 'stride': 80}
        )
        
        # Create neutron-specific loss (robust Huber loss)
        loss_fn = CurveSpecificLossFactory.create_loss('CNL', {
            'loss_type': 'huber',
            'robust_loss_delta': 1.5,
            'constraint_weight': 0.8
        })
        
        logger.info(f"✓ Neutron prediction setup complete")
        logger.info(f"  Dataset: {len(dataset)} samples")
        logger.info(f"  Loss info: {loss_fn.get_loss_info()}")
        
        # Test robustness with outliers
        device = get_device()
        sample_input, sample_target = safe_dataset_access(dataset, desired_idx=3)
        sample_input = sample_input.to(device)
        sample_target = sample_target.to(device)
        
        # Create predictions with and without outliers
        normal_prediction = torch.randn_like(sample_target, device=device) * 0.1 + sample_target
        outlier_prediction = normal_prediction.clone()
        outlier_prediction[0, :50] += 2.0  # Add outliers to prediction
        
        normal_loss = loss_fn(normal_prediction.unsqueeze(0), sample_target.unsqueeze(0))
        outlier_loss = loss_fn(outlier_prediction.unsqueeze(0), sample_target.unsqueeze(0))
        
        logger.info(f"✓ Robust loss testing complete")
        logger.info(f"  Normal prediction loss: {normal_loss['total_loss']:.4f}")
        logger.info(f"  Outlier prediction loss: {outlier_loss['total_loss']:.4f}")
        logger.info(f"  Loss ratio (should be <3x for robust loss): {outlier_loss['total_loss']/normal_loss['total_loss']:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Neutron prediction demo failed: {e}")
        return False

def demo_multi_curve_comparison():
    """Demo 4: Compare Different Curve Types"""
    logger.info("\n" + "="*60)
    logger.info("🎯 DEMO 4: Multi-Curve Comparison")
    logger.info("="*60)
    logger.info("Configuration: Compare training setups for 5 different curves")
    logger.info("Expected: Different configurations, loss functions, physics constraints")
    
    try:
        from vp_predictor import (
            get_training_template, CurveSpecificLossFactory,
            GeneralDataNormalizer, CURVE_CONFIGURATIONS
        )
        
        # Test all supported curves
        curves_to_test = ['AC', 'DEN', 'CNL', 'GR', 'RLLD']
        results = {}
        
        well_data = create_realistic_well_data(1500)
        
        for curve in curves_to_test:
            logger.info(f"\n--- Testing {curve} prediction setup ---")
            
            try:
                # Get curve configuration
                curve_config = CURVE_CONFIGURATIONS.get(curve, {})
                physics_range = curve_config.get('physics_range', (0, 1))
                
                # Create appropriate input curves (exclude target)
                available_curves = ['GR', 'CNL', 'DEN', 'RLLD', 'AC', 'VP']
                input_curves = [c for c in available_curves if c != curve and c in well_data][:4]
                
                # Create normalizer
                normalizer = GeneralDataNormalizer(
                    input_curves=input_curves,
                    output_curves=[curve]
                )
                
                # Create loss function
                loss_fn = CurveSpecificLossFactory.create_loss(curve)
                loss_info = loss_fn.get_loss_info()
                
                # Get recommended training template
                template_map = {
                    'AC': 'vp_training',
                    'DEN': 'density_training', 
                    'CNL': 'neutron_training',
                    'GR': 'gamma_ray_training',
                    'RLLD': 'resistivity_training'
                }
                
                template_name = template_map.get(curve, 'vp_training')
                training_config = get_training_template(template_name)
                
                results[curve] = {
                    'physics_range': physics_range,
                    'input_curves': input_curves,
                    'loss_type': loss_info['loss_type'],
                    'constraint_weight': loss_info['constraint_weight'],
                    'template_used': template_name,
                    'training_config': training_config
                }
                
                logger.info(f"  ✓ {curve}: {len(input_curves)} inputs → 1 output")
                logger.info(f"    Physics range: {physics_range}")
                logger.info(f"    Loss: {loss_info['loss_type']} (constraint: {loss_info['constraint_weight']})")
                
            except Exception as e:
                logger.error(f"  ❌ {curve} setup failed: {e}")
                results[curve] = {'error': str(e)}
        
        # Summary
        logger.info(f"\n✓ Multi-curve comparison complete")
        successful_curves = [c for c, r in results.items() if 'error' not in r]
        logger.info(f"  Successful configurations: {len(successful_curves)}/{len(curves_to_test)}")
        logger.info(f"  Supported curves: {', '.join(successful_curves)}")
        
        return len(successful_curves) >= 4  # At least 4 curves should work
        
    except Exception as e:
        logger.error(f"❌ Multi-curve comparison failed: {e}")
        return False

def demo_training_simulation():
    """Demo 5: Simulate Training Process"""
    logger.info("\n" + "="*60)
    logger.info("🎯 DEMO 5: Training Process Simulation")
    logger.info("="*60)
    logger.info("Configuration: Simulate complete training workflow")
    logger.info("Expected: Dataset, model, trainer, loss computation, validation")
    
    try:
        from vp_predictor import (
            GeneralWellLogTransformer, GeneralDataNormalizer,
            GeneralWellLogDataset, GeneralTrainingManager,
            get_model_template, get_training_template, get_data_template
        )
        
        # Create training data
        train_data = create_realistic_well_data(4000)
        val_data = create_realistic_well_data(1000)
        
        logger.info("✓ Training and validation data created")
        
        # Setup complete training pipeline for VP prediction
        model_config = get_model_template('vp_prediction')
        training_config = get_training_template('fast_vp_training')
        training_config['max_epochs'] = 2  # Quick simulation
        training_config['batch_size'] = 4
        data_config = get_data_template('short_sequence')
        
        # Create model
        device = get_device()
        model = GeneralWellLogTransformer.from_template('vp_prediction')
        model = model.to(device)
        
        # Create normalizer
        normalizer = GeneralDataNormalizer(
            model_config['input_curves'], 
            model_config['output_curves']
        )
        
        # Create datasets
        train_dataset = GeneralWellLogDataset(
            data_dict=train_data,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],
            normalizer=normalizer,
            sequence_config=data_config
        )
        
        val_dataset = GeneralWellLogDataset(
            data_dict=val_data,
            input_curves=model_config['input_curves'],
            target_curve=model_config['output_curves'][0],
            normalizer=normalizer,
            sequence_config=data_config
        )
        
        # Create trainer
        trainer = GeneralTrainingManager(
            model=model,
            train_dataset=train_dataset,
            val_dataset=val_dataset,
            training_config=training_config
        )
        
        # Get training info
        training_info = trainer.get_training_info()
        
        logger.info("✓ Complete training pipeline created")
        logger.info(f"  Model: {training_info['model_info']['model_class']}")
        logger.info(f"  Parameters: {training_info['model_info']['total_parameters']:,}")
        logger.info(f"  Training samples: {training_info['dataset_info']['train_samples']}")
        logger.info(f"  Validation samples: {training_info['dataset_info']['val_samples']}")
        logger.info(f"  Target curve: {training_info['target_curve']}")
        logger.info(f"  Device: {training_info.get('device_info', {}).get('device', 'cuda:0')}")
        
        # Simulate one training step
        train_loader = torch.utils.data.DataLoader(
            train_dataset, batch_size=training_config['batch_size'], shuffle=True
        )
        
        batch_inputs, batch_targets = next(iter(train_loader))
        batch_inputs = batch_inputs.to(device)
        batch_targets = batch_targets.to(device)
        logger.info(f"  Batch shapes: inputs {batch_inputs.shape}, targets {batch_targets.shape}")
        
        # Test forward pass with device consistency
        model, (batch_inputs, batch_targets) = ensure_device_consistency(model, batch_inputs, batch_targets)
        
        model.eval()
        with torch.no_grad():
            predictions = model(batch_inputs)
            
        # Test loss computation
        loss_components = trainer.loss_function(predictions, batch_targets)
        
        logger.info("✓ Training simulation complete")
        logger.info(f"  Forward pass successful: {predictions.shape}")
        logger.info(f"  Loss components: {list(loss_components.keys())}")
        logger.info(f"  Total loss: {loss_components['total_loss']:.4f}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Training simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all demonstration cases"""
    logger.info("🚀 General Well Log Transformer - Phase 2 Demonstration")
    logger.info("=" * 80)
    logger.info("This demo showcases the flexible prediction capabilities")
    logger.info("Ready to advance to Phase 3: API Development")
    logger.info("=" * 80)
    
    # Add current directory to Python path
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.insert(0, current_dir)
    
    # Show device information
    device = get_device()
    
    # Run all demos
    demos = [
        ("VP Prediction (Backward Compatible)", demo_vp_prediction),
        ("Density Prediction", demo_density_prediction),
        ("Neutron Prediction (Robust)", demo_neutron_prediction),
        ("Multi-Curve Comparison", demo_multi_curve_comparison),
        ("Training Simulation", demo_training_simulation)
    ]
    
    results = {}
    for demo_name, demo_func in demos:
        logger.info(f"\n{'='*20} Starting {demo_name} {'='*20}")
        try:
            results[demo_name] = demo_func()
            status = "✅ PASSED" if results[demo_name] else "❌ FAILED"
            logger.info(f"\n{status}: {demo_name}")
        except Exception as e:
            logger.error(f"\n❌ CRASHED: {demo_name} - {e}")
            results[demo_name] = False
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 DEMONSTRATION SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for demo_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{demo_name:<35} {status}")
    
    logger.info(f"\nOverall: {passed}/{total} demonstrations passed")
    
    if passed == total:
        logger.info("\n🎉 ALL DEMONSTRATIONS SUCCESSFUL!")
        logger.info("✅ Phase 2 Complete - Ready for Phase 3: API Development")
        logger.info("\nCapabilities Demonstrated:")
        logger.info("  ✅ Multi-curve prediction (VP, DEN, CNL, GR, RLLD)")
        logger.info("  ✅ Flexible input/output combinations")
        logger.info("  ✅ Curve-specific loss functions and physics constraints")
        logger.info("  ✅ Robust training infrastructure with GPU support")
        logger.info("  ✅ Configuration-driven approach with templates")
        logger.info("  ✅ Backward compatibility with VpTransformer")
        return True
    else:
        logger.warning(f"\n⚠️  {total - passed}/{total} demonstrations failed")
        logger.info("Some functionality may need refinement before Phase 3")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)