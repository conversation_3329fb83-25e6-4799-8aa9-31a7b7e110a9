# MWLT MultiWell Requirements
# Core dependencies for Multi-Well Log Transformer organized codebase

# Deep Learning Framework
torch>=1.9.0
torchvision>=0.10.0

# Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
scikit-learn>=1.0.0

# Data Visualization
matplotlib>=3.4.0
seaborn>=0.11.0

# Data Processing
pandas>=1.3.0
h5py>=3.3.0

# Well Log Data
lasio>=0.28

# Progress and Logging
tqdm>=4.62.0
loguru>=0.6.0

# Statistical Analysis
statsmodels>=0.13.0

# File Path Handling
pathlib2>=2.3.0

# Optional: Jupyter support for notebooks
jupyter>=1.0.0
ipython>=7.25.0

# Optional: Advanced visualization
plotly>=5.0.0
bokeh>=2.4.0

# Optional: GPU acceleration (if available)
# nvidia-ml-py>=7.352.0

# Development/Testing
pytest>=6.2.0
pytest-cov>=2.12.0
black>=21.7.0
flake8>=3.9.0
