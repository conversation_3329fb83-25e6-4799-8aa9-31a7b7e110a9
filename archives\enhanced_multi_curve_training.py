#!/usr/bin/env python3
"""
Enhanced Multi-Curve Training Script
Addresses Priority 1 & 2 improvements:
1. Fixed normalization infrastructure
2. Robust model training with cross-validation
3. Comprehensive validation metrics
4. Confidence interval estimation

Author: AI Assistant
Date: 2025-08-22
"""

import os
import sys
import json
import numpy as np
import torch
import torch.nn as nn
from pathlib import Path
from sklearn.model_selection import <PERSON><PERSON><PERSON>
from typing import Dict, List, Tuple, Optional
import argparse
import logging

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from vp_predictor import VpDataNormalizer
from vp_predictor.core.dataset import GeneralWellLogDataset, create_general_dataset
from vp_predictor.core.training import GeneralTrainingManager
from vp_predictor.core.loss_functions import GeneralWellLogLoss
from vp_predictor.vp_model_improved import MWLT_Vp_Base
from vp_predictor.utils import get_device

# Try to import training examples, fallback to synthetic data if not available
try:
    from examples.production_training_examples import load_training_data
    TRAINING_EXAMPLES_AVAILABLE = True
except ImportError:
    TRAINING_EXAMPLES_AVAILABLE = False
    def create_synthetic_training_data():
        """Create synthetic training data for demonstration"""
        n_samples = 50  # Increased from original small dataset
        seq_length = 640
        
        np.random.seed(42)  # For reproducible results
        
        # Create realistic synthetic well log data
        synthetic_data = {
            'GR': np.random.randn(n_samples, seq_length) * 30 + 80,
            'CNL': np.random.randn(n_samples, seq_length) * 10 + 20, 
            'DEN': np.random.randn(n_samples, seq_length) * 0.3 + 2.3,
            'AC': np.random.randn(n_samples, seq_length) * 30 + 200,
            'RLLD': np.random.exponential(10, (n_samples, seq_length))
        }
        
        # Add some geological relationships to make it more realistic
        for i in range(n_samples):
            # Porosity relationship: higher CNL -> lower DEN
            synthetic_data['DEN'][i] -= 0.01 * (synthetic_data['CNL'][i] - 20)
            
            # Velocity relationship: higher DEN -> lower AC
            synthetic_data['AC'][i] -= 2 * (synthetic_data['DEN'][i] - 2.3) * 30
            
            # Resistivity relationship: lower porosity -> higher RLLD
            porosity_proxy = synthetic_data['CNL'][i] / 30.0
            synthetic_data['RLLD'][i] *= (1.5 - porosity_proxy * 0.5)
        
        return synthetic_data

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enhanced_training.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnhancedTrainingPipeline:
    """Enhanced training pipeline with cross-validation and confidence metrics"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.device = get_device()
        self.normalizer = VpDataNormalizer()
        self.results_dir = Path("enhanced_training_outputs")
        self.results_dir.mkdir(exist_ok=True)
        
        logger.info(f"🚀 Enhanced Training Pipeline Initialized")
        logger.info(f"📱 Device: {self.device}")
        logger.info(f"📁 Results directory: {self.results_dir}")
    
    def load_and_validate_data(self) -> Dict[str, np.ndarray]:
        """Load data with comprehensive validation"""
        logger.info("📂 Loading and validating training data...")
        
        try:
            if TRAINING_EXAMPLES_AVAILABLE:
                data_dict = load_training_data()
                logger.info("   ✅ Loaded real training data")
            else:
                logger.info("   ⚠️  Real training data not available, using synthetic data")
                data_dict = create_synthetic_training_data()
                logger.info("   ✅ Generated synthetic training data")
                
            logger.info(f"✅ Loaded data for curves: {list(data_dict.keys())}")
            
            # Validate data quality
            validation_report = {}
            for curve_name, curve_data in data_dict.items():
                # Basic statistics
                valid_samples = np.sum(np.isfinite(curve_data))
                total_samples = curve_data.size
                completeness = valid_samples / total_samples
                
                validation_report[curve_name] = {
                    'total_samples': total_samples,
                    'valid_samples': valid_samples,
                    'completeness': completeness,
                    'range': [np.nanmin(curve_data), np.nanmax(curve_data)],
                    'std': np.nanstd(curve_data)
                }
                
                logger.info(f"   {curve_name}: {valid_samples}/{total_samples} valid ({completeness:.1%})")
                
                if completeness < 0.7:
                    logger.warning(f"   ⚠️  Low data completeness for {curve_name}: {completeness:.1%}")
            
            # Save validation report
            with open(self.results_dir / "data_validation_report.json", 'w') as f:
                json.dump(validation_report, f, indent=2, default=str)
            
            return data_dict
            
        except Exception as e:
            logger.error(f"❌ Failed to load data: {e}")
            raise
    
    def create_cross_validation_folds(self, data_dict: Dict, n_folds: int = 5) -> List[Tuple]:
        """Create cross-validation folds ensuring balanced data distribution"""
        logger.info(f"🔄 Creating {n_folds}-fold cross-validation splits...")
        
        # Get the total number of samples (assuming all curves have same length)
        first_curve = next(iter(data_dict.values()))
        n_samples = len(first_curve)
        
        kfold = KFold(n_splits=n_folds, shuffle=True, random_state=42)
        folds = []
        
        for fold_idx, (train_idx, val_idx) in enumerate(kfold.split(range(n_samples))):
            train_data = {}
            val_data = {}
            
            for curve_name, curve_data in data_dict.items():
                train_data[curve_name] = curve_data[train_idx]
                val_data[curve_name] = curve_data[val_idx]
            
            folds.append((train_data, val_data, train_idx, val_idx))
            logger.info(f"   Fold {fold_idx + 1}: {len(train_idx)} train, {len(val_idx)} val samples")
        
        return folds
    
    def train_single_model(
        self, 
        target_curve: str,
        train_data: Dict,
        val_data: Dict,
        fold_idx: int
    ) -> Dict:
        """Train a single model for one target curve"""
        logger.info(f"🎯 Training model for {target_curve} (Fold {fold_idx + 1})")
        
        try:
            # Define input curves (all except target)
            all_curves = ['GR', 'CNL', 'DEN', 'AC', 'RLLD']
            input_curves = [curve for curve in all_curves if curve != target_curve and curve in train_data]
            
            if len(input_curves) < 2:
                logger.warning(f"   ⚠️  Insufficient input curves for {target_curve}: {input_curves}")
                return None
            
            logger.info(f"   📊 Input curves: {input_curves}")
            
            # Create datasets
            train_dataset = create_general_dataset(
                data_dict=train_data,
                input_curves=input_curves,
                target_curve=target_curve,
                normalizer=self.normalizer,
                sequence_config={'effect_seqlen': 640, 'total_seqlen': 640},
                missing_data_strategy='interpolation',
                quality_threshold=0.8,
                transform=True
            )
            
            val_dataset = create_general_dataset(
                data_dict=val_data,
                input_curves=input_curves,
                target_curve=target_curve,
                normalizer=self.normalizer,
                sequence_config={'effect_seqlen': 640, 'total_seqlen': 640},
                missing_data_strategy='interpolation',
                quality_threshold=0.8,
                transform=False
            )
            
            if len(train_dataset) == 0 or len(val_dataset) == 0:
                logger.error(f"   ❌ Empty dataset for {target_curve}")
                return None
            
            logger.info(f"   📈 Training samples: {len(train_dataset)}")
            logger.info(f"   📊 Validation samples: {len(val_dataset)}")
            
            # Create model
            model = MWLT_Vp_Base(
                in_channels=len(input_curves),
                out_channels=1,
                feature_num=64
            )
            
            # Enhanced training configuration
            training_config = {
                'learning_rate': self.config.get('learning_rate', 1e-4),
                'weight_decay': self.config.get('weight_decay', 1e-5),
                'batch_size': self.config.get('batch_size', 16),
                'max_epochs': self.config.get('max_epochs', 200),
                'patience': self.config.get('patience', 50),
                'optimizer': 'adam',
                'scheduler': 'plateau',
                'scheduler_params': {
                    'patience': 20,
                    'factor': 0.6,
                    'min_lr': 1e-7
                },
                'loss_config': {
                    'type': 'curve_specific',
                    'custom_params': {
                        'constraint_weight': 0.5,
                        'physics_constraints': True
                    }
                },
                'save_best_only': True,
                'validation_frequency': 1,
                'gradient_clipping': 1.0,
                'mixed_precision': False,
                'save_dir': str(self.results_dir / f"{target_curve}_fold_{fold_idx}")
            }
            
            # Create trainer
            trainer = GeneralTrainingManager(
                model=model,
                train_dataset=train_dataset,
                val_dataset=val_dataset,
                training_config=training_config,
                device=self.device
            )
            
            # Train model
            logger.info(f"   🏋️  Starting training...")
            training_results = trainer.train_model()
            
            logger.info(f"   ✅ Training completed for {target_curve} (Fold {fold_idx + 1})")
            logger.info(f"   📈 Best validation R²: {training_results.get('best_r2', 'N/A'):.4f}")
            logger.info(f"   📉 Best validation loss: {training_results.get('best_val_loss', 'N/A'):.4f}")
            
            return {
                'target_curve': target_curve,
                'fold': fold_idx,
                'input_curves': input_curves,
                'training_results': training_results,
                'model_path': Path(training_config['save_dir']) / "best_model.pth",
                'config_path': Path(training_config['save_dir']) / "training_config.json"
            }
            
        except Exception as e:
            logger.error(f"   ❌ Training failed for {target_curve} (Fold {fold_idx + 1}): {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def calculate_confidence_metrics(self, fold_results: List[Dict], target_curve: str) -> Dict:
        """Calculate confidence intervals and cross-validation metrics"""
        logger.info(f"📊 Calculating confidence metrics for {target_curve}...")
        
        # Extract metrics from all folds
        r2_scores = []
        val_losses = []
        train_losses = []
        
        for result in fold_results:
            if result and result['training_results']:
                tr = result['training_results']
                if 'best_r2' in tr:
                    r2_scores.append(tr['best_r2'])
                if 'best_val_loss' in tr:
                    val_losses.append(tr['best_val_loss'])
                if 'final_train_loss' in tr:
                    train_losses.append(tr['final_train_loss'])
        
        if not r2_scores:
            logger.warning(f"   ⚠️  No valid R² scores for {target_curve}")
            return None
        
        # Calculate statistics
        confidence_metrics = {
            'r2_mean': np.mean(r2_scores),
            'r2_std': np.std(r2_scores),
            'r2_ci_lower': np.mean(r2_scores) - 1.96 * np.std(r2_scores) / np.sqrt(len(r2_scores)),
            'r2_ci_upper': np.mean(r2_scores) + 1.96 * np.std(r2_scores) / np.sqrt(len(r2_scores)),
            'val_loss_mean': np.mean(val_losses) if val_losses else None,
            'val_loss_std': np.std(val_losses) if val_losses else None,
            'n_folds': len(r2_scores),
            'successful_folds': len([r for r in fold_results if r is not None])
        }
        
        logger.info(f"   🎯 R² = {confidence_metrics['r2_mean']:.4f} ± {confidence_metrics['r2_std']:.4f}")
        logger.info(f"   📊 95% CI: [{confidence_metrics['r2_ci_lower']:.4f}, {confidence_metrics['r2_ci_upper']:.4f}]")
        
        return confidence_metrics
    
    def run_enhanced_training(self):
        """Run the complete enhanced training pipeline"""
        logger.info("🚀 Starting Enhanced Multi-Curve Training Pipeline")
        
        # Load and validate data
        data_dict = self.load_and_validate_data()
        
        # Create cross-validation folds
        cv_folds = self.create_cross_validation_folds(data_dict, n_folds=5)
        
        # Target curves to train
        target_curves = self.config.get('target_curves', ['AC', 'DEN', 'CNL', 'GR', 'RLLD'])
        available_curves = [curve for curve in target_curves if curve in data_dict]
        
        logger.info(f"🎯 Training targets: {available_curves}")
        
        # Results storage
        all_results = {}
        
        # Train each target curve with cross-validation
        for target_curve in available_curves:
            logger.info(f"\n{'='*80}")
            logger.info(f"🎯 TRAINING {target_curve} PREDICTION MODELS")
            logger.info(f"{'='*80}")
            
            fold_results = []
            
            # Train on each fold
            for fold_idx, (train_data, val_data, train_idx, val_idx) in enumerate(cv_folds):
                result = self.train_single_model(target_curve, train_data, val_data, fold_idx)
                fold_results.append(result)
            
            # Calculate confidence metrics
            confidence_metrics = self.calculate_confidence_metrics(fold_results, target_curve)
            
            all_results[target_curve] = {
                'fold_results': fold_results,
                'confidence_metrics': confidence_metrics
            }
        
        # Save comprehensive results
        self._save_comprehensive_results(all_results)
        
        # Generate summary report
        self._generate_summary_report(all_results)
        
        logger.info(f"\n🎉 Enhanced Training Pipeline Complete!")
        logger.info(f"📁 Results saved in: {self.results_dir}")
    
    def _save_comprehensive_results(self, all_results: Dict):
        """Save comprehensive training results"""
        logger.info("💾 Saving comprehensive results...")
        
        # Save detailed results
        results_file = self.results_dir / "enhanced_training_results.json"
        with open(results_file, 'w') as f:
            # Convert Path objects to strings for JSON serialization
            serializable_results = {}
            for curve, data in all_results.items():
                serializable_results[curve] = {
                    'confidence_metrics': data['confidence_metrics'],
                    'fold_results': []
                }
                
                for fold_result in data['fold_results']:
                    if fold_result:
                        serialized_fold = fold_result.copy()
                        if 'model_path' in serialized_fold:
                            serialized_fold['model_path'] = str(serialized_fold['model_path'])
                        if 'config_path' in serialized_fold:
                            serialized_fold['config_path'] = str(serialized_fold['config_path'])
                        serializable_results[curve]['fold_results'].append(serialized_fold)
            
            json.dump(serializable_results, f, indent=2, default=str)
        
        logger.info(f"   ✅ Results saved to: {results_file}")
    
    def _generate_summary_report(self, all_results: Dict):
        """Generate a comprehensive summary report"""
        logger.info("📋 Generating summary report...")
        
        report_file = self.results_dir / "training_summary_report.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Enhanced Multi-Curve Training Summary Report\n\n")
            f.write(f"**Generated**: {__import__('datetime').datetime.now()}\n")
            f.write(f"**Device**: {self.device}\n")
            f.write(f"**Cross-validation folds**: 5\n\n")
            
            f.write("## Model Performance Summary\n\n")
            f.write("| Target Curve | R² (Mean ± Std) | 95% Confidence Interval | Validation Loss | Success Rate |\n")
            f.write("|-------------|-----------------|------------------------|-----------------|-------------|\n")
            
            for curve, data in all_results.items():
                if data['confidence_metrics']:
                    cm = data['confidence_metrics']
                    f.write(f"| {curve} | {cm['r2_mean']:.4f} ± {cm['r2_std']:.4f} | "
                           f"[{cm['r2_ci_lower']:.4f}, {cm['r2_ci_upper']:.4f}] | "
                           f"{cm['val_loss_mean']:.4f} ± {cm['val_loss_std']:.4f} | "
                           f"{cm['successful_folds']}/{cm['n_folds']} |\n")
            
            f.write("\n## Performance Analysis\n\n")
            
            # Performance classification
            for curve, data in all_results.items():
                if data['confidence_metrics']:
                    cm = data['confidence_metrics']
                    r2_mean = cm['r2_mean']
                    
                    if r2_mean > 0.8:
                        status = "🟢 Excellent"
                    elif r2_mean > 0.6:
                        status = "🟡 Good"
                    elif r2_mean > 0.3:
                        status = "🟠 Moderate"
                    else:
                        status = "🔴 Poor"
                    
                    f.write(f"### {curve} Prediction: {status}\n")
                    f.write(f"- **R² Score**: {r2_mean:.4f} ± {r2_std:.4f}\n")
                    f.write(f"- **Confidence Interval**: [{cm['r2_ci_lower']:.4f}, {cm['r2_ci_upper']:.4f}]\n")
                    f.write(f"- **Model Consistency**: {cm['successful_folds']}/{cm['n_folds']} successful folds\n\n")
            
            f.write("## Next Steps Recommendations\n\n")
            
            # Generate recommendations based on performance
            poor_performers = [curve for curve, data in all_results.items() 
                             if data['confidence_metrics'] and data['confidence_metrics']['r2_mean'] < 0.3]
            
            if poor_performers:
                f.write(f"### Priority Actions for Poor Performers: {', '.join(poor_performers)}\n")
                f.write("1. **Increase Training Data**: Current dataset may be insufficient\n")
                f.write("2. **Feature Engineering**: Consider additional input curves or transformations\n")
                f.write("3. **Architecture Optimization**: Experiment with different model architectures\n")
                f.write("4. **Hyperparameter Tuning**: Systematic optimization of training parameters\n\n")
            
            good_performers = [curve for curve, data in all_results.items() 
                             if data['confidence_metrics'] and data['confidence_metrics']['r2_mean'] > 0.6]
            
            if good_performers:
                f.write(f"### Production Ready Models: {', '.join(good_performers)}\n")
                f.write("1. **Deploy to Production**: Models show good performance and consistency\n")
                f.write("2. **Monitor Performance**: Implement real-time monitoring\n")
                f.write("3. **Collect Feedback**: Gather user feedback for continuous improvement\n\n")
        
        logger.info(f"   ✅ Summary report saved to: {report_file}")


def main():
    """Main execution function"""
    parser = argparse.ArgumentParser(description="Enhanced Multi-Curve Training Pipeline")
    parser.add_argument('--config', type=str, help='Training configuration file (JSON)')
    parser.add_argument('--target-curves', nargs='+', 
                       choices=['AC', 'DEN', 'CNL', 'GR', 'RLLD'],
                       default=['AC', 'DEN', 'CNL', 'GR', 'RLLD'],
                       help='Target curves to train')
    parser.add_argument('--learning-rate', type=float, default=1e-4,
                       help='Learning rate for training')
    parser.add_argument('--max-epochs', type=int, default=200,
                       help='Maximum number of training epochs')
    parser.add_argument('--batch-size', type=int, default=16,
                       help='Training batch size')
    
    args = parser.parse_args()
    
    # Load configuration
    if args.config and os.path.exists(args.config):
        with open(args.config, 'r') as f:
            config = json.load(f)
    else:
        config = {
            'target_curves': args.target_curves,
            'learning_rate': args.learning_rate,
            'max_epochs': args.max_epochs,
            'batch_size': args.batch_size,
            'weight_decay': 1e-5,
            'patience': 50
        }
    
    # Run enhanced training pipeline
    pipeline = EnhancedTrainingPipeline(config)
    pipeline.run_enhanced_training()


if __name__ == "__main__":
    main()
