"""
Create sample test data with the correct format for the transformer model
"""
import h5py
import numpy as np
import os

def create_sample_data():
    """Create sample well log data files with the correct format"""
    
    # Create the val directory if it doesn't exist
    val_dir = "../data_normal/val"
    if os.path.exists(val_dir):
        # Remove existing files
        for file in os.listdir(val_dir):
            if file.endswith('.hdf5'):
                os.remove(os.path.join(val_dir, file))
    
    # Parameters
    total_seqlen = 720
    num_samples = 5  # Create 5 sample files for testing
    
    # Create sample data files
    for i in range(1, num_samples + 1):
        filename = f"case_{i}.hdf5"
        filepath = os.path.join(val_dir, filename)
        
        # Generate synthetic well log data
        # These are typical ranges for well log measurements
        depth = np.linspace(0, 3600, total_seqlen)  # Depth in feet
        
        # GR (Gamma Ray): typically 0-200 API units
        GR = 50 + 30 * np.sin(depth / 100) + 10 * np.random.normal(0, 1, total_seqlen)
        GR = np.clip(GR, 0, 200)
        
        # AC (Acoustic/Sonic): typically 40-140 microseconds/foot
        AC = 80 + 20 * np.sin(depth / 150 + 1) + 5 * np.random.normal(0, 1, total_seqlen)
        AC = np.clip(AC, 40, 140)
        
        # CNL (Neutron): typically 0-60 porosity units
        CNL = 20 + 15 * np.sin(depth / 200 + 2) + 3 * np.random.normal(0, 1, total_seqlen)
        CNL = np.clip(CNL, 0, 60)
        
        # RLLD (Resistivity): typically 0.1-1000 ohm-m (log scale)
        RLLD = 10 ** (1 + 1.5 * np.sin(depth / 180 + 3) + 0.2 * np.random.normal(0, 1, total_seqlen))
        RLLD = np.clip(RLLD, 0.1, 1000)
        
        # DEN (Density): typically 1.5-3.0 g/cm3
        # Make it somewhat correlated with other logs for realism
        DEN = 2.2 + 0.3 * np.sin(depth / 120 + 4) + 0.1 * (GR - 50) / 50 + 0.05 * np.random.normal(0, 1, total_seqlen)
        DEN = np.clip(DEN, 1.5, 3.0)
        
        # Create HDF5 file
        with h5py.File(filepath, 'w') as f:
            f.create_dataset('GR', data=GR.astype(np.float32))
            f.create_dataset('AC', data=AC.astype(np.float32))
            f.create_dataset('CNL', data=CNL.astype(np.float32))
            f.create_dataset('RLLD', data=RLLD.astype(np.float32))
            f.create_dataset('DEN', data=DEN.astype(np.float32))
        
        print(f"Created {filepath}")
        print(f"  GR shape: {GR.shape}, range: [{GR.min():.2f}, {GR.max():.2f}]")
        print(f"  AC shape: {AC.shape}, range: [{AC.min():.2f}, {AC.max():.2f}]")
        print(f"  CNL shape: {CNL.shape}, range: [{CNL.min():.2f}, {CNL.max():.2f}]")
        print(f"  RLLD shape: {RLLD.shape}, range: [{RLLD.min():.2f}, {RLLD.max():.2f}]")
        print(f"  DEN shape: {DEN.shape}, range: [{DEN.min():.2f}, {DEN.max():.2f}]")
        print()

if __name__ == "__main__":
    create_sample_data()
    print("Sample data creation completed!")
