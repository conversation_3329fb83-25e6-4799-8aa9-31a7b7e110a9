"""
General Well Log Predictor API

Provides the new general-purpose prediction API that supports arbitrary
input/output curve combinations and advanced features.
"""

import torch
import numpy as np
from typing import Dict, List, Optional, Union, Tuple
from ..core import GeneralWellLogTransformer, GeneralDataNormalizer
from ..configs import get_model_template, validate_full_config
from ..utils import get_device, load_checkpoint


class GeneralWellLogPredictor:
    """
    General-purpose well log prediction API
    
    Supports arbitrary input/output curve combinations with configurable
    model architectures and prediction strategies.
    """
    
    def __init__(self,
                 model_path: str = None,
                 prediction_config: Dict = None,
                 template_name: str = None,
                 device_id: int = 0,
                 model: torch.nn.Module = None):
        """
        Initialize GeneralWellLogPredictor
        
        Args:
            model_path: Path to trained model checkpoint
            prediction_config: Configuration dict specifying input/output curves
            template_name: Use predefined template (alternative to prediction_config)
            device_id: GPU device ID (0 for first GPU, -1 for CPU)
            model: Pre-loaded model (optional)
        """
        self.device = get_device() if device_id >= 0 else torch.device('cpu')
        self.model_path = model_path
        
        # Load configuration
        if template_name:
            self.config = get_model_template(template_name)
        elif prediction_config:
            self.config = prediction_config
        else:
            # Default to Vp prediction for backward compatibility
            self.config = get_model_template('vp_prediction')
        
        # Validate configuration
        is_valid, messages = validate_full_config(self.config)
        if not is_valid:
            print(f"Configuration warnings: {messages}")
        
        # Extract curve information
        self.input_curves = self.config['input_curves']
        self.output_curves = self.config['output_curves']
        
        # Initialize normalizer
        self.normalizer = GeneralDataNormalizer(
            input_curves=self.input_curves,
            output_curves=self.output_curves
        )
        
        # Load or create model
        if model is not None:
            self.model = model
        elif model_path is not None:
            self.model = self._load_model(model_path)
        else:
            # Create new model from configuration
            self.model = self._create_model()
        
        self.model.to(self.device)
        self.model.eval()
    
    def _create_model(self) -> GeneralWellLogTransformer:
        """Create model from configuration"""
        return GeneralWellLogTransformer.from_config(self.config)
    
    def _load_model(self, model_path: str) -> GeneralWellLogTransformer:
        """Load model from checkpoint"""
        model = self._create_model()
        checkpoint = load_checkpoint(model_path, self.device)
        
        try:
            model.load_state_dict(checkpoint['model_state_dict'])
        except KeyError:
            # Try alternative checkpoint format
            model.load_state_dict(checkpoint)
        
        return model
    
    def predict(self, 
                input_data: Union[Dict[str, Union[np.ndarray, torch.Tensor]], str],
                format: str = 'curves',
                return_confidence: bool = False) -> Dict[str, Union[np.ndarray, torch.Tensor]]:
        """
        General prediction method
        
        Args:
            input_data: Input data (dict of curves or file path)
            format: Data format ('curves', 'file')
            return_confidence: Whether to return prediction confidence
            
        Returns:
            dict: Predicted curves
        """
        if format == 'file':
            return self.predict_file(input_data)
        elif format == 'curves':
            return self.predict_curves(input_data)
        else:
            raise ValueError(f"Unknown format: {format}")
    
    def predict_curves(self, 
                      curves_data: Dict[str, Union[np.ndarray, torch.Tensor]]) -> Dict[str, np.ndarray]:
        """
        Predict from curve data dictionary
        
        Args:
            curves_data: Dictionary mapping curve names to data arrays
            
        Returns:
            dict: Dictionary mapping output curve names to predictions
        """
        with torch.no_grad():
            # Convert to torch tensors if needed
            torch_curves = {}
            for curve_name, data in curves_data.items():
                if isinstance(data, np.ndarray):
                    torch_curves[curve_name] = torch.from_numpy(data).float()
                else:
                    torch_curves[curve_name] = data.float()
            
            # Validate input curves
            missing_curves = []
            for required_curve in self.input_curves:
                if required_curve not in torch_curves:
                    missing_curves.append(required_curve)
            
            if missing_curves:
                raise ValueError(f"Missing required input curves: {missing_curves}")
            
            # Normalize inputs
            normalized_inputs = self.normalizer.normalize_inputs(torch_curves)
            
            # Prepare input tensor [B, C, L] where B=1
            input_list = []
            seq_len = None
            
            for curve in self.input_curves:
                curve_data = normalized_inputs[curve]
                if seq_len is None:
                    seq_len = curve_data.shape[-1]
                input_list.append(curve_data)
            
            # Stack and add batch dimension
            input_tensor = torch.stack(input_list, dim=0).unsqueeze(0)  # [1, C, L]
            input_tensor = input_tensor.to(self.device)
            
            # Forward pass
            predictions = self.model(input_tensor)
            
            # Handle different output formats
            if isinstance(predictions, dict):
                # Multi-output case
                denormalized = self.normalizer.denormalize_predictions(predictions)
            else:
                # Single output case
                denormalized = self.normalizer.denormalize_predictions(
                    predictions, self.output_curves
                )
            
            # Convert to numpy arrays and remove batch dimension
            result = {}
            if isinstance(denormalized, dict):
                for curve_name, pred_tensor in denormalized.items():
                    result[curve_name] = pred_tensor.squeeze(0).cpu().numpy()
            else:
                # Single output
                curve_name = self.output_curves[0]
                result[curve_name] = denormalized.squeeze(0).cpu().numpy()
            
            return result
    
    def predict_file(self, file_path: str) -> Dict[str, np.ndarray]:
        """
        Predict from data file
        
        Args:
            file_path: Path to HDF5 or LAS file
            
        Returns:
            dict: Predicted curves
        """
        # Load file data
        from ..las_processor import LASProcessor
        processor = LASProcessor()
        
        try:
            curves = processor.process_hdf5_file(file_path)
        except:
            # Try as LAS file
            curves = processor.process_las_file(file_path)
        
        # Convert to appropriate format
        curves_dict = {}
        for curve_name in self.input_curves:
            if curve_name in curves:
                curves_dict[curve_name] = curves[curve_name]
            else:
                # Handle missing curves
                seq_len = len(curves[list(curves.keys())[0]])
                if curve_name == 'GR':
                    curves_dict[curve_name] = np.full(seq_len, 75.0)  # Typical GR value
                elif curve_name == 'CNL':
                    curves_dict[curve_name] = np.full(seq_len, 20.0)  # Typical neutron
                elif curve_name == 'DEN':
                    curves_dict[curve_name] = np.full(seq_len, 2.5)   # Typical density
                elif curve_name == 'RLLD':
                    curves_dict[curve_name] = np.full(seq_len, 10.0)  # Typical resistivity
                else:
                    curves_dict[curve_name] = np.zeros(seq_len)
                
                print(f"Warning: Missing curve {curve_name}, using default values")
        
        return self.predict_curves(curves_dict)
    
    def predict_missing_sections(self, 
                                available_curves: Dict[str, np.ndarray],
                                target_curves: List[str],
                                missing_indices: Optional[List[int]] = None) -> Dict[str, np.ndarray]:
        """
        Fill missing log sections using available curves
        
        Args:
            available_curves: Dictionary of available curve data
            target_curves: List of curves to predict/fill
            missing_indices: Indices where data is missing (optional)
            
        Returns:
            dict: Predicted curves for missing sections
        """
        # Validate that we can predict target curves
        for curve in target_curves:
            if curve not in self.output_curves:
                raise ValueError(f"Cannot predict curve {curve}. "
                               f"Model outputs: {self.output_curves}")
        
        # Use available curves as inputs
        predictions = self.predict_curves(available_curves)
        
        # Extract only requested target curves
        result = {curve: predictions[curve] for curve in target_curves 
                 if curve in predictions}
        
        # Apply missing indices filter if specified
        if missing_indices:
            for curve_name, pred_data in result.items():
                filtered_data = np.full_like(pred_data, np.nan)
                filtered_data[missing_indices] = pred_data[missing_indices]
                result[curve_name] = filtered_data
        
        return result
    
    def batch_predict(self, 
                     data_list: List[Union[Dict, str]],
                     parallel: bool = True) -> List[Dict[str, np.ndarray]]:
        """
        Batch prediction for multiple samples
        
        Args:
            data_list: List of input data (curves or file paths)
            parallel: Whether to use parallel processing (future feature)
            
        Returns:
            list: List of prediction results
        """
        results = []
        for data in data_list:
            if isinstance(data, str):
                # File path
                result = self.predict_file(data)
            else:
                # Curves dictionary
                result = self.predict_curves(data)
            results.append(result)
        
        return results
    
    def evaluate_model(self, 
                      test_data: Dict[str, Dict[str, np.ndarray]], 
                      metrics: List[str] = ['rmse', 'r2', 'mae']) -> Dict[str, Dict]:
        """
        Evaluate model performance on test data
        
        Args:
            test_data: Dictionary with 'inputs' and 'targets' keys
            metrics: List of metrics to compute
            
        Returns:
            dict: Evaluation metrics for each output curve
        """
        # Get predictions
        predictions = self.predict_curves(test_data['inputs'])
        
        # Compute metrics for each output curve
        results = {}
        
        for curve_name in self.output_curves:
            if curve_name in predictions and curve_name in test_data['targets']:
                pred = predictions[curve_name]
                target = test_data['targets'][curve_name]
                
                curve_metrics = {}
                
                for metric in metrics:
                    if metric == 'rmse':
                        curve_metrics['rmse'] = float(np.sqrt(np.mean((pred - target)**2)))
                    elif metric == 'r2':
                        ss_res = np.sum((target - pred)**2)
                        ss_tot = np.sum((target - np.mean(target))**2)
                        curve_metrics['r2'] = float(1 - (ss_res / ss_tot))
                    elif metric == 'mae':
                        curve_metrics['mae'] = float(np.mean(np.abs(pred - target)))
                    elif metric == 'mape':
                        curve_metrics['mape'] = float(np.mean(np.abs((target - pred) / target)) * 100)
                
                results[curve_name] = curve_metrics
        
        return results
    
    def get_supported_curves(self) -> Dict[str, List[str]]:
        """
        Get supported input and output curves
        
        Returns:
            dict: Lists of supported input and output curves
        """
        return {
            'input_curves': self.input_curves.copy(),
            'output_curves': self.output_curves.copy(),
            'all_supported': list(set(self.input_curves + self.output_curves))
        }
    
    def get_model_info(self) -> Dict:
        """
        Get comprehensive model information
        
        Returns:
            dict: Model configuration and status
        """
        info = {
            'config': self.config,
            'input_curves': self.input_curves,
            'output_curves': self.output_curves,
            'device': str(self.device),
            'model_path': self.model_path,
        }
        
        if hasattr(self.model, 'get_model_info'):
            info.update(self.model.get_model_info())
        
        return info
    
    def update_config(self, new_config: Dict):
        """
        Update prediction configuration (requires model reload)
        
        Args:
            new_config: New configuration dictionary
        """
        # Validate new configuration
        is_valid, messages = validate_full_config(new_config)
        if not is_valid:
            raise ValueError(f"Invalid configuration: {messages}")
        
        self.config = new_config
        self.input_curves = new_config['input_curves']
        self.output_curves = new_config['output_curves']
        
        # Update normalizer
        self.normalizer = GeneralDataNormalizer(
            input_curves=self.input_curves,
            output_curves=self.output_curves
        )
        
        # Recreate model (will need retraining)
        self.model = self._create_model()
        self.model.to(self.device)
        self.model.eval()
        
        print("Configuration updated. Model will need retraining.")


# Factory functions
def create_general_predictor(template_name: str = 'vp_prediction', **kwargs) -> GeneralWellLogPredictor:
    """
    Create GeneralWellLogPredictor from template
    
    Args:
        template_name: Template to use
        **kwargs: Additional arguments
        
    Returns:
        GeneralWellLogPredictor: Configured predictor
    """
    return GeneralWellLogPredictor(template_name=template_name, **kwargs)

def create_multi_curve_predictor(input_curves: List[str], 
                                output_curves: List[str], 
                                **kwargs) -> GeneralWellLogPredictor:
    """
    Create predictor for specific curve combination
    
    Args:
        input_curves: List of input curve names
        output_curves: List of output curve names
        **kwargs: Additional arguments
        
    Returns:
        GeneralWellLogPredictor: Configured predictor
    """
    from ..configs.models import create_custom_template
    
    config = create_custom_template(
        name='custom_prediction',
        input_curves=input_curves,
        output_curves=output_curves
    )
    
    return GeneralWellLogPredictor(prediction_config=config, **kwargs)