# VpTransformer Integration Package Dependencies

# Core Dependencies (Required)
torch>=1.8.0                 # PyTorch deep learning framework
numpy>=1.19.0                # Numerical computing
h5py>=3.1.0                  # HDF5 file handling for well log data
scikit-learn>=0.24.0         # Data preprocessing and metrics

# Optional Dependencies for Enhanced Features
matplotlib>=3.3.0            # Plotting and visualization
pandas>=1.2.0                # Data analysis and manipulation
lasio>=0.30                  # LAS file reading (full LAS support)
thop>=0.0.31                 # Model profiling (FLOPs and parameters counting)

# Additional Utilities (Optional)
tqdm>=4.50.0                 # Progress bars for training/processing
seaborn>=0.11.0              # Enhanced visualization
jupyter>=1.0.0               # Jupyter notebook support for analysis

# For development and testing (Optional)
pytest>=6.0.0                # Testing framework
pytest-cov>=2.10.0           # Coverage reporting
black>=21.0.0                # Code formatting
flake8>=3.8.0                # Code linting

# Installation Commands:
#
# Minimal installation (core functionality only):
# pip install torch numpy h5py scikit-learn
#
# Full installation with enhanced features:
# pip install -r requirements.txt
#
# For CUDA support (if using GPU):
# pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu118
#
# Note: Adjust CUDA version (cu118) based on your system's CUDA installation