  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,351 - INFO - 📊 Calculating confidence metrics for DEN...
2025-08-24 00:27:00,351 - WARNING -    ⚠️  No valid R² scores for DEN
2025-08-24 00:27:00,351 - INFO -
================================================================================
2025-08-24 00:27:00,351 - INFO - 🎯 TRAINING CNL PREDICTION MODELS
2025-08-24 00:27:00,352 - INFO - ================================================================================
2025-08-24 00:27:00,352 - INFO - 🎯 Training model for CNL (Fold 1)
2025-08-24 00:27:00,352 - INFO -    📊 Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,352 - INFO -    🎯 Using curve-specific dataset for CNL prediction        
2025-08-24 00:27:00,368 - INFO -    📊 Created 45 sequences for CNL prediction
2025-08-24 00:27:00,373 - INFO -    📊 Created 10 sequences for CNL prediction
2025-08-24 00:27:00,373 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,373 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,385 - INFO -    🎯 Using curve-specific training configuration for CNL    
2025-08-24 00:27:00,386 - INFO -    🎯 Starting curve-specific training for CNL
2025-08-24 00:27:00,387 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,387 - INFO -    ⚙️  LR: 0.00012, Weight Decay: 1e-05, Loss: CNL-specific  
2025-08-24 00:27:00,390 - ERROR -    ❌ Training failed for CNL (Fold 1): Input type (torch.cud
a.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,394 - INFO - 🎯 Training model for CNL (Fold 2)
2025-08-24 00:27:00,395 - INFO -    📊 Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,395 - INFO -    🎯 Using curve-specific dataset for CNL prediction        
2025-08-24 00:27:00,411 - INFO -    📊 Created 45 sequences for CNL prediction
2025-08-24 00:27:00,415 - INFO -    📊 Created 10 sequences for CNL prediction
2025-08-24 00:27:00,415 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,415 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,428 - INFO -    🎯 Using curve-specific training configuration for CNL
2025-08-24 00:27:00,428 - INFO -    🎯 Starting curve-specific training for CNL
2025-08-24 00:27:00,429 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,430 - INFO -    ⚙️  LR: 0.00012, Weight Decay: 1e-05, Loss: CNL-specific  
2025-08-24 00:27:00,432 - ERROR -    ❌ Training failed for CNL (Fold 2): Input type (torch.cud
a.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,436 - INFO - 🎯 Training model for CNL (Fold 3)
2025-08-24 00:27:00,436 - INFO -    📊 Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,437 - INFO -    🎯 Using curve-specific dataset for CNL prediction        
2025-08-24 00:27:00,453 - INFO -    📊 Created 45 sequences for CNL prediction
2025-08-24 00:27:00,457 - INFO -    📊 Created 10 sequences for CNL prediction
2025-08-24 00:27:00,457 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,457 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,474 - INFO -    🎯 Using curve-specific training configuration for CNL
2025-08-24 00:27:00,475 - INFO -    🎯 Starting curve-specific training for CNL
2025-08-24 00:27:00,476 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,476 - INFO -    ⚙️  LR: 0.00012, Weight Decay: 1e-05, Loss: CNL-specific  
2025-08-24 00:27:00,480 - ERROR -    ❌ Training failed for CNL (Fold 3): Input type (torch.cud
a.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,484 - INFO - 🎯 Training model for CNL (Fold 4)
2025-08-24 00:27:00,484 - INFO -    📊 Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,485 - INFO -    🎯 Using curve-specific dataset for CNL prediction        
2025-08-24 00:27:00,504 - INFO -    📊 Created 45 sequences for CNL prediction
2025-08-24 00:27:00,510 - INFO -    📊 Created 10 sequences for CNL prediction
2025-08-24 00:27:00,510 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,510 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,525 - INFO -    🎯 Using curve-specific training configuration for CNL
2025-08-24 00:27:00,525 - INFO -    🎯 Starting curve-specific training for CNL
2025-08-24 00:27:00,526 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,526 - INFO -    ⚙️  LR: 0.00012, Weight Decay: 1e-05, Loss: CNL-specific  
2025-08-24 00:27:00,529 - ERROR -    ❌ Training failed for CNL (Fold 4): Input type (torch.cud
a.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,533 - INFO - 🎯 Training model for CNL (Fold 5)
2025-08-24 00:27:00,533 - INFO -    📊 Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,533 - INFO -    🎯 Using curve-specific dataset for CNL prediction        
2025-08-24 00:27:00,550 - INFO -    📊 Created 45 sequences for CNL prediction
2025-08-24 00:27:00,554 - INFO -    📊 Created 10 sequences for CNL prediction
2025-08-24 00:27:00,554 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,554 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,568 - INFO -    🎯 Using curve-specific training configuration for CNL
2025-08-24 00:27:00,568 - INFO -    🎯 Starting curve-specific training for CNL
2025-08-24 00:27:00,569 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,569 - INFO -    ⚙️  LR: 0.00012, Weight Decay: 1e-05, Loss: CNL-specific  
2025-08-24 00:27:00,572 - ERROR -    ❌ Training failed for CNL (Fold 5): Input type (torch.cud
a.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,577 - INFO - 📊 Calculating confidence metrics for CNL...
2025-08-24 00:27:00,577 - WARNING -    ⚠️  No valid R² scores for CNL
2025-08-24 00:27:00,577 - INFO -
================================================================================
2025-08-24 00:27:00,578 - INFO - 🎯 TRAINING GR PREDICTION MODELS
2025-08-24 00:27:00,578 - INFO - ================================================================================
2025-08-24 00:27:00,578 - INFO - 🎯 Training model for GR (Fold 1)
2025-08-24 00:27:00,578 - INFO -    📊 Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,578 - INFO -    🎯 Using curve-specific dataset for GR prediction
2025-08-24 00:27:00,593 - INFO -    📊 Created 45 sequences for GR prediction
2025-08-24 00:27:00,597 - INFO -    📊 Created 10 sequences for GR prediction
2025-08-24 00:27:00,597 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,598 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,616 - INFO -    🎯 Using curve-specific training configuration for GR
2025-08-24 00:27:00,616 - INFO -    🎯 Starting curve-specific training for GR
2025-08-24 00:27:00,617 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,617 - INFO -    ⚙️  LR: 0.0001, Weight Decay: 1e-05, Loss: GR-specific    
2025-08-24 00:27:00,621 - ERROR -    ❌ Training failed for GR (Fold 1): Input type (torch.cuda
.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,626 - INFO - 🎯 Training model for GR (Fold 2)
2025-08-24 00:27:00,626 - INFO -    📊 Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,626 - INFO -    🎯 Using curve-specific dataset for GR prediction
2025-08-24 00:27:00,644 - INFO -    📊 Created 45 sequences for GR prediction
2025-08-24 00:27:00,648 - INFO -    📊 Created 10 sequences for GR prediction
2025-08-24 00:27:00,648 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,648 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,662 - INFO -    🎯 Using curve-specific training configuration for GR
2025-08-24 00:27:00,662 - INFO -    🎯 Starting curve-specific training for GR
2025-08-24 00:27:00,663 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,663 - INFO -    ⚙️  LR: 0.0001, Weight Decay: 1e-05, Loss: GR-specific    
2025-08-24 00:27:00,666 - ERROR -    ❌ Training failed for GR (Fold 2): Input type (torch.cuda
.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,671 - INFO - 🎯 Training model for GR (Fold 3)
2025-08-24 00:27:00,671 - INFO -    📊 Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,671 - INFO -    🎯 Using curve-specific dataset for GR prediction
2025-08-24 00:27:00,687 - INFO -    📊 Created 45 sequences for GR prediction
2025-08-24 00:27:00,691 - INFO -    📊 Created 10 sequences for GR prediction
2025-08-24 00:27:00,692 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,692 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,709 - INFO -    🎯 Using curve-specific training configuration for GR
2025-08-24 00:27:00,709 - INFO -    🎯 Starting curve-specific training for GR
2025-08-24 00:27:00,710 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,710 - INFO -    ⚙️  LR: 0.0001, Weight Decay: 1e-05, Loss: GR-specific    
2025-08-24 00:27:00,713 - ERROR -    ❌ Training failed for GR (Fold 3): Input type (torch.cuda
.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,718 - INFO - 🎯 Training model for GR (Fold 4)
2025-08-24 00:27:00,718 - INFO -    📊 Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,719 - INFO -    🎯 Using curve-specific dataset for GR prediction
2025-08-24 00:27:00,734 - INFO -    📊 Created 45 sequences for GR prediction
2025-08-24 00:27:00,738 - INFO -    📊 Created 10 sequences for GR prediction
2025-08-24 00:27:00,738 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,738 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,754 - INFO -    🎯 Using curve-specific training configuration for GR
2025-08-24 00:27:00,755 - INFO -    🎯 Starting curve-specific training for GR
2025-08-24 00:27:00,755 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,756 - INFO -    ⚙️  LR: 0.0001, Weight Decay: 1e-05, Loss: GR-specific    
2025-08-24 00:27:00,758 - ERROR -    ❌ Training failed for GR (Fold 4): Input type (torch.cuda
.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,764 - INFO - 🎯 Training model for GR (Fold 5)
2025-08-24 00:27:00,764 - INFO -    📊 Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-24 00:27:00,765 - INFO -    🎯 Using curve-specific dataset for GR prediction
2025-08-24 00:27:00,780 - INFO -    📊 Created 45 sequences for GR prediction
2025-08-24 00:27:00,784 - INFO -    📊 Created 10 sequences for GR prediction
2025-08-24 00:27:00,785 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,785 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,801 - INFO -    🎯 Using curve-specific training configuration for GR
2025-08-24 00:27:00,802 - INFO -    🎯 Starting curve-specific training for GR
2025-08-24 00:27:00,802 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,803 - INFO -    ⚙️  LR: 0.0001, Weight Decay: 1e-05, Loss: GR-specific    
2025-08-24 00:27:00,805 - ERROR -    ❌ Training failed for GR (Fold 5): Input type (torch.cuda
.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,809 - INFO - 📊 Calculating confidence metrics for GR...
2025-08-24 00:27:00,809 - WARNING -    ⚠️  No valid R² scores for GR
2025-08-24 00:27:00,809 - INFO -
================================================================================
2025-08-24 00:27:00,809 - INFO - 🎯 TRAINING RLLD PREDICTION MODELS
2025-08-24 00:27:00,810 - INFO - ================================================================================
2025-08-24 00:27:00,810 - INFO - 🎯 Training model for RLLD (Fold 1)
2025-08-24 00:27:00,810 - INFO -    📊 Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-24 00:27:00,810 - INFO -    🎯 Using curve-specific dataset for RLLD prediction       
2025-08-24 00:27:00,825 - INFO -    📊 Created 45 sequences for RLLD prediction
2025-08-24 00:27:00,829 - INFO -    📊 Created 10 sequences for RLLD prediction
2025-08-24 00:27:00,829 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,830 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,847 - INFO -    🎯 Using curve-specific training configuration for RLLD
2025-08-24 00:27:00,848 - INFO -    🎯 Starting curve-specific training for RLLD
2025-08-24 00:27:00,849 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,850 - INFO -    ⚙️  LR: 8e-05, Weight Decay: 1e-05, Loss: RLLD-specific   
2025-08-24 00:27:00,853 - ERROR -    ❌ Training failed for RLLD (Fold 1): Input type (torch.cu
da.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,858 - INFO - 🎯 Training model for RLLD (Fold 2)
2025-08-24 00:27:00,858 - INFO -    📊 Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-24 00:27:00,858 - INFO -    🎯 Using curve-specific dataset for RLLD prediction       
2025-08-24 00:27:00,874 - INFO -    📊 Created 45 sequences for RLLD prediction
2025-08-24 00:27:00,879 - INFO -    📊 Created 10 sequences for RLLD prediction
2025-08-24 00:27:00,879 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,879 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,893 - INFO -    🎯 Using curve-specific training configuration for RLLD
2025-08-24 00:27:00,893 - INFO -    🎯 Starting curve-specific training for RLLD
2025-08-24 00:27:00,894 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,895 - INFO -    ⚙️  LR: 8e-05, Weight Decay: 1e-05, Loss: RLLD-specific   
2025-08-24 00:27:00,898 - ERROR -    ❌ Training failed for RLLD (Fold 2): Input type (torch.cu
da.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,902 - INFO - 🎯 Training model for RLLD (Fold 3)
2025-08-24 00:27:00,902 - INFO -    📊 Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-24 00:27:00,902 - INFO -    🎯 Using curve-specific dataset for RLLD prediction       
2025-08-24 00:27:00,917 - INFO -    📊 Created 45 sequences for RLLD prediction
2025-08-24 00:27:00,921 - INFO -    📊 Created 10 sequences for RLLD prediction
2025-08-24 00:27:00,922 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,922 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,937 - INFO -    🎯 Using curve-specific training configuration for RLLD
2025-08-24 00:27:00,937 - INFO -    🎯 Starting curve-specific training for RLLD
2025-08-24 00:27:00,938 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,939 - INFO -    ⚙️  LR: 8e-05, Weight Decay: 1e-05, Loss: RLLD-specific   
2025-08-24 00:27:00,942 - ERROR -    ❌ Training failed for RLLD (Fold 3): Input type (torch.cu
da.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,947 - INFO - 🎯 Training model for RLLD (Fold 4)
2025-08-24 00:27:00,948 - INFO -    📊 Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-24 00:27:00,948 - INFO -    🎯 Using curve-specific dataset for RLLD prediction       
2025-08-24 00:27:00,965 - INFO -    📊 Created 45 sequences for RLLD prediction
2025-08-24 00:27:00,969 - INFO -    📊 Created 10 sequences for RLLD prediction
2025-08-24 00:27:00,970 - INFO -    📈 Training samples: 45
2025-08-24 00:27:00,970 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:00,984 - INFO -    🎯 Using curve-specific training configuration for RLLD
2025-08-24 00:27:00,985 - INFO -    🎯 Starting curve-specific training for RLLD
2025-08-24 00:27:00,985 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:00,986 - INFO -    ⚙️  LR: 8e-05, Weight Decay: 1e-05, Loss: RLLD-specific   
2025-08-24 00:27:00,989 - ERROR -    ❌ Training failed for RLLD (Fold 4): Input type (torch.cu
da.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:00,994 - INFO - 🎯 Training model for RLLD (Fold 5)
2025-08-24 00:27:00,995 - INFO -    📊 Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-24 00:27:00,995 - INFO -    🎯 Using curve-specific dataset for RLLD prediction       
2025-08-24 00:27:01,011 - INFO -    📊 Created 45 sequences for RLLD prediction
2025-08-24 00:27:01,016 - INFO -    📊 Created 10 sequences for RLLD prediction
2025-08-24 00:27:01,016 - INFO -    📈 Training samples: 45
2025-08-24 00:27:01,016 - INFO -    📊 Validation samples: 10
2025-08-24 00:27:01,033 - INFO -    🎯 Using curve-specific training configuration for RLLD
2025-08-24 00:27:01,033 - INFO -    🎯 Starting curve-specific training for RLLD
2025-08-24 00:27:01,034 - INFO -    📊 Training samples: 45, Validation samples: 10
2025-08-24 00:27:01,034 - INFO -    ⚙️  LR: 8e-05, Weight Decay: 1e-05, Loss: RLLD-specific   
2025-08-24 00:27:01,038 - ERROR -    ❌ Training failed for RLLD (Fold 5): Input type (torch.cu
da.FloatTensor) and weight type (torch.FloatTensor) should be the same
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 672, in train_single_model
    training_results = self._train_curve_specific_model(
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\training\enhanced_multi_curve_training.py", line 747, in _train_curve_specific_model       
    outputs = model(inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\vp_model_improved.py", line 83, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.cuda.FloatTensor) and weight type (torch.FloatTensor) should be the same
2025-08-24 00:27:01,043 - INFO - 📊 Calculating confidence metrics for RLLD...
2025-08-24 00:27:01,043 - WARNING -    ⚠️  No valid R² scores for RLLD
2025-08-24 00:27:01,044 - INFO - 💾 Saving comprehensive results...
2025-08-24 00:27:01,045 - INFO -    ✅ Results saved to: enhanced_training_outputs\enhanced_tra
ining_results.json
2025-08-24 00:27:01,045 - INFO - 📋 Generating summary report...
2025-08-24 00:27:01,047 - INFO -    ✅ Summary report saved to: enhanced_training_outputs\train
ing_summary_report.md
2025-08-24 00:27:01,047 - INFO -
🎉 Enhanced Training Pipeline Complete!
2025-08-24 00:27:01,047 - INFO - 📁 Results saved in: enhanced_training_outputs
(imputeML) PS D:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer> 