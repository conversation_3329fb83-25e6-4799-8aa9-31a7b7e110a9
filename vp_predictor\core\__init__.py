"""
Core components for General Well Log Transformer

Contains generalized versions of the original VpTransformer components
that support flexible single-target and multi-curve predictions.
"""

from .decoder import GeneralDecoder, VpCompatibleDecoder, MultiCurveDecoder
from .normalizer import GeneralDataNormalizer, VpCompatibleNormalizer
from .transformer import (
    GeneralWellLogTransformer,
    GWLT_VpPrediction, GWLT_DensityPrediction, GWLT_MultiCurve,
    GWLT_MissingSectionFill, GWLT_FastInference,
    GWLT_Small, GWLT_Base, GWLT_Large,
    GWLT_Vp_Small, GWLT_Vp_Base, GWLT_Vp_Large,
    VpTransformer_Compatible
)

# Phase 2 additions - Training infrastructure
from .dataset import GeneralWellLogDataset, VpDatasetCompatible, create_dataset_from_template
from .loss_functions import (
    GeneralWellLogLoss, CurveSpecificLossFactory, VpLossCompatible,
    MultiCurveLossBalancer,
    create_vp_loss, create_density_loss, create_neutron_loss,
    create_gamma_ray_loss, create_resistivity_loss
)
from .training import GeneralTrainingManager, create_vp_trainer, create_density_trainer

__all__ = [
    # Phase 1 - Core components
    'GeneralDecoder', 'VpCompatibleDecoder', 'MultiCurveDecoder',
    'GeneralDataNormalizer', 'VpCompatibleNormalizer',
    
    # Main transformer
    'GeneralWellLogTransformer',
    
    # Template-based models
    'GWLT_VpPrediction', 'GWLT_DensityPrediction', 'GWLT_MultiCurve',
    'GWLT_MissingSectionFill', 'GWLT_FastInference',
    
    # Size variants
    'GWLT_Small', 'GWLT_Base', 'GWLT_Large',
    
    # Backward compatible variants
    'GWLT_Vp_Small', 'GWLT_Vp_Base', 'GWLT_Vp_Large',
    'VpTransformer_Compatible',
    
    # Phase 2 - Training infrastructure
    # Datasets
    'GeneralWellLogDataset', 'VpDatasetCompatible', 'create_dataset_from_template',
    
    # Loss functions
    'GeneralWellLogLoss', 'CurveSpecificLossFactory', 'VpLossCompatible',
    'MultiCurveLossBalancer',
    'create_vp_loss', 'create_density_loss', 'create_neutron_loss',
    'create_gamma_ray_loss', 'create_resistivity_loss',
    
    # Training managers
    'GeneralTrainingManager', 'create_vp_trainer', 'create_density_trainer'
]