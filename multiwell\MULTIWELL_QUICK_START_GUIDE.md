# MWLT MultiWell - Quick Start Guide

## 🚀 Overview

This guide provides simple, step-by-step instructions to use the MWLT MultiWell system for training and predicting multiple well log curves (GR, CNL, DEN, AC, RLLD).

## 📋 Prerequisites

### Required Dependencies
```bash
pip install torch numpy matplotlib scikit-learn scipy h5py tqdm
```

### Required Files
Ensure the `vp_predictor` package is available in your Python path.

## 🎯 Quick Start - Complete Workflow

### Option 1: Use the Launcher (Recommended)
```bash
# Navigate to multiwell directory
cd multiwell/

# Use the unified launcher
python multiwell_launcher.py train      # Enhanced training
python multiwell_launcher.py validate   # Validation suite
python multiwell_launcher.py predict    # Prediction demo
python multiwell_launcher.py demos      # Interactive demos
python multiwell_launcher.py test       # Quick tests
python multiwell_launcher.py status     # Show status
```

### Option 2: Manual Step-by-Step Process

## 📊 Step-by-Step Process

### Step 1: Train Multi-Curve Models
```bash
# Navigate to training directory
cd multiwell/training/

# Run enhanced multi-curve training
python enhanced_multi_curve_training.py
```

**What happens:**
- Trains 25 models (5 folds × 5 curves: GR, CNL, DEN, AC, RLLD)
- Uses 5-fold cross-validation for robust performance assessment
- Saves models to `training/outputs/` (self-contained)
- Generates comprehensive training reports

**Expected Output:**
```
🚀 Enhanced Multi-Curve Training Pipeline
Training GR models (5 folds)...
Training CNL models (5 folds)...
Training DEN models (5 folds)...
Training AC models (5 folds)...
Training RLLD models (5 folds)...
✅ Training completed: 25 models created
📊 Results saved to training/outputs/
```

**Expected Performance:**
- **Density R²**: >0.7 (improved from -11.18)
- **Neutron R²**: >0.6 (improved from -0.23)
- **Gamma Ray R²**: >0.5 (improved from -0.31)
- **Resistivity R²**: >0.5 (improved from -0.31)

### Step 2: Select Best Production Models
```bash
# Navigate to utils directory
cd multiwell/utils/

# Run production model selector
python production_model_selector.py
```

**What happens:**
- Analyzes all 25 cross-validation models
- Selects the best model for each curve using composite scoring
- Creates `production_models/` directory with selected models
- Generates detailed selection reports

**Expected Output:**
```
🔍 Analyzing Cross-Validation Results...
📊 Selecting Best Models:
   GR: fold_2 selected (R²=0.78, Loss=0.045)
   CNL: fold_1 selected (R²=0.72, Loss=0.052)
   DEN: fold_3 selected (R²=0.81, Loss=0.038)
   AC: fold_0 selected (R²=0.85, Loss=0.032)
   RLLD: fold_4 selected (R²=0.69, Loss=0.058)
✅ Production models ready in production_models/
```

### Step 3: Validate Production Models
```bash
# Navigate to validation directory
cd multiwell/validation/

# Run comprehensive validation (uses training/outputs/ by default)
python model_validation_suite.py
```

**What happens:**
- Loads models from `training/outputs/` for validation
- Performs independent cross-validation analysis
- Detects overfitting and calculates uncertainty
- Generates diagnostic plots and reports in `validation/outputs/`

**Expected Output:**
```
🔍 Comprehensive Model Validation Suite
Loading production models...
Performing cross-validation analysis...
Generating diagnostic plots...
✅ Validation completed
📊 All models show good performance and stability
```

### Step 4: Make Predictions
```bash
# Navigate to prediction directory
cd multiwell/prediction/

# Run improved prediction demo
python improved_multi_curve_prediction_demo.py
```

**What happens:**
- Loads best models from `training/outputs/` automatically
- Makes predictions on test data
- Creates comprehensive visualizations in `prediction/outputs/`
- Provides detailed performance analysis

**Expected Output:**
```
🎯 Improved Multi-Curve Prediction Demo
Loading production models...
Making predictions for all curves...
Generating visualizations...
✅ Predictions completed
📊 Results show excellent agreement with actual data
```

### Step 5: Interactive Demonstrations
```bash
# Navigate to demos directory
cd multiwell/demos/

# Run interactive demo suite
python demo_curve_prediction.py
```

**What happens:**
- Provides interactive demonstrations of all curve predictions
- Shows VP prediction (backward compatible)
- Demonstrates density, neutron, and multi-curve predictions
- Includes training simulation capabilities

## 📁 Understanding the Results

After running the complete workflow, check these directories:

```
multiwell/
├── training/
│   └── outputs/                       # 🆕 25 cross-validation models
│       ├── GR_fold_0/ → GR_fold_4/    # Gamma Ray models
│       ├── CNL_fold_0/ → CNL_fold_4/  # Neutron models
│       ├── DEN_fold_0/ → DEN_fold_4/  # Density models
│       ├── AC_fold_0/ → AC_fold_4/    # Acoustic models
│       ├── RLLD_fold_0/ → RLLD_fold_4/# Resistivity models
│       ├── enhanced_training_results.json  # Comprehensive results
│       └── training_summary_report.md      # Training summary
│
├── validation/
│   └── outputs/                       # 🆕 Validation results
│       ├── comprehensive_validation_results.json  # Performance assessment
│       ├── validation_report.md                   # Detailed report
│       └── {CURVE}_validation_results.png         # Per-curve plots
│
├── prediction/
│   └── outputs/                       # 🆕 Prediction results
│       └── improved_multi_curve_demo_{well}.png   # Visualizations
│
└── production_models/                 # 🔮 Future: Selected best models
    ├── gr_production_model/           # Best GR model
    ├── cnl_production_model/          # Best CNL model
    ├── den_production_model/          # Best DEN model
    ├── ac_production_model/           # Best AC model
    └── rlld_production_model/         # Best RLLD model
```

## 🎨 Key Visualizations Generated

### Training Progress
- Cross-validation performance across folds
- Training and validation loss curves
- R² score progression for each curve

### Model Selection Analysis
- Composite score comparison across folds
- Performance metrics visualization
- Selection justification reports

### Validation Results
- 6-panel diagnostic plots per curve
- Overfitting detection analysis
- Uncertainty quantification plots

### Prediction Analysis
- Predicted vs Actual scatter plots
- Multi-curve comparison plots
- Error distribution analysis

## ⚙️ Customization Options

### Custom Training Configuration
Edit `enhanced_multi_curve_training.py` to modify:
- Number of cross-validation folds
- Training epochs and learning rates
- Model architecture parameters
- Curve-specific configurations

### Custom Model Selection Criteria
Edit `production_model_selector.py` to adjust:
```python
composite_score = (
    0.5 * r2_score +           # 50% weight on R² performance
    0.3 * loss_score +         # 30% weight on validation loss  
    0.2 * stability_score      # 20% weight on training stability
)
```

### Custom Validation Parameters
Edit `model_validation_suite.py` to modify:
- Monte Carlo dropout samples (default: 50-100)
- Confidence interval levels (default: 95%)
- Cross-validation folds for validation

## 🔧 Troubleshooting

### Common Issues

**1. Import Errors**
```
❌ ModuleNotFoundError: No module named 'vp_predictor'
```
**Solution:** Ensure `vp_predictor` package is in Python path:
```bash
# Run from project root directory
cd Init_transformer/
python -m multiwell.training.enhanced_multi_curve_training
```

**2. No Training Data**
```
❌ No training data available
```
**Solution:** Scripts include fallback to synthetic data generation

**3. Model Loading Issues**
```
❌ Cannot load production models
```
**Solution:** Ensure Step 2 (model selection) was completed:
```bash
cd multiwell/utils/
python production_model_selector.py
```

**4. Poor Performance**
```
⚠️ R² scores below expected values
```
**Solution:** 
- Check data quality and preprocessing
- Verify the critical normalization fix is applied
- Increase training epochs or adjust learning rates

### Getting Help
```bash
# Test basic functionality
cd multiwell/utils/
python simple_test.py

# Check priority fixes implementation
python test_priority_fixes.py

# View launcher options
cd multiwell/
python multiwell_launcher.py --help
```

## 📈 Performance Expectations

### Training Results
- **25 models trained** (5 folds × 5 curves)
- **Training time**: 2-4 hours (depending on hardware)
- **Cross-validation R²**: 0.5-0.85 depending on curve

### Model Selection Results
- **5 production models** selected (1 per curve)
- **Selection criteria**: Composite score combining R², loss, stability
- **Improvement**: Significant performance gains over previous versions

### Prediction Results
- **Multi-curve predictions** with uncertainty quantification
- **Geological consistency** preserved across curves
- **Production-ready models** with comprehensive validation

## 🎯 Supported Curves

- **GR**: Gamma Ray (geological indicator)
- **CNL**: Neutron (porosity proxy)
- **DEN**: Density (porosity/lithology)
- **AC**: Acoustic (velocity/porosity)
- **RLLD**: Resistivity (fluid saturation)

## 📚 Additional Resources

- **Detailed Documentation**: `README.md`
- **Technical Analysis**: `BEST_MODEL_SELECTION_AND_TRANSFER.md`
- **Organization Overview**: `ORGANIZATION_SUMMARY.md`
- **Codebase Documentation**: `codebase_multiwell.md`

---

**Quick Command Reference:**
```bash
# Complete workflow using launcher
python multiwell_launcher.py train
python multiwell_launcher.py validate
python multiwell_launcher.py predict

# Manual workflow (self-contained outputs)
cd training/ && python enhanced_multi_curve_training.py     # → training/outputs/
cd ../utils/ && python production_model_selector.py
cd ../validation/ && python model_validation_suite.py       # → validation/outputs/
cd ../prediction/ && python improved_multi_curve_prediction_demo.py  # → prediction/outputs/

# Testing and demos
cd utils/ && python simple_test.py
cd ../demos/ && python demo_curve_prediction.py
```

**🎉 Ready to predict multiple well log curves with state-of-the-art transformer models!**
