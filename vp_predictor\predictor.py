"""
VpTransformer Integration Wrapper
Provides simple API for incorporating VpTransformer into external applications
"""
import torch
import numpy as np
import os
from pathlib import Path
from typing import Dict, List, Union, Optional, Tuple
import warnings

from .vp_model_improved import MWLT_Vp_Base, MWLT_Vp_Small, MWLT_Vp_Large, VpDataNormalizer
from .utils import get_device, load_checkpoint
from .las_processor import LASProcessor

class VpPredictor:
    """
    Simple wrapper for VpTransformer model integration
    
    This class provides a clean interface for using trained VpTransformer models
    for sonic velocity prediction from well log curves.
    """
    
    def __init__(self, model_path: str, device_id: int = 0, model_type: str = "base"):
        """
        Initialize the Vp predictor
        
        Args:
            model_path: Path to trained model checkpoint (.pth file)
            device_id: GPU device ID (0 for first GPU, -1 for CPU)
            model_type: Model size ("small", "base", or "large")
        """
        self.model_path = model_path
        self.model_type = model_type
        self.device = get_device(device_id if device_id >= 0 else 0)
        if device_id == -1:
            self.device = torch.device('cpu')
        
        self.normalizer = VpDataNormalizer()
        self.processor = LASProcessor()
        
        # Load model
        self._load_model()
        
    def _load_model(self):
        """Load the trained model"""
        if not os.path.exists(self.model_path):
            raise FileNotFoundError(f"Model file not found: {self.model_path}")
            
        # Create model based on type
        if self.model_type.lower() == 'small':
            self.model = MWLT_Vp_Small()
        elif self.model_type.lower() == 'base':
            self.model = MWLT_Vp_Base()
        elif self.model_type.lower() == 'large':
            self.model = MWLT_Vp_Large()
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
            
        # Load checkpoint
        try:
            model_dict, epoch, loss = load_checkpoint(self.model_path, self.device)
            self.model.load_state_dict(model_dict)
            self.model.to(self.device)
            self.model.eval()
            print(f"Loaded {self.model_type} model from epoch {epoch} with loss {loss:.4f}")
        except Exception as e:
            print(f"Error loading model: {e}")
            raise
            
    def predict_from_curves(self, gr: np.ndarray, cnl: np.ndarray, 
                           den: np.ndarray, rlld: np.ndarray) -> np.ndarray:
        """
        Predict Vp from individual well log curves
        
        Args:
            gr: Gamma Ray curve (array-like)
            cnl: Neutron curve (array-like) 
            den: Density curve (array-like)
            rlld: Resistivity curve (array-like)
            
        Returns:
            vp_prediction: Predicted sonic velocity values [μs/ft]
        """
        # Prepare input data
        curves = {
            'GR': np.array(gr),
            'CNL': np.array(cnl), 
            'DEN': np.array(den),
            'RLLD': np.array(rlld)
        }
        
        input_features, _ = self.processor.prepare_for_prediction(curves)
        
        # Ensure correct sequence length (640)
        target_length = 640
        if input_features.shape[1] > target_length:
            input_features = input_features[:, :target_length]
        elif input_features.shape[1] < target_length:
            padding = target_length - input_features.shape[1]
            input_features = np.pad(input_features, ((0,0), (0,padding)))
        
        # Convert to tensor and predict
        input_tensor = torch.FloatTensor(input_features).unsqueeze(0).to(self.device)
        
        with torch.no_grad():
            prediction_normalized = self.model(input_tensor)
            # Denormalize predictions to get physical units
            prediction_tensor = torch.from_numpy(prediction_normalized.cpu().numpy())
            prediction_physical = self.normalizer.denormalize_vp(prediction_tensor)
            vp_prediction = prediction_physical.numpy().flatten()
            
        return vp_prediction
    
    def predict_from_file(self, file_path: str) -> np.ndarray:
        """
        Predict Vp from LAS or HDF5 file
        
        Args:
            file_path: Path to LAS or HDF5 file
            
        Returns:
            vp_prediction: Predicted sonic velocity values [μs/ft]
        """
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Input file not found: {file_path}")
            
        # Process file
        file_ext = Path(file_path).suffix.lower()
        if file_ext == '.hdf5':
            curves = self.processor.process_hdf5_to_las_format(file_path)
        elif file_ext == '.las':
            curves = self.processor.read_las_file(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        # Extract curves and predict
        gr = curves.get('GR', np.zeros(640))
        cnl = curves.get('CNL', np.zeros(640))
        den = curves.get('DEN', np.zeros(640))
        rlld = curves.get('RLLD', np.zeros(640))
        
        return self.predict_from_curves(gr, cnl, den, rlld)
        
    def get_model_info(self) -> Dict:
        """Get comprehensive model information"""
        return {
            'model_type': self.model_type,
            'architecture': 'VpTransformer',
            'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
            'output_range': (40, 400),
            'sequence_length': 640,
            'improvements': ['sigmoid_fix', 'proper_scaling', 'enhanced_normalization'],
            'device': str(self.device),
            'model_path': self.model_path
        }

class VpTransformerAPI:
    """
    Production-ready API for improved VpTransformer integration
    
    This class provides advanced features including batch processing,
    validation, confidence scoring, and comprehensive error handling.
    """
    
    def __init__(self, model_path: str, device: str = "auto", model_type: str = "base"):
        """
        Initialize with model path and device preference
        
        Args:
            model_path: Path to trained model checkpoint
            device: "auto", "gpu", "cpu", or specific device ID
            model_type: "small", "base", or "large" variant
        """
        self.model_path = model_path
        self.model_type = model_type
        
        # Setup device
        if device == "auto":
            self.device = get_device()
        elif device == "gpu":
            self.device = get_device(0)  
        elif device == "cpu":
            self.device = torch.device("cpu")
        else:
            try:
                device_id = int(device)
                self.device = get_device(device_id)
            except:
                self.device = get_device()
                warnings.warn(f"Invalid device '{device}', using auto-detection")
        
        # Initialize components
        self.normalizer = VpDataNormalizer()
        self.processor = LASProcessor()
        self.model = None
        
        # Load model
        self._initialize_model()
        
    def _initialize_model(self):
        """Initialize and load the model"""
        # Create model
        if self.model_type == "small":
            self.model = MWLT_Vp_Small()
        elif self.model_type == "base":
            self.model = MWLT_Vp_Base()
        elif self.model_type == "large":
            self.model = MWLT_Vp_Large()
        else:
            raise ValueError(f"Unknown model type: {self.model_type}")
            
        # Load checkpoint
        if os.path.exists(self.model_path):
            model_dict, epoch, loss = load_checkpoint(self.model_path, self.device)
            self.model.load_state_dict(model_dict)
            self.model.to(self.device)
            self.model.eval()
            print(f"Initialized {self.model_type} VpTransformer from epoch {epoch}")
        else:
            raise FileNotFoundError(f"Model checkpoint not found: {self.model_path}")
    
    def predict(self, data: Union[Dict, str], format: str = "curves", 
                validate: bool = True) -> Dict:
        """
        Enhanced prediction method with validation and metadata
        
        Args:
            data: Input data (curves dict or file path)
            format: "curves", "file", or "arrays"
            validate: Whether to validate input ranges and quality
            
        Returns:
            Dictionary containing:
            - predictions: Vp predictions [40-400 μs/ft]
            - confidence: Prediction confidence scores  
            - metadata: Processing metadata
            - warnings: Data quality warnings
        """
        warnings_list = []
        metadata = {'format': format, 'device': str(self.device)}
        
        try:
            # Process input data based on format
            if format == "file":
                if not isinstance(data, str):
                    raise ValueError("File format requires string path")
                curves = self._load_file(data)
                metadata['file_path'] = data
            elif format == "curves":
                if not isinstance(data, dict):
                    raise ValueError("Curves format requires dictionary")
                curves = data.copy()
            else:
                raise ValueError(f"Unknown format: {format}")
            
            # Validate input if requested
            if validate:
                validation_result = self.validate_input(curves)
                warnings_list.extend(validation_result.get('warnings', []))
                metadata['validation'] = validation_result
            
            # Prepare data for prediction
            input_features = self._prepare_input(curves)
            
            # Make prediction
            with torch.no_grad():
                input_tensor = torch.FloatTensor(input_features).unsqueeze(0).to(self.device)
                prediction_normalized = self.model(input_tensor)
                prediction_tensor = torch.from_numpy(prediction_normalized.cpu().numpy())
                prediction_physical = self.normalizer.denormalize_vp(prediction_tensor)
                predictions = prediction_physical.numpy().flatten()
            
            # Calculate confidence scores (simplified implementation)
            confidence = self._calculate_confidence(predictions)
            
            # Update metadata
            metadata.update({
                'prediction_range': (predictions.min(), predictions.max()),
                'mean_prediction': predictions.mean(),
                'std_prediction': predictions.std(),
                'sequence_length': len(predictions)
            })
            
            return {
                'predictions': predictions,
                'confidence': confidence,
                'metadata': metadata,
                'warnings': warnings_list
            }
            
        except Exception as e:
            return {
                'predictions': None,
                'confidence': None,
                'metadata': metadata,
                'warnings': warnings_list + [f"Prediction failed: {str(e)}"],
                'error': str(e)
            }
    
    def batch_predict(self, data_list: List, parallel: bool = True) -> List[Dict]:
        """
        Enhanced batch prediction with optional parallelization
        
        Args:
            data_list: List of input data
            parallel: Whether to use parallel processing (future implementation)
            
        Returns:
            List of prediction results
        """
        results = []
        for i, data in enumerate(data_list):
            try:
                result = self.predict(data)
                result['batch_index'] = i
                results.append(result)
            except Exception as e:
                results.append({
                    'predictions': None,
                    'confidence': None,
                    'metadata': {'batch_index': i},
                    'warnings': [f"Batch item {i} failed: {str(e)}"],
                    'error': str(e)
                })
        return results
    
    def validate_input(self, curves: Dict) -> Dict:
        """
        Enhanced input validation with detailed feedback
        
        Args:
            curves: Dictionary of well log curves
            
        Returns:
            Validation results with warnings and suggestions
        """
        warnings_list = []
        required_curves = ['GR', 'CNL', 'DEN', 'RLLD']
        
        # Check required curves
        missing_curves = [curve for curve in required_curves if curve not in curves]
        if missing_curves:
            warnings_list.append(f"Missing required curves: {missing_curves}")
        
        # Check curve ranges and quality
        range_checks = {
            'GR': (0, 500),    # Gamma Ray range
            'CNL': (0, 100),   # Neutron range  
            'DEN': (1.0, 4.0), # Density range
            'RLLD': (0.01, 10000)  # Resistivity range
        }
        
        for curve_name, (min_val, max_val) in range_checks.items():
            if curve_name in curves:
                data = np.array(curves[curve_name])
                if np.any(data < min_val) or np.any(data > max_val):
                    warnings_list.append(f"{curve_name} values outside typical range [{min_val}, {max_val}]")
                if np.any(np.isnan(data)) or np.any(np.isinf(data)):
                    warnings_list.append(f"{curve_name} contains invalid values (NaN/Inf)")
        
        return {
            'valid': len(warnings_list) == 0,
            'warnings': warnings_list,
            'required_curves': required_curves,
            'available_curves': list(curves.keys())
        }
    
    def verify_model_integrity(self) -> bool:
        """
        Verify model was trained with improved architecture
        
        Returns:
            True if model has improvement indicators
        """
        try:
            # Check if model has VpDecoder improvements
            has_vp_decoder = hasattr(self.model, 'decoder')
            model_info = self.get_model_info()
            has_improvements = model_info.get('improvements', [])
            
            return has_vp_decoder and len(has_improvements) > 0
        except:
            return False
    
    def get_model_info(self) -> Dict:
        """Comprehensive model metadata and capabilities"""
        return {
            'model_type': self.model_type,
            'architecture': 'VpTransformer',
            'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'],
            'output_range': (40, 400),
            'sequence_length': 640,
            'improvements': ['sigmoid_fix', 'proper_scaling', 'enhanced_normalization'],
            'device': str(self.device),
            'model_path': self.model_path,
            'version': '1.0.0',
            'integrity_check': self.verify_model_integrity()
        }
    
    def _load_file(self, file_path: str) -> Dict:
        """Load curves from file"""
        file_ext = Path(file_path).suffix.lower()
        if file_ext == '.hdf5':
            return self.processor.process_hdf5_to_las_format(file_path)
        elif file_ext == '.las':
            return self.processor.read_las_file(file_path)
        else:
            raise ValueError(f"Unsupported file format: {file_ext}")
    
    def _prepare_input(self, curves: Dict) -> np.ndarray:
        """Prepare curves for model input"""
        input_features, _ = self.processor.prepare_for_prediction(curves)
        
        # Ensure correct sequence length
        target_length = 640
        if input_features.shape[1] > target_length:
            input_features = input_features[:, :target_length]
        elif input_features.shape[1] < target_length:
            padding = target_length - input_features.shape[1]
            input_features = np.pad(input_features, ((0,0), (0,padding)))
        
        return input_features
    
    def _calculate_confidence(self, predictions: np.ndarray) -> np.ndarray:
        """
        Calculate prediction confidence scores (simplified implementation)
        In a production system, this would use model uncertainty estimation
        """
        # Simple confidence based on prediction smoothness and range
        diff = np.abs(np.diff(predictions))
        smoothness = 1.0 / (1.0 + diff.std())
        
        # Range-based confidence (higher for values in typical range)
        typical_range = (60, 120)  # Typical Vp range
        range_confidence = np.ones_like(predictions)
        range_confidence[(predictions < typical_range[0]) | (predictions > typical_range[1])] *= 0.8
        
        # Combine factors
        base_confidence = smoothness * 0.7 + 0.3  # Base confidence 0.3-1.0
        confidence = base_confidence * range_confidence
        
        # Pad to match prediction length
        if len(confidence) < len(predictions):
            confidence = np.pad(confidence, (0, len(predictions) - len(confidence)), 
                               mode='constant', constant_values=confidence[-1] if len(confidence) > 0 else 0.7)
        
        return np.clip(confidence, 0.0, 1.0)