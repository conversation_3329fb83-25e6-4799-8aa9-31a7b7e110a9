"""
Advanced Performance Optimization for PyTorch Well Log Transformers

This module implements cutting-edge PyTorch optimization techniques:
- Mixed precision training with automatic loss scaling
- Gradient checkpointing for memory efficiency
- Dynamic batch sizing and memory management
- TorchScript compilation and ONNX export
- Advanced profiling and performance monitoring
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.cuda.amp import autocast, GradScaler
import torch.utils.checkpoint as checkpoint
from torch.profiler import profile, record_function, ProfilerActivity
from typing import Dict, List, Optional, Tuple, Any, Callable
import logging
import time
import gc
from contextlib import contextmanager
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetrics:
    """Container for performance metrics"""
    forward_time: float
    memory_peak: float
    memory_allocated: float
    memory_cached: float
    throughput_samples_per_second: float
    gpu_utilization: float
    recommendations: List[str]


class MixedPrecisionManager:
    """Advanced mixed precision training manager with automatic loss scaling"""
    
    def __init__(self, enabled: bool = True, init_scale: float = 2**16, 
                 growth_factor: float = 2.0, backoff_factor: float = 0.5,
                 growth_interval: int = 2000):
        self.enabled = enabled and torch.cuda.is_available()
        self.scaler = GradScaler(
            init_scale=init_scale,
            growth_factor=growth_factor,
            backoff_factor=backoff_factor,
            growth_interval=growth_interval,
            enabled=self.enabled
        )
        self.autocast_context = autocast(enabled=self.enabled)
        
        if self.enabled:
            logger.info("Mixed precision training enabled")
        
    @contextmanager
    def autocast_context_manager(self):
        """Context manager for autocast"""
        with self.autocast_context:
            yield
    
    def scale_loss(self, loss: torch.Tensor) -> torch.Tensor:
        """Scale loss for backward pass"""
        return self.scaler.scale(loss)
    
    def step_optimizer(self, optimizer: torch.optim.Optimizer) -> bool:
        """Step optimizer with gradient scaling"""
        self.scaler.step(optimizer)
        self.scaler.update()
        
        # Check if step was successful (no inf/NaN gradients)
        return self.scaler.get_scale() > 0
    
    def backward(self, loss: torch.Tensor):
        """Backward pass with scaling"""
        scaled_loss = self.scale_loss(loss)
        scaled_loss.backward()


class GradientCheckpointingWrapper(nn.Module):
    """Wrapper for gradient checkpointing to save memory"""
    
    def __init__(self, model: nn.Module, checkpoint_segments: int = 2):
        super().__init__()
        self.model = model
        self.checkpoint_segments = checkpoint_segments
        
        # Split model into segments for checkpointing
        self.segments = self._create_segments()
        
    def _create_segments(self) -> List[nn.Module]:
        """Create model segments for checkpointing"""
        if hasattr(self.model, 'encoder_blocks'):
            # For transformer models
            encoder_blocks = list(self.model.encoder_blocks)
            segment_size = len(encoder_blocks) // self.checkpoint_segments
            
            segments = []
            for i in range(self.checkpoint_segments):
                start_idx = i * segment_size
                end_idx = (i + 1) * segment_size if i < self.checkpoint_segments - 1 else len(encoder_blocks)
                segment = nn.Sequential(*encoder_blocks[start_idx:end_idx])
                segments.append(segment)
            
            return segments
        else:
            # For other models, use the full model as single segment
            return [self.model]
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Forward pass with gradient checkpointing"""
        if self.training and len(self.segments) > 1:
            # Use gradient checkpointing
            for segment in self.segments:
                x = checkpoint.checkpoint(segment, x)
            return x
        else:
            # Regular forward pass
            return self.model(x)


class DynamicBatchSizer:
    """Dynamic batch sizing based on available GPU memory"""
    
    def __init__(self, initial_batch_size: int = 8, max_batch_size: int = 64,
                 memory_threshold: float = 0.85, growth_factor: float = 1.2):
        self.initial_batch_size = initial_batch_size
        self.max_batch_size = max_batch_size
        self.memory_threshold = memory_threshold
        self.growth_factor = growth_factor
        self.current_batch_size = initial_batch_size
        self.memory_history = []
        
    def adjust_batch_size(self, model: nn.Module, sample_input: torch.Tensor) -> int:
        """Dynamically adjust batch size based on memory usage"""
        if not torch.cuda.is_available():
            return self.current_batch_size
        
        torch.cuda.empty_cache()
        device = next(model.parameters()).device
        
        # Test memory usage with current batch size
        with torch.no_grad():
            try:
                # Create test batch
                batch_input = sample_input.unsqueeze(0).repeat(self.current_batch_size, 1, 1)
                batch_input = batch_input.to(device)
                
                # Forward pass
                model.eval()
                _ = model(batch_input)
                
                # Check memory usage
                memory_used = torch.cuda.memory_allocated(device) / torch.cuda.max_memory_allocated(device)
                self.memory_history.append(memory_used)
                
                # Keep only recent history
                if len(self.memory_history) > 10:
                    self.memory_history = self.memory_history[-10:]
                
                avg_memory_usage = np.mean(self.memory_history)
                
                # Adjust batch size
                if avg_memory_usage < self.memory_threshold * 0.7 and self.current_batch_size < self.max_batch_size:
                    # Increase batch size
                    new_batch_size = min(
                        int(self.current_batch_size * self.growth_factor),
                        self.max_batch_size
                    )
                    logger.info(f"Increasing batch size: {self.current_batch_size} → {new_batch_size}")
                    self.current_batch_size = new_batch_size
                    
                elif avg_memory_usage > self.memory_threshold:
                    # Decrease batch size
                    new_batch_size = max(
                        int(self.current_batch_size / self.growth_factor),
                        1
                    )
                    logger.info(f"Decreasing batch size: {self.current_batch_size} → {new_batch_size}")
                    self.current_batch_size = new_batch_size
                    
            except RuntimeError as e:
                if "out of memory" in str(e):
                    # Reduce batch size significantly
                    self.current_batch_size = max(1, self.current_batch_size // 2)
                    logger.warning(f"OOM detected, reducing batch size to {self.current_batch_size}")
                    torch.cuda.empty_cache()
                else:
                    raise e
        
        return self.current_batch_size


class AdvancedProfiler:
    """Advanced profiling with memory tracking and bottleneck detection"""
    
    def __init__(self, output_dir: str = "./profiling_results"):
        self.output_dir = output_dir
        self.metrics_history = []
        
    @contextmanager
    def profile_context(self, description: str = "operation"):
        """Context manager for profiling operations"""
        start_time = time.time()
        start_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
        
        if torch.cuda.is_available():
            torch.cuda.synchronize()
        
        try:
            with record_function(description):
                yield
        finally:
            if torch.cuda.is_available():
                torch.cuda.synchronize()
            
            end_time = time.time()
            end_memory = torch.cuda.memory_allocated() if torch.cuda.is_available() else 0
            
            duration = end_time - start_time
            memory_delta = end_memory - start_memory
            
            logger.debug(f"{description}: {duration:.4f}s, memory: {memory_delta/1024**2:.1f}MB")
    
    def profile_model_inference(self, model: nn.Module, sample_input: torch.Tensor, 
                               num_iterations: int = 10) -> PerformanceMetrics:
        """Comprehensive model profiling"""
        device = next(model.parameters()).device
        sample_input = sample_input.to(device)
        
        # Warm up
        model.eval()
        with torch.no_grad():
            for _ in range(3):
                _ = model(sample_input.unsqueeze(0))
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            torch.cuda.reset_peak_memory_stats()
        
        # Profiling
        forward_times = []
        memory_snapshots = []
        
        with torch.profiler.profile(
            activities=[ProfilerActivity.CPU, ProfilerActivity.CUDA] if torch.cuda.is_available() else [ProfilerActivity.CPU],
            record_shapes=True,
            with_stack=True
        ) as prof:
            
            with torch.no_grad():
                for i in range(num_iterations):
                    start_time = time.time()
                    
                    if torch.cuda.is_available():
                        torch.cuda.synchronize()
                    
                    with record_function(f"inference_iter_{i}"):
                        output = model(sample_input.unsqueeze(0))
                    
                    if torch.cuda.is_available():
                        torch.cuda.synchronize()
                    
                    end_time = time.time()
                    forward_times.append(end_time - start_time)
                    
                    if torch.cuda.is_available():
                        memory_snapshots.append({
                            'allocated': torch.cuda.memory_allocated(),
                            'cached': torch.cuda.memory_reserved(),
                            'peak': torch.cuda.max_memory_allocated()
                        })
        
        # Calculate metrics
        avg_forward_time = np.mean(forward_times)
        throughput = 1.0 / avg_forward_time if avg_forward_time > 0 else 0
        
        if torch.cuda.is_available() and memory_snapshots:
            memory_peak = max(snapshot['peak'] for snapshot in memory_snapshots)
            memory_allocated = memory_snapshots[-1]['allocated']
            memory_cached = memory_snapshots[-1]['cached']
        else:
            memory_peak = memory_allocated = memory_cached = 0
        
        # Generate recommendations
        recommendations = []
        
        if avg_forward_time > 0.1:
            recommendations.append("Consider model quantization or pruning")
        
        if memory_peak > 2 * 1024**3:  # 2GB
            recommendations.append("Use gradient checkpointing or reduce batch size")
        
        if torch.cuda.is_available():
            gpu_util = self._estimate_gpu_utilization(prof)
            if gpu_util < 0.5:
                recommendations.append("Increase batch size or model complexity for better GPU utilization")
        else:
            gpu_util = 0.0
        
        # Save detailed profiling results
        prof.export_chrome_trace(f"{self.output_dir}/trace_{int(time.time())}.json")
        
        return PerformanceMetrics(
            forward_time=avg_forward_time,
            memory_peak=memory_peak,
            memory_allocated=memory_allocated,
            memory_cached=memory_cached,
            throughput_samples_per_second=throughput,
            gpu_utilization=gpu_util,
            recommendations=recommendations
        )
    
    def _estimate_gpu_utilization(self, profiler) -> float:
        """Estimate GPU utilization from profiler data"""
        # This is a simplified estimation
        # In production, you'd use nvidia-ml-py or similar
        try:
            key_averages = profiler.key_averages()
            cuda_time = sum(item.cuda_time_total for item in key_averages if item.cuda_time_total > 0)
            total_time = sum(item.cpu_time_total for item in key_averages)
            
            if total_time > 0:
                return min(1.0, cuda_time / total_time)
        except:
            pass
        
        return 0.5  # Default estimate


class OptimizedModel(nn.Module):
    """Model wrapper with all optimizations applied"""
    
    def __init__(self, base_model: nn.Module, optimization_config: Dict[str, Any]):
        super().__init__()
        
        self.optimization_config = optimization_config
        self.base_model = base_model
        
        # Apply optimizations
        if optimization_config.get('gradient_checkpointing', False):
            self.model = GradientCheckpointingWrapper(
                base_model, 
                checkpoint_segments=optimization_config.get('checkpoint_segments', 2)
            )
        else:
            self.model = base_model
        
        # Mixed precision
        self.mixed_precision = MixedPrecisionManager(
            enabled=optimization_config.get('mixed_precision', True)
        )
        
        # Dynamic batch sizing
        self.dynamic_batcher = DynamicBatchSizer(
            initial_batch_size=optimization_config.get('initial_batch_size', 8),
            max_batch_size=optimization_config.get('max_batch_size', 64)
        )
        
        # Profiler
        self.profiler = AdvancedProfiler()
        
        # Compilation (if available)
        if optimization_config.get('compile_model', False) and hasattr(torch, 'compile'):
            try:
                self.model = torch.compile(
                    self.model, 
                    mode=optimization_config.get('compile_mode', 'default')
                )
                logger.info("Model compiled with torch.compile")
            except Exception as e:
                logger.warning(f"Model compilation failed: {e}")
    
    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Optimized forward pass"""
        if self.training and self.mixed_precision.enabled:
            with self.mixed_precision.autocast_context_manager():
                return self.model(x)
        else:
            return self.model(x)
    
    def get_optimal_batch_size(self, sample_input: torch.Tensor) -> int:
        """Get optimal batch size for current conditions"""
        return self.dynamic_batcher.adjust_batch_size(self.model, sample_input)
    
    def profile_performance(self, sample_input: torch.Tensor) -> PerformanceMetrics:
        """Profile model performance"""
        return self.profiler.profile_model_inference(self.model, sample_input)
    
    def optimize_for_inference(self):
        """Apply inference-specific optimizations"""
        self.eval()
        
        # Disable gradient computation
        for param in self.parameters():
            param.requires_grad_(False)
        
        # Apply fusion optimizations if available
        if hasattr(torch.jit, 'optimize_for_inference'):
            try:
                scripted = torch.jit.script(self.model)
                self.model = torch.jit.optimize_for_inference(scripted)
                logger.info("Applied torch.jit inference optimizations")
            except Exception as e:
                logger.debug(f"JIT optimization failed: {e}")
    
    def export_to_onnx(self, sample_input: torch.Tensor, output_path: str):
        """Export model to ONNX format for deployment"""
        self.eval()
        
        with torch.no_grad():
            torch.onnx.export(
                self.model,
                sample_input.unsqueeze(0),
                output_path,
                input_names=['input'],
                output_names=['output'],
                dynamic_axes={
                    'input': {0: 'batch_size'},
                    'output': {0: 'batch_size'}
                },
                opset_version=11
            )
        
        logger.info(f"Model exported to ONNX: {output_path}")


class MemoryManager:
    """Advanced memory management for PyTorch operations"""
    
    @staticmethod
    @contextmanager
    def memory_context():
        """Context manager for memory operations"""
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        
        try:
            yield
        finally:
            if torch.cuda.is_available():
                torch.cuda.empty_cache()
            gc.collect()
    
    @staticmethod
    def optimize_memory_usage():
        """Apply memory optimizations"""
        if torch.cuda.is_available():
            # Enable memory optimization
            torch.backends.cudnn.benchmark = True
            torch.backends.cudnn.enabled = True
            
            # Set memory management strategy
            torch.cuda.empty_cache()
            
        # CPU optimizations
        torch.set_num_threads(torch.get_num_threads())  # Ensure proper threading
    
    @staticmethod
    def get_memory_summary() -> Dict[str, float]:
        """Get comprehensive memory summary"""
        summary = {}
        
        if torch.cuda.is_available():
            summary.update({
                'gpu_allocated_mb': torch.cuda.memory_allocated() / 1024**2,
                'gpu_cached_mb': torch.cuda.memory_reserved() / 1024**2,
                'gpu_peak_mb': torch.cuda.max_memory_allocated() / 1024**2,
            })
            
        # System memory (approximation)
        import psutil
        process = psutil.Process()
        memory_info = process.memory_info()
        summary.update({
            'cpu_rss_mb': memory_info.rss / 1024**2,
            'cpu_vms_mb': memory_info.vms / 1024**2,
        })
        
        return summary


def create_optimized_training_loop(model: nn.Module, train_loader, val_loader, 
                                 optimizer: torch.optim.Optimizer, 
                                 loss_fn: Callable, num_epochs: int = 10,
                                 optimization_config: Dict[str, Any] = None) -> Dict[str, Any]:
    """Create optimized training loop with all performance enhancements"""
    
    if optimization_config is None:
        optimization_config = {
            'mixed_precision': True,
            'gradient_checkpointing': True,
            'compile_model': True,
            'dynamic_batching': True
        }
    
    # Wrap model with optimizations
    optimized_model = OptimizedModel(model, optimization_config)
    device = next(model.parameters()).device
    optimized_model = optimized_model.to(device)
    
    # Memory optimization
    MemoryManager.optimize_memory_usage()
    
    # Training history
    history = {'train_loss': [], 'val_loss': [], 'performance_metrics': []}
    
    # Training loop
    for epoch in range(num_epochs):
        optimized_model.train()
        train_losses = []
        
        with MemoryManager.memory_context():
            for batch_idx, (batch_inputs, batch_targets) in enumerate(train_loader):
                batch_inputs = batch_inputs.to(device, non_blocking=True)
                batch_targets = batch_targets.to(device, non_blocking=True)
                
                optimizer.zero_grad()
                
                # Forward pass with mixed precision
                if optimized_model.mixed_precision.enabled:
                    with optimized_model.mixed_precision.autocast_context_manager():
                        outputs = optimized_model(batch_inputs)
                        loss = loss_fn(outputs, batch_targets)
                    
                    # Backward pass with scaling
                    optimized_model.mixed_precision.backward(loss)
                    success = optimized_model.mixed_precision.step_optimizer(optimizer)
                    
                    if not success:
                        logger.debug("Gradient step skipped due to inf/NaN")
                        continue
                        
                else:
                    outputs = optimized_model(batch_inputs)
                    loss = loss_fn(outputs, batch_targets)
                    loss.backward()
                    optimizer.step()
                
                train_losses.append(loss.item())
        
        # Validation
        optimized_model.eval()
        val_losses = []
        
        with torch.no_grad():
            for batch_inputs, batch_targets in val_loader:
                batch_inputs = batch_inputs.to(device, non_blocking=True)
                batch_targets = batch_targets.to(device, non_blocking=True)
                
                outputs = optimized_model(batch_inputs)
                loss = loss_fn(outputs, batch_targets)
                val_losses.append(loss.item())
        
        # Record metrics
        avg_train_loss = np.mean(train_losses)
        avg_val_loss = np.mean(val_losses)
        
        history['train_loss'].append(avg_train_loss)
        history['val_loss'].append(avg_val_loss)
        
        # Profile performance occasionally
        if epoch % 5 == 0:
            sample_input = next(iter(train_loader))[0][0]
            metrics = optimized_model.profile_performance(sample_input)
            history['performance_metrics'].append(metrics)
            
            logger.info(f"Epoch {epoch}: train_loss={avg_train_loss:.4f}, "
                       f"val_loss={avg_val_loss:.4f}, "
                       f"throughput={metrics.throughput_samples_per_second:.1f} samples/s")
    
    return {
        'model': optimized_model,
        'history': history,
        'final_metrics': history['performance_metrics'][-1] if history['performance_metrics'] else None,
        'memory_summary': MemoryManager.get_memory_summary()
    }


if __name__ == "__main__":
    # Test performance optimizations
    logger.info("Testing performance optimization system...")
    
    # Create dummy model and data for testing
    dummy_model = nn.Sequential(
        nn.Linear(256, 512),
        nn.ReLU(),
        nn.Linear(512, 256),
        nn.ReLU(),
        nn.Linear(256, 1)
    )
    
    sample_input = torch.randn(1, 256)
    
    # Test optimization wrapper
    optimization_config = {
        'mixed_precision': True,
        'gradient_checkpointing': False,  # Not applicable for this simple model
        'compile_model': True,
        'initial_batch_size': 8
    }
    
    optimized_model = OptimizedModel(dummy_model, optimization_config)
    
    # Profile performance
    metrics = optimized_model.profile_performance(sample_input)
    print(f"Performance metrics: {metrics}")
    
    # Test memory management
    memory_summary = MemoryManager.get_memory_summary()
    print(f"Memory summary: {memory_summary}")
    
    print("✅ Performance optimization system tested successfully")