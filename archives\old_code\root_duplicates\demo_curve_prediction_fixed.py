"""
FIXED VERSION of demo_curve_prediction.py

This version addresses all PyTorch-specific errors with advanced solutions:
1. Device mismatch errors with proper device management
2. Small dataset issues with intelligent sizing and augmentation
3. Memory/performance optimization with modern PyTorch patterns

Key improvements:
- Advanced device management with verification
- Intelligent dataset sizing with data augmentation
- Mixed precision training and gradient checkpointing
- Dynamic batch sizing and memory optimization
- Comprehensive error handling and diagnostics
"""

import sys
import os
import numpy as np
import torch
import logging
import matplotlib.pyplot as plt
from typing import Dict, List, Any
import traceback

# Add current directory to path
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# Import our optimization modules
from device_management_fixes import DeviceManager, create_device_aware_model, safe_forward_pass
from dataset_optimization_fixes import (
    IntelligentSequenceOptimizer, DataAugmentationEngine, 
    MemoryEfficientDataset, create_optimized_datasets
)
from performance_optimization_fixes import (
    OptimizedModel, MemoryManager, create_optimized_training_loop,
    AdvancedProfiler
)

# Setup enhanced logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_realistic_well_data(num_points: int = 2000) -> Dict[str, np.ndarray]:
    """Enhanced well data creation with better geological relationships"""
    logger.info(f"Creating {num_points} points of enhanced realistic well data...")
    
    # Create depth array
    depth = np.linspace(2000, 2500, num_points)
    depth_trend = (depth - 2000) / 500
    
    data = {}
    
    # Enhanced geological modeling
    # Add multiple geological layers with proper relationships
    shale_factor = 0.3 + 0.4 * np.sin(depth_trend * 6) * np.sin(depth_trend * 15)
    sand_factor = 1.0 - shale_factor
    carbonate_factor = 0.2 * np.sin(depth_trend * 3) ** 2
    
    # Gamma Ray with geological control
    gr_base = (shale_factor * np.random.uniform(80, 150, num_points) +
               sand_factor * np.random.uniform(15, 50, num_points) +
               carbonate_factor * np.random.uniform(5, 25, num_points))
    data['GR'] = np.clip(gr_base + np.random.normal(0, 8, num_points), 0, 200)
    
    # Neutron with porosity relationships
    porosity_base = 0.25 - shale_factor * 0.1 + carbonate_factor * 0.1
    cnl_base = porosity_base * 40 + shale_factor * 15
    data['CNL'] = np.clip(cnl_base + np.random.normal(0, 3, num_points), 0, 60)
    
    # Density with lithology control
    matrix_density = 2.65 - shale_factor * 0.15 + carbonate_factor * 0.05
    porosity_effect = (data['CNL'] / 40.0) * 0.65
    data['DEN'] = np.clip(matrix_density - porosity_effect + np.random.normal(0, 0.05, num_points), 1.5, 3.0)
    
    # Resistivity with hydrocarbon indicators
    water_resistivity = 0.1 + depth_trend * 0.3
    hydrocarbon_zones = np.sin(depth_trend * 10) > 0.7
    rlld_base = np.where(
        hydrocarbon_zones,
        water_resistivity * np.random.uniform(10, 100, num_points),
        water_resistivity * np.random.uniform(0.5, 5, num_points)
    )
    rlld_base = np.where(shale_factor > 0.6, rlld_base * 0.1, rlld_base)
    data['RLLD'] = np.clip(rlld_base, 0.1, 1000)
    
    # Acoustic with geological and porosity control
    matrix_transit = 55 + shale_factor * 30 - carbonate_factor * 10
    porosity_transit = (data['CNL'] / 40.0) * 50
    compaction_effect = depth_trend * 10
    data['AC'] = np.clip(
        matrix_transit + porosity_transit - compaction_effect + np.random.normal(0, 5, num_points),
        40, 400
    )
    data['VP'] = data['AC'].copy()
    
    logger.info("✓ Enhanced realistic well data created")
    for curve, values in data.items():
        logger.info(f"  {curve}: {values.min():.2f} - {values.max():.2f}")
    
    return data


def demo_vp_prediction_fixed():
    """FIXED Demo 1: VP Prediction with proper device management"""
    logger.info("\n" + "="*60)
    logger.info("🎯 FIXED DEMO 1: VP (Sonic Velocity) Prediction")
    logger.info("="*60)
    
    try:
        # Create enhanced device manager
        device_manager = DeviceManager()
        
        with device_manager.device_context():
            from vp_predictor import (
                GeneralWellLogTransformer, GeneralDataNormalizer, 
                GeneralWellLogDataset, get_model_template
            )
            
            # Create enhanced well data
            well_data = create_realistic_well_data(5000)  # Larger dataset
            
            # Augment data if needed
            if len(next(iter(well_data.values()))) < 10000:
                augmenter = DataAugmentationEngine(augmentation_factor=1.5)
                well_data = augmenter.augment_well_data(well_data)
            
            # Get model configuration
            model_config = get_model_template('vp_prediction')
            
            # Create device-aware model
            model = create_device_aware_model(
                GeneralWellLogTransformer, 'vp_prediction', device_manager
            )
            
            # Create normalizer
            normalizer = GeneralDataNormalizer(
                input_curves=['GR', 'CNL', 'DEN', 'RLLD'], 
                output_curves=['VP']
            )
            
            # Optimize sequence configuration
            optimizer = IntelligentSequenceOptimizer(min_samples=50)
            sequence_config = optimizer.optimize_configuration(well_data, target_samples=60)
            
            # Create optimized dataset
            dataset = MemoryEfficientDataset(
                data_dict=well_data,
                input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
                target_curve='VP',
                normalizer=normalizer,
                sequence_config=sequence_config,
                augment_data=False  # Already augmented
            )
            
            logger.info(f"✓ Enhanced VP prediction setup complete")
            logger.info(f"  Dataset: {len(dataset)} samples")
            logger.info(f"  Model parameters: {sum(p.numel() for p in model.parameters()):,}")
            logger.info(f"  Device: {device_manager.device}")
            
            # Test prediction with proper device handling
            sample_input, sample_target = dataset[0]
            
            # Safe forward pass
            model.eval()
            with torch.no_grad():
                prediction = safe_forward_pass(
                    model, sample_input.unsqueeze(0), device_manager
                )
            
            # Denormalize results
            pred_physical = normalizer.denormalize_predictions(prediction, ['VP'])
            target_physical = normalizer.denormalize_predictions(
                sample_target.unsqueeze(0), ['VP']
            )
            
            logger.info(f"✅ Sample prediction successful")
            logger.info(f"  Input shape: {sample_input.shape}")
            logger.info(f"  Prediction range: {pred_physical.min():.1f} - {pred_physical.max():.1f} μs/ft")
            logger.info(f"  Target range: {target_physical.min():.1f} - {target_physical.max():.1f} μs/ft")
            logger.info(f"  Memory usage: {MemoryManager.get_memory_summary()}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Enhanced VP prediction demo failed: {e}")
        traceback.print_exc()
        return False


def demo_training_simulation_fixed():
    """FIXED Demo 5: Complete training simulation with all optimizations"""
    logger.info("\n" + "="*60)
    logger.info("🎯 FIXED DEMO 5: Enhanced Training Simulation")
    logger.info("="*60)
    
    try:
        # Initialize optimizations
        device_manager = DeviceManager()
        MemoryManager.optimize_memory_usage()
        
        with device_manager.device_context():
            from vp_predictor import (
                GeneralWellLogTransformer, GeneralDataNormalizer,
                GeneralWellLogLoss, get_model_template, get_training_template
            )
            
            # Create enhanced datasets
            train_data = create_realistic_well_data(8000)  # Larger training set
            val_data = create_realistic_well_data(2000)    # Larger validation set
            
            logger.info("✓ Enhanced training and validation data created")
            
            # Get configurations
            model_config = get_model_template('vp_prediction')
            training_config = get_training_template('vp_training')
            training_config['max_epochs'] = 3  # Quick simulation
            training_config['batch_size'] = 8
            
            # Create device-aware model with optimizations
            optimization_config = {
                'mixed_precision': torch.cuda.is_available(),
                'gradient_checkpointing': True,
                'compile_model': torch.cuda.is_available(),
                'initial_batch_size': training_config['batch_size'],
                'max_batch_size': 32
            }
            
            base_model = create_device_aware_model(
                GeneralWellLogTransformer, 'vp_prediction', device_manager
            )
            optimized_model = OptimizedModel(base_model, optimization_config)
            optimized_model = device_manager.move_to_device(optimized_model)
            
            # Create normalizer
            normalizer = GeneralDataNormalizer(
                model_config['input_curves'], 
                model_config['output_curves']
            )
            
            # Create optimized datasets
            train_dataset, val_dataset = create_optimized_datasets(
                train_data, val_data,
                model_config['input_curves'], model_config['output_curves'][0],
                normalizer, min_train_samples=100, min_val_samples=30
            )
            
            logger.info(f"✓ Optimized datasets created")
            logger.info(f"  Training: {len(train_dataset)} samples")
            logger.info(f"  Validation: {len(val_dataset)} samples")
            
            # Create data loaders with dynamic batch sizing
            optimal_batch_size = optimized_model.get_optimal_batch_size(
                train_dataset[0][0]
            )
            
            train_loader = torch.utils.data.DataLoader(
                train_dataset,
                batch_size=min(optimal_batch_size, len(train_dataset)),
                shuffle=True,
                pin_memory=torch.cuda.is_available()
            )
            
            val_loader = torch.utils.data.DataLoader(
                val_dataset,
                batch_size=min(optimal_batch_size, len(val_dataset)),
                shuffle=False,
                pin_memory=torch.cuda.is_available()
            )
            
            # Create optimizer and loss function
            optimizer = torch.optim.Adam(
                optimized_model.parameters(),
                lr=training_config['learning_rate'],
                weight_decay=training_config.get('weight_decay', 1e-5)
            )
            
            loss_fn = GeneralWellLogLoss(
                target_curve='VP',
                loss_type='mse',
                constraint_weight=1.0,
                physics_constraints=True
            )
            
            # Test single batch processing
            logger.info("Testing batch processing...")
            batch_inputs, batch_targets = next(iter(train_loader))
            batch_inputs = device_manager.move_to_device(batch_inputs)
            batch_targets = device_manager.move_to_device(batch_targets)
            
            logger.info(f"✅ Batch processing successful")
            logger.info(f"  Batch shapes: inputs {batch_inputs.shape}, targets {batch_targets.shape}")
            logger.info(f"  Batch devices: inputs {batch_inputs.device}, targets {batch_targets.device}")
            logger.info(f"  Model device: {next(optimized_model.parameters()).device}")
            
            # Test forward pass
            optimized_model.eval()
            with torch.no_grad():
                predictions = optimized_model(batch_inputs)
                loss_components = loss_fn(predictions, batch_targets)
            
            logger.info(f"✅ Forward pass successful")
            logger.info(f"  Prediction shape: {predictions.shape}")
            logger.info(f"  Loss components: {list(loss_components.keys())}")
            logger.info(f"  Total loss: {loss_components['total_loss']:.4f}")
            
            # Performance profiling
            logger.info("Profiling model performance...")
            metrics = optimized_model.profile_performance(batch_inputs[0])
            
            logger.info(f"✅ Performance profiling complete")
            logger.info(f"  Forward time: {metrics.forward_time:.4f}s")
            logger.info(f"  Throughput: {metrics.throughput_samples_per_second:.1f} samples/s")
            logger.info(f"  Memory peak: {metrics.memory_peak/1024**3:.2f}GB")
            logger.info(f"  GPU utilization: {metrics.gpu_utilization:.2%}")
            
            if metrics.recommendations:
                logger.info("  Recommendations:")
                for rec in metrics.recommendations:
                    logger.info(f"    - {rec}")
            
            # Mini training loop (1 epoch demonstration)
            logger.info("Running mini training demonstration...")
            optimized_model.train()
            
            total_loss = 0
            num_batches = 0
            
            with MemoryManager.memory_context():
                for batch_inputs, batch_targets in train_loader:
                    batch_inputs = device_manager.move_to_device(batch_inputs)
                    batch_targets = device_manager.move_to_device(batch_targets)
                    
                    optimizer.zero_grad()
                    
                    # Forward pass with mixed precision
                    if optimized_model.mixed_precision.enabled:
                        with optimized_model.mixed_precision.autocast_context_manager():
                            outputs = optimized_model(batch_inputs)
                            loss_dict = loss_fn(outputs, batch_targets)
                            loss = loss_dict['total_loss']
                        
                        optimized_model.mixed_precision.backward(loss)
                        success = optimized_model.mixed_precision.step_optimizer(optimizer)
                        
                        if success:
                            total_loss += loss.item()
                            num_batches += 1
                    else:
                        outputs = optimized_model(batch_inputs)
                        loss_dict = loss_fn(outputs, batch_targets)
                        loss = loss_dict['total_loss']
                        
                        loss.backward()
                        optimizer.step()
                        
                        total_loss += loss.item()
                        num_batches += 1
                    
                    # Break after a few batches for demonstration
                    if num_batches >= 3:
                        break
            
            avg_loss = total_loss / num_batches if num_batches > 0 else 0
            
            logger.info(f"✅ Mini training complete")
            logger.info(f"  Processed {num_batches} batches")
            logger.info(f"  Average loss: {avg_loss:.4f}")
            logger.info(f"  Final memory: {MemoryManager.get_memory_summary()}")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Enhanced training simulation failed: {e}")
        traceback.print_exc()
        return False


def demo_performance_comparison():
    """Demo: Performance comparison between original and optimized approaches"""
    logger.info("\n" + "="*60)
    logger.info("🎯 PERFORMANCE COMPARISON DEMO")
    logger.info("="*60)
    
    try:
        device_manager = DeviceManager()
        
        with device_manager.device_context():
            from vp_predictor import GeneralWellLogTransformer, get_model_template
            
            # Create test data
            test_data = create_realistic_well_data(3000)
            
            # Create models
            model_config = get_model_template('vp_prediction')
            
            # Standard model
            standard_model = create_device_aware_model(
                GeneralWellLogTransformer, 'vp_prediction', device_manager
            )
            
            # Optimized model
            optimization_config = {
                'mixed_precision': torch.cuda.is_available(),
                'gradient_checkpointing': True,
                'compile_model': torch.cuda.is_available(),
            }
            optimized_model = OptimizedModel(standard_model, optimization_config)
            optimized_model = device_manager.move_to_device(optimized_model)
            
            # Test input
            sample_input = torch.randn(1, 4, 640)
            sample_input = device_manager.move_to_device(sample_input)
            
            # Profile both models
            profiler = AdvancedProfiler()
            
            logger.info("Profiling standard model...")
            standard_metrics = profiler.profile_model_inference(standard_model, sample_input[0])
            
            logger.info("Profiling optimized model...")
            optimized_metrics = profiler.profile_model_inference(optimized_model, sample_input[0])
            
            # Compare results
            logger.info(f"✅ Performance comparison complete")
            logger.info(f"Standard Model:")
            logger.info(f"  Forward time: {standard_metrics.forward_time:.4f}s")
            logger.info(f"  Throughput: {standard_metrics.throughput_samples_per_second:.1f} samples/s")
            logger.info(f"  Memory peak: {standard_metrics.memory_peak/1024**2:.1f}MB")
            
            logger.info(f"Optimized Model:")
            logger.info(f"  Forward time: {optimized_metrics.forward_time:.4f}s")
            logger.info(f"  Throughput: {optimized_metrics.throughput_samples_per_second:.1f} samples/s")
            logger.info(f"  Memory peak: {optimized_metrics.memory_peak/1024**2:.1f}MB")
            
            # Calculate improvements
            speed_improvement = standard_metrics.forward_time / optimized_metrics.forward_time
            throughput_improvement = optimized_metrics.throughput_samples_per_second / standard_metrics.throughput_samples_per_second
            memory_reduction = 1 - (optimized_metrics.memory_peak / standard_metrics.memory_peak)
            
            logger.info(f"Improvements:")
            logger.info(f"  Speed: {speed_improvement:.2f}x faster")
            logger.info(f"  Throughput: {throughput_improvement:.2f}x higher")
            logger.info(f"  Memory: {memory_reduction:.1%} reduction")
            
            return True
            
    except Exception as e:
        logger.error(f"❌ Performance comparison failed: {e}")
        traceback.print_exc()
        return False


def main():
    """Run all fixed demonstrations"""
    logger.info("🚀 FIXED General Well Log Transformer - Advanced PyTorch Demo")
    logger.info("=" * 80)
    logger.info("This demo showcases FIXED PyTorch implementations addressing:")
    logger.info("✅ Device mismatch errors with advanced device management")
    logger.info("✅ Dataset sizing issues with intelligent optimization")
    logger.info("✅ Memory/performance optimization with modern patterns")
    logger.info("=" * 80)
    
    # Enhanced demonstrations
    demos = [
        ("VP Prediction (FIXED)", demo_vp_prediction_fixed),
        ("Training Simulation (FIXED)", demo_training_simulation_fixed),
        ("Performance Comparison", demo_performance_comparison)
    ]
    
    results = {}
    for demo_name, demo_func in demos:
        logger.info(f"\n{'='*20} Starting {demo_name} {'='*20}")
        try:
            results[demo_name] = demo_func()
            status = "✅ PASSED" if results[demo_name] else "❌ FAILED"
            logger.info(f"\n{status}: {demo_name}")
        except Exception as e:
            logger.error(f"\n❌ CRASHED: {demo_name} - {e}")
            results[demo_name] = False
    
    # Final summary
    logger.info("\n" + "=" * 80)
    logger.info("📊 FIXED DEMONSTRATION SUMMARY")
    logger.info("=" * 80)
    
    passed = sum(1 for result in results.values() if result)
    total = len(results)
    
    for demo_name, result in results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"{demo_name:<40} {status}")
    
    logger.info(f"\nOverall: {passed}/{total} demonstrations passed")
    
    if passed == total:
        logger.info("\n🎉 ALL FIXED DEMONSTRATIONS SUCCESSFUL!")
        logger.info("✅ PyTorch Issues Resolved:")
        logger.info("  ✅ Device mismatch errors eliminated")
        logger.info("  ✅ Dataset sizing optimized with intelligent algorithms")
        logger.info("  ✅ Memory usage optimized with modern PyTorch patterns")
        logger.info("  ✅ Performance enhanced with mixed precision and compilation")
        logger.info("  ✅ Advanced profiling and diagnostics implemented")
        return True
    else:
        logger.warning(f"\n⚠️  {total - passed}/{total} demonstrations still failing")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)