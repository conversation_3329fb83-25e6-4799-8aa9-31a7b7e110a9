# MWLT MultiWell Launcher Guide

## 🚀 Overview

The `multiwell_launcher.py` provides a unified interface to access all major MWLT MultiWell functionality with simple commands.

## 📋 Available Commands

### Training
```bash
python multiwell_launcher.py train
```
**What it does:**
- Launches enhanced multi-curve training pipeline
- Trains 25 models (5 folds × 5 curves: GR, CNL, DEN, AC, RLLD)
- Uses 5-fold cross-validation for robust performance
- Saves results to `training/outputs/` (self-contained)

**Expected time:** 2-4 hours  
**Expected output:** 25 trained models with cross-validation results

### Validation
```bash
python multiwell_launcher.py validate
```
**What it does:**
- Launches comprehensive model validation suite
- Performs cross-validation analysis with statistical significance
- Detects overfitting using training history analysis
- Quantifies uncertainty via Monte Carlo dropout
- Generates 6-panel diagnostic plots
- Saves results to `validation/outputs/` (self-contained)

**Expected time:** 30-60 minutes
**Expected output:** Validation reports and diagnostic visualizations

### Prediction
```bash
python multiwell_launcher.py predict
```
**What it does:**
- Launches improved multi-curve prediction demo
- Uses production models (automatically selected best models)
- Makes predictions with comprehensive error analysis
- Creates prediction visualizations and performance reports
- Saves results to `prediction/outputs/` (self-contained)

**Expected time:** 10-20 minutes
**Expected output:** Prediction results with visualizations

### Demos
```bash
python multiwell_launcher.py demos
```
**What it does:**
- Launches interactive demo suite
- Provides demonstrations of all curve predictions
- Shows VP prediction (backward compatible)
- Includes training simulation capabilities

**Expected time:** Interactive (user-controlled)  
**Expected output:** Interactive demonstrations and examples

### Testing
```bash
python multiwell_launcher.py test
```
**What it does:**
- Runs quick functionality tests
- Verifies basic system functionality
- Tests import capabilities and basic operations
- Provides quick health check

**Expected time:** 1-2 minutes  
**Expected output:** Test results and system status

### Priority Check
```bash
python multiwell_launcher.py priority
```
**What it does:**
- Checks priority fixes implementation
- Verifies critical normalization fix is applied
- Tests improved model performance
- Validates system improvements

**Expected time:** 2-5 minutes  
**Expected output:** Priority fixes validation report

### Status
```bash
python multiwell_launcher.py status
```
**What it does:**
- Shows current system status
- Lists available functionality
- Displays directory structure
- Provides usage guidance

**Expected time:** Instant  
**Expected output:** System status overview

## 🔄 Recommended Workflow

### Complete Workflow (First Time)
```bash
# Step 1: Check system status
python multiwell_launcher.py status

# Step 2: Run tests to verify setup
python multiwell_launcher.py test

# Step 3: Train models (this takes the longest)
python multiwell_launcher.py train

# Step 4: Validate trained models
python multiwell_launcher.py validate

# Step 5: Make predictions
python multiwell_launcher.py predict

# Step 6: Explore demos (optional)
python multiwell_launcher.py demos
```

### Quick Workflow (After Initial Setup)
```bash
# If you already have trained models, just run:
python multiwell_launcher.py predict
python multiwell_launcher.py demos
```

### Development Workflow
```bash
# For development and testing:
python multiwell_launcher.py test
python multiwell_launcher.py priority
python multiwell_launcher.py status
```

## 📊 Expected Results by Command

### After `train`
```
multiwell/training/outputs/
├── GR_fold_0/ → GR_fold_4/     # 5 Gamma Ray models
├── CNL_fold_0/ → CNL_fold_4/   # 5 Neutron models
├── DEN_fold_0/ → DEN_fold_4/   # 5 Density models
├── AC_fold_0/ → AC_fold_4/     # 5 Acoustic models
├── RLLD_fold_0/ → RLLD_fold_4/ # 5 Resistivity models
├── enhanced_training_results.json  # Comprehensive results
├── data_validation_report.json     # Data validation
└── training_summary_report.md      # Training summary
```

### After `validate`
```
multiwell/validation/outputs/
├── comprehensive_validation_results.json  # Performance assessment
├── validation_report.md                   # Detailed validation report
├── {CURVE}_validation_results.png         # Per-curve diagnostic plots
└── uncertainty_analysis.json              # Uncertainty quantification
```

### After `predict`
```
multiwell/prediction/outputs/
├── improved_multi_curve_demo_{well}.png   # Prediction visualizations
├── prediction_results.json                # Prediction performance
├── multi_curve_comparison.png             # Cross-curve analysis
└── error_analysis.json                    # Error distribution analysis
```

## 🔧 Troubleshooting

### Common Issues

**1. Command Not Recognized**
```bash
# Make sure you're in the multiwell directory
cd multiwell/
python multiwell_launcher.py status
```

**2. Import Errors**
```bash
# Ensure you're running from the project root
cd Init_transformer/
python -m multiwell.multiwell_launcher status
```

**3. No Models Found**
```bash
# Run training first
python multiwell_launcher.py train
# Then run other commands
```

**4. Permission Errors**
```bash
# Ensure write permissions in the directory
# Check that you can create files and directories
```

### Getting Help

**View Available Commands:**
```bash
python multiwell_launcher.py --help
```

**Check System Status:**
```bash
python multiwell_launcher.py status
```

**Run Basic Tests:**
```bash
python multiwell_launcher.py test
```

## 📈 Performance Monitoring

### Training Performance
- Monitor R² scores during training
- Expected: R² > 0.5 for all curves
- Best performance: Density R² > 0.7

### Validation Performance
- Check cross-validation consistency
- Monitor overfitting indicators
- Verify uncertainty quantification

### Prediction Performance
- Review prediction vs actual plots
- Check error distribution analysis
- Validate geological consistency

## 🎯 Tips for Best Results

### Before Training
1. **Check system status** to ensure everything is ready
2. **Run tests** to verify setup
3. **Ensure sufficient disk space** (models can be large)
4. **Plan for training time** (2-4 hours for complete training)

### During Training
1. **Monitor progress** through log outputs
2. **Check intermediate results** in training outputs
3. **Don't interrupt** the training process

### After Training
1. **Run validation** to assess model quality
2. **Check prediction results** for reasonableness
3. **Explore demos** to understand capabilities

## 📚 Additional Resources

- **Quick Start Guide**: `MULTIWELL_QUICK_START_GUIDE.md`
- **Detailed Documentation**: `README.md`
- **Technical Analysis**: `BEST_MODEL_SELECTION_AND_TRANSFER.md`
- **Organization Overview**: `ORGANIZATION_SUMMARY.md`

---

**🚀 Ready to use the MultiWell system?** Start with:
```bash
python multiwell_launcher.py status
python multiwell_launcher.py test
python multiwell_launcher.py train
```
