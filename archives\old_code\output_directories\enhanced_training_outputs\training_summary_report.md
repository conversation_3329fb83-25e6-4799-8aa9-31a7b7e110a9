# Enhanced Multi-Curve Training Summary Report

**Generated**: 2025-08-24 15:46:28.627007
**Device**: cuda:0
**Cross-validation folds**: 5

## Model Performance Summary

| Target Curve | R² (Mean ± Std) | 95% Confidence Interval | Validation Loss | Success Rate |
|-------------|-----------------|------------------------|-----------------|-------------|
| AC | 0.5411 ± 0.1390 | [0.4193, 0.6629] | 2.0236 ± 0.0259 | 5/5 |
| DEN | 0.2060 ± 0.1285 | [0.0934, 0.3187] | 0.7385 ± 0.1319 | 5/5 |
| CNL | 0.7554 ± 0.0615 | [0.7015, 0.8094] | 0.2240 ± 0.0531 | 5/5 |
| GR | 0.6011 ± 0.0962 | [0.5168, 0.6854] | 0.3890 ± 0.0881 | 5/5 |
| RLLD | 0.5583 ± 0.1094 | [0.4624, 0.6543] | 0.4303 ± 0.1072 | 5/5 |

## Performance Analysis

### AC Prediction: 🟠 Moderate
- **R² Score**: 0.5411 ± 0.1390
- **Confidence Interval**: [0.4193, 0.6629]
- **Model Consistency**: 5/5 successful folds

### DEN Prediction: 🔴 Poor
- **R² Score**: 0.2060 ± 0.1285
- **Confidence Interval**: [0.0934, 0.3187]
- **Model Consistency**: 5/5 successful folds

### CNL Prediction: 🟡 Good
- **R² Score**: 0.7554 ± 0.0615
- **Confidence Interval**: [0.7015, 0.8094]
- **Model Consistency**: 5/5 successful folds

### GR Prediction: 🟡 Good
- **R² Score**: 0.6011 ± 0.0962
- **Confidence Interval**: [0.5168, 0.6854]
- **Model Consistency**: 5/5 successful folds

### RLLD Prediction: 🟠 Moderate
- **R² Score**: 0.5583 ± 0.1094
- **Confidence Interval**: [0.4624, 0.6543]
- **Model Consistency**: 5/5 successful folds

## Next Steps Recommendations

### Priority Actions for Poor Performers: DEN
1. **Increase Training Data**: Current dataset may be insufficient
2. **Feature Engineering**: Consider additional input curves or transformations
3. **Architecture Optimization**: Experiment with different model architectures
4. **Hyperparameter Tuning**: Systematic optimization of training parameters

### Production Ready Models: CNL, GR
1. **Deploy to Production**: Models show good performance and consistency
2. **Monitor Performance**: Implement real-time monitoring
3. **Collect Feedback**: Gather user feedback for continuous improvement

