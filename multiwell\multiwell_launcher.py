#!/usr/bin/env python3
"""
MWLT MultiWell Launcher Script
Quick access to all major functionality with organized structure
"""

import os
import sys
import argparse
from pathlib import Path

def run_training():
    """Launch enhanced training pipeline"""
    print("🚀 Launching Enhanced Multi-Curve Training Pipeline...")
    training_script = Path(__file__).parent / "training" / "enhanced_multi_curve_training.py"
    os.system(f'python "{training_script}"')

def run_validation():
    """Launch comprehensive validation suite"""
    print("🔍 Launching Comprehensive Model Validation Suite...")
    validation_script = Path(__file__).parent / "validation" / "model_validation_suite.py"
    os.system(f'python "{validation_script}"')

def run_prediction():
    """Launch improved prediction demo"""
    print("🎯 Launching Improved Multi-Curve Prediction Demo...")
    prediction_script = Path(__file__).parent / "prediction" / "improved_multi_curve_prediction_demo.py"
    os.system(f'python "{prediction_script}"')

def run_demos():
    """Launch interactive demo suite"""
    print("🎭 Launching Interactive Demo Suite...")
    demo_script = Path(__file__).parent / "demos" / "demo_curve_prediction.py"
    os.system(f'python "{demo_script}"')

def run_tests():
    """Run quick functionality tests"""
    print("🧪 Running Quick Functionality Tests...")
    test_script = Path(__file__).parent / "utils" / "simple_test.py"
    os.system(f'python "{test_script}"')

def run_priority_check():
    """Check priority fixes implementation"""
    print("✅ Checking Priority Fixes Implementation...")
    check_script = Path(__file__).parent / "utils" / "test_priority_fixes.py"
    os.system(f'python "{check_script}"')

def show_status():
    """Show current status and available options"""
    print("="*80)
    print("🏗️  MWLT MultiWell - Organized Codebase Status")
    print("="*80)
    print()
    
    # Check directory structure
    base_dir = Path(__file__).parent
    dirs = ['training', 'prediction', 'validation', 'utils', 'demos']

    for dir_name in dirs:
        dir_path = base_dir / dir_name
        if dir_path.exists():
            files = list(dir_path.glob("*.py"))
            outputs_dir = dir_path / "outputs"
            outputs_status = "📊" if outputs_dir.exists() else "📂"
            print(f"📁 {dir_name}/ - ✅ ({len(files)} scripts) {outputs_status}")
        else:
            print(f"📁 {dir_name}/ - ❌ Missing")
    
    print()
    print("🚀 Available Actions:")
    print("   train     - Enhanced multi-curve training → training/outputs/")
    print("   validate  - Comprehensive model validation → validation/outputs/")
    print("   predict   - Improved multi-curve prediction → prediction/outputs/")
    print("   demos     - Interactive demonstration suite")
    print("   test      - Quick functionality tests")
    print("   check     - Priority fixes validation")
    print("   status    - Show this status (current)")
    print()
    print("📋 Quick Start:")
    print("   python multiwell_launcher.py train")
    print("   python multiwell_launcher.py validate")
    print("   python multiwell_launcher.py predict")
    print()
    print("📊 Output Structure: All outputs saved within multiwell/ directory")
    print("   📁 training/outputs/   - Training results and models")
    print("   📁 validation/outputs/ - Validation reports and plots")
    print("   📁 prediction/outputs/ - Prediction visualizations")
    print()

def main():
    parser = argparse.ArgumentParser(
        description="MWLT MultiWell Launcher - Quick access to organized functionality",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python multiwell_launcher.py train     # Start enhanced training
  python multiwell_launcher.py validate  # Run validation suite  
  python multiwell_launcher.py predict   # Make predictions
  python multiwell_launcher.py demos     # Interactive demos
  python multiwell_launcher.py test      # Quick tests
  python multiwell_launcher.py check     # Check priority fixes
  python multiwell_launcher.py status    # Show status
        """
    )
    
    parser.add_argument(
        'action',
        choices=['train', 'validate', 'predict', 'demos', 'test', 'check', 'status'],
        help='Action to perform'
    )
    
    if len(sys.argv) == 1:
        # No arguments provided, show status
        show_status()
        return
    
    args = parser.parse_args()
    
    actions = {
        'train': run_training,
        'validate': run_validation,
        'predict': run_prediction,
        'demos': run_demos,
        'test': run_tests,
        'check': run_priority_check,
        'status': show_status
    }
    
    actions[args.action]()

if __name__ == "__main__":
    main()
