"""
Comprehensive curve configuration definitions for General Well Log Transformer

This replaces hardcoded curve parameters with flexible, configurable definitions
that support arbitrary input/output curve combinations.
"""

CURVE_CONFIGURATIONS = {
    # Petrophysical curve definitions with physics-based parameters
    'GR': {
        'name': 'Gamma Ray',
        'unit': 'API',
        'type': 'input',  # Can be 'input', 'output', or 'both'
        'physics_range': (0, 200),
        'normalization': {
            'method': 'zscore', 
            'mean': 75.0, 
            'std': 50.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'none',
        'description': 'Gamma ray log - shale content indicator'
    },
    
    'AC': {
        'name': 'Acoustic/Sonic',
        'unit': 'μs/ft', 
        'type': 'both',  # Can be input or output
        'physics_range': (40, 400),
        'normalization': {
            'method': 'zscore', 
            'mean': 200.0, 
            'std': 75.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'none',  # No artificial constraints - let model learn natural range
        'description': 'Acoustic/sonic log - travel time, porosity/lithology indicator'
    },
    
    'VP': {  # Alias for AC for backward compatibility
        'name': 'Sonic Velocity', 
        'unit': 'μs/ft',
        'type': 'output',
        'physics_range': (40, 400),
        'normalization': {
            'method': 'zscore',
            'mean': 200.0,
            'std': 75.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'none',
        'description': 'Sonic velocity prediction (Vp) - backward compatibility alias for AC'
    },
    
    'DEN': {
        'name': 'Bulk Density',
        'unit': 'g/cm³',
        'type': 'both',  # Can be input or output
        'physics_range': (1.5, 3.0),
        'normalization': {
            'method': 'zscore', 
            'mean': 2.5, 
            'std': 0.3,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'sigmoid',  # Density benefits from bounded output
        'description': 'Bulk density log - lithology/porosity indicator'
    },
    
    'CNL': {
        'name': 'Neutron Porosity',
        'unit': '%', 
        'type': 'both',
        'physics_range': (0, 60),
        'normalization': {
            'method': 'zscore',
            'mean': 20.0,
            'std': 15.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'relu',  # Non-negative porosity values
        'description': 'Neutron log - hydrogen content, porosity indicator'
    },
    
    'NPHI': {  # Alias for CNL
        'name': 'Neutron Porosity',
        'unit': 'v/v',
        'type': 'both',
        'physics_range': (0, 0.6),  # Fraction units
        'normalization': {
            'method': 'zscore',
            'mean': 0.2,
            'std': 0.15,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'relu',
        'description': 'Neutron porosity in volume fraction units'
    },
    
    'RLLD': {
        'name': 'Deep Laterolog Resistivity', 
        'unit': 'ohm-m',
        'type': 'input',  # Typically input curve
        'physics_range': (0.1, 1000),
        'normalization': {
            'method': 'log_zscore',
            'log_base': 10,
            'mean': 1.0,  # Mean of log10 values
            'std': 2.0,   # Std of log10 values
            'clip_range': (-3, 3)
        },
        'preprocessing': 'log10',
        'activation': 'none',
        'description': 'Deep resistivity log - fluid saturation indicator'
    },
    
    'RILD': {  # Alias for RLLD
        'name': 'Deep Induction Resistivity',
        'unit': 'ohm-m',
        'type': 'input',
        'physics_range': (0.1, 1000),
        'normalization': {
            'method': 'log_zscore',
            'log_base': 10,
            'mean': 1.0,
            'std': 2.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': 'log10',
        'activation': 'none',
        'description': 'Deep induction resistivity log'
    },
    
    'PE': {
        'name': 'Photoelectric Factor',
        'unit': 'b/e',
        'type': 'both',
        'physics_range': (1.0, 10.0),
        'normalization': {
            'method': 'zscore',
            'mean': 3.0,
            'std': 2.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'relu',  # Non-negative values
        'description': 'Photoelectric factor - lithology indicator'
    },
    
    'SP': {
        'name': 'Spontaneous Potential',
        'unit': 'mV',
        'type': 'input',
        'physics_range': (-200, 50),
        'normalization': {
            'method': 'zscore',
            'mean': -50.0,
            'std': 75.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'none',
        'description': 'Spontaneous potential log - formation water salinity indicator'
    },
    
    'CALI': {
        'name': 'Caliper',
        'unit': 'in',
        'type': 'input',
        'physics_range': (6, 20),
        'normalization': {
            'method': 'zscore',
            'mean': 8.5,
            'std': 2.0,
            'clip_range': (-3, 3)
        },
        'preprocessing': None,
        'activation': 'relu',
        'description': 'Caliper log - borehole diameter measurement'
    }
}

# Activation function mapping for easy lookup
ACTIVATION_FUNCTIONS = {
    'none': None,
    'sigmoid': 'sigmoid',
    'relu': 'relu', 
    'tanh': 'tanh',
    'softplus': 'softplus'
}

# Normalization method mapping
NORMALIZATION_METHODS = {
    'zscore': 'z_score_normalization',
    'minmax': 'min_max_normalization',
    'log_zscore': 'log_zscore_normalization',
    'robust': 'robust_normalization'
}

def get_curve_config(curve_name):
    """
    Get configuration for a specific curve
    
    Args:
        curve_name: Name of the curve (e.g., 'GR', 'AC', 'DEN')
        
    Returns:
        dict: Curve configuration
        
    Raises:
        ValueError: If curve_name not found
    """
    curve_name = curve_name.upper()
    if curve_name not in CURVE_CONFIGURATIONS:
        raise ValueError(f"Curve '{curve_name}' not found in configurations. "
                        f"Available curves: {list(CURVE_CONFIGURATIONS.keys())}")
    
    return CURVE_CONFIGURATIONS[curve_name].copy()

def get_supported_curves():
    """Get list of all supported curve names"""
    return list(CURVE_CONFIGURATIONS.keys())

def get_curves_by_type(curve_type):
    """
    Get curves that can be used as specified type
    
    Args:
        curve_type: 'input', 'output', or 'both'
        
    Returns:
        list: Curve names matching the type
    """
    if curve_type == 'all':
        return get_supported_curves()
    
    return [name for name, config in CURVE_CONFIGURATIONS.items() 
            if config['type'] == curve_type or config['type'] == 'both']

def validate_curve_combination(input_curves, output_curves):
    """
    Validate that curve combination is physically meaningful
    
    Args:
        input_curves: List of input curve names
        output_curves: List of output curve names
        
    Returns:
        tuple: (is_valid, validation_messages)
    """
    messages = []
    is_valid = True
    
    # Check all curves are supported
    all_curves = input_curves + output_curves
    for curve in all_curves:
        if curve.upper() not in CURVE_CONFIGURATIONS:
            messages.append(f"Unsupported curve: {curve}")
            is_valid = False
    
    # Check input/output type compatibility
    for curve in input_curves:
        config = get_curve_config(curve)
        if config['type'] == 'output':
            messages.append(f"Warning: Curve {curve} is typically an output, not input")
    
    for curve in output_curves:
        config = get_curve_config(curve)
        if config['type'] == 'input':
            messages.append(f"Warning: Curve {curve} is typically an input, not output")
    
    # Check for duplicate curves
    if len(set(all_curves)) != len(all_curves):
        duplicates = [x for x in all_curves if all_curves.count(x) > 1]
        messages.append(f"Duplicate curves found: {duplicates}")
        is_valid = False
    
    # Geological validity checks
    if 'AC' in output_curves and 'VP' in output_curves:
        messages.append("Warning: AC and VP are aliases - don't use both")
    
    if 'CNL' in output_curves and 'NPHI' in output_curves:
        messages.append("Warning: CNL and NPHI are similar - consider using only one")
    
    return is_valid, messages