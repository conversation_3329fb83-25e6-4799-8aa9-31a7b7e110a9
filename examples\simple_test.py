"""
Simple test script to verify the VpTransformer implementation
"""
import sys
import os

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '.'))

def test_imports():
    """Test that all necessary modules can be imported"""
    try:
        from vp_predictor import create_improved_vp_data, VpDataNormalizer, MWLT_Vp_Base, VpDataset, VpLoss
        from vp_predictor.utils import get_device, save_checkpoint
        from sklearn.model_selection import train_test_split
        import torch
        import numpy as np
        import h5py
        
        print("✅ All imports successful!")
        return True
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_model_creation():
    """Test that we can create a model"""
    try:
        from vp_predictor import MWLT_Vp_Base
        from vp_predictor.utils import get_device
        
        device = get_device()
        model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(device)
        
        print(f"✅ Model created successfully!")
        print(f"   Model type: {type(model).__name__}")
        print(f"   Device: {device}")
        return True
    except Exception as e:
        print(f"❌ Model creation error: {e}")
        return False

def test_data_loading():
    """Test that we can load data"""
    try:
        from vp_predictor import create_improved_vp_data
        
        print("🔧 Testing data loading...")
        input_data, target_data = create_improved_vp_data()
        
        if len(input_data) > 0:
            print(f"✅ Data loading successful!")
            print(f"   Input data shape: {input_data.shape}")
            print(f"   Target data shape: {target_data.shape}")
            print(f"   Number of samples: {len(input_data)}")
            return True
        else:
            print("⚠️  No data loaded (this might be expected if data files are missing)")
            return False
    except Exception as e:
        print(f"❌ Data loading error: {e}")
        return False

def main():
    print("🔧 VpTransformer Implementation Test")
    print("=" * 50)
    
    # Test 1: Imports
    print("\n1. Testing imports...")
    imports_ok = test_imports()
    
    # Test 2: Model creation
    print("\n2. Testing model creation...")
    model_ok = test_model_creation()
    
    # Test 3: Data loading
    print("\n3. Testing data loading...")
    data_ok = test_data_loading()
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 TEST SUMMARY")
    print("=" * 50)
    print(f"Imports: {'✅ PASS' if imports_ok else '❌ FAIL'}")
    print(f"Model Creation: {'✅ PASS' if model_ok else '❌ FAIL'}")
    print(f"Data Loading: {'✅ PASS' if data_ok else '❌ FAIL'}")
    
    if imports_ok and model_ok:
        print("\n🎉 Core functionality is working!")
        print("You can now proceed with training or inference.")
    else:
        print("\n❌ Some core functionality is not working.")
        print("Please check the error messages above.")

if __name__ == "__main__":
    main()