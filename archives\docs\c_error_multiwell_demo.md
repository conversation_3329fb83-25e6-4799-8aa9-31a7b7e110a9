sers/mutia/imputeML/Scripts/python.exe "d:/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT/Init_transformer/multiwell/demos/demo_curve_prediction.py"
INFO:__main__:🚀 General Well Log Transformer - Phase 2 Demonstration
INFO:__main__:================================================================================
INFO:__main__:This demo showcases the flexible prediction capabilities
INFO:__main__:Ready to advance to Phase 3: API Development
INFO:__main__:================================================================================
INFO:__main__:
==================== Starting VP Prediction (Backward Compatible) ====================
INFO:__main__:
============================================================
INFO:__main__:🎯 DEMO 1: VP (Sonic Velocity) Prediction
INFO:__main__:============================================================
INFO:__main__:Configuration: Standard VP prediction from GR, CNL, DEN, RLLD
INFO:__main__:Expected: Backward compatible with VpTransformer
INFO:__main__:Creating 3000 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 172.6 API
INFO:__main__:  CNL range: 19.4 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.38 g/cm³
INFO:__main__:  RLLD range: 0.1 - 1000.0 ohm-m
INFO:__main__:  VP range: 72.0 - 155.0 μs/ft
INFO:__main__:Model config: {'name': 'Sonic Velocity Prediction', 'description': 'Predict sonic velocity (Vp) from conventional logs - backward compatible', 'input_curves': ['GR', 'CNL', 'DEN', 'RLLD'], 'output_curves': ['VP'], 'model_config': {'architecture': 'transformer', 'model_size': 'base', 'res_blocks': 4, 'encoder_layers': 4, 'attention_heads': 4, 'feature_dim': 64, 'sequence_length': 640}, 'training_config': {'optimizer': 'adam', 'learning_rate': 0.0001, 'batch_size': 8, 'max_epochs': 200, 'early_stopping_patience': 50, 'loss_weights': {'VP': 1.0}}, 'data_config': {'validation_split': 0.2, 'augmentation': True, 'total_sequence_length': 720, 'effective_sequence_length': 640}, 'compatibility': {'vp_transformer': True, 'legacy_api': True}}
INFO:vp_predictor.core.dataset:Preparing dataset windows: min_length=3000, total_seqlen=720, step_size=360, num_samples=7, est_mem=0.10 MB
INFO:vp_predictor.core.dataset:Data quality: 100.00% valid data
INFO:vp_predictor.core.dataset:GeneralWellLogDataset initialized:
INFO:vp_predictor.core.dataset:  Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
INFO:vp_predictor.core.dataset:  Target curve: VP
INFO:vp_predictor.core.dataset:  Samples: 7
INFO:vp_predictor.core.dataset:  Sequence: 720 -> 640 (step=360)
INFO:__main__:✓ VP prediction setup complete
INFO:__main__:  Dataset: 7 samples
INFO:__main__:  Model parameters: 949,410
INFO:__main__:✓ Sample prediction complete
INFO:__main__:  Input shape: torch.Size([4, 640])
INFO:__main__:  Predicted VP range: 40.0 - 243.0 μs/ft
INFO:__main__:  Target VP range: 40.0 - 40.0 μs/ft
INFO:__main__:
✅ PASSED: VP Prediction (Backward Compatible)
INFO:__main__:
==================== Starting Density Prediction ====================
INFO:__main__:
============================================================
INFO:__main__:🎯 DEMO 2: Density Prediction
INFO:__main__:============================================================
INFO:__main__:Configuration: Density prediction from GR, CNL, AC, RLLD
INFO:__main__:Expected: Physics constraints, different loss function
INFO:__main__:Creating 2500 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 176.7 API
INFO:__main__:  CNL range: 19.0 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.37 g/cm³
INFO:__main__:  RLLD range: 0.1 - 1000.0 ohm-m
INFO:__main__:  VP range: 63.6 - 160.2 μs/ft
INFO:__main__:Training config target: DEN
INFO:vp_predictor.core.dataset:Preparing dataset windows: min_length=2500, total_seqlen=720, step_size=360, num_samples=5, est_mem=0.07 MB
INFO:vp_predictor.core.dataset:Data quality: 100.00% valid data
INFO:vp_predictor.core.dataset:GeneralWellLogDataset initialized:
INFO:vp_predictor.core.dataset:  Input curves: ['GR', 'CNL', 'AC', 'RLLD']
INFO:vp_predictor.core.dataset:  Target curve: DEN
INFO:vp_predictor.core.dataset:  Samples: 5
INFO:vp_predictor.core.dataset:  Sequence: 720 -> 640 (step=360)
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'DEN' with mse loss        
INFO:vp_predictor.core.loss_functions:  Physics range: (1.5, 3.0)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.5
INFO:__main__:✓ Density prediction setup complete
INFO:__main__:  Dataset: 5 samples
INFO:__main__:  Physics range: (1.5, 3.0)
ERROR:__main__:❌ Density prediction demo failed: Index 10 out of range for dataset of size 5        
INFO:__main__:
❌ FAILED: Density Prediction
INFO:__main__:
==================== Starting Neutron Prediction (Robust) ====================
INFO:__main__:
============================================================
INFO:__main__:🎯 DEMO 3: Neutron Porosity Prediction
INFO:__main__:============================================================
INFO:__main__:Configuration: CNL prediction from GR, DEN, AC with robust loss
INFO:__main__:Expected: Huber loss for outlier resistance
INFO:__main__:Creating 2000 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 179.1 API
INFO:__main__:  CNL range: 18.8 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.39 g/cm³
INFO:__main__:  RLLD range: 0.1 - 1000.0 ohm-m
INFO:__main__:  VP range: 71.4 - 157.0 μs/ft
INFO:__main__:Added 50 neutron outliers for robust testing
INFO:__main__:Loss type: curve_specific
INFO:vp_predictor.core.dataset:Preparing dataset windows: min_length=2000, total_seqlen=720, step_size=360, num_samples=4, est_mem=0.04 MB
INFO:vp_predictor.core.dataset:Data quality: 100.00% valid data
INFO:vp_predictor.core.dataset:GeneralWellLogDataset initialized:
INFO:vp_predictor.core.dataset:  Input curves: ['GR', 'DEN', 'AC']
INFO:vp_predictor.core.dataset:  Target curve: CNL
INFO:vp_predictor.core.dataset:  Samples: 4
INFO:vp_predictor.core.dataset:  Sequence: 720 -> 640 (step=360)
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'CNL' with huber loss      
INFO:vp_predictor.core.loss_functions:  Physics range: (0, 60)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.8
INFO:__main__:✓ Neutron prediction setup complete
INFO:__main__:  Dataset: 4 samples
INFO:__main__:  Loss info: {'target_curve': 'CNL', 'loss_type': 'huber', 'constraint_weight': 0.8, 'physics_constraints': True, 'physics_range': (0, 60), 'normalize_loss': True, 'robust_loss_delta': 1.5}
ERROR:__main__:❌ Neutron prediction demo failed: Index 5 out of range for dataset of size 4
INFO:__main__:
❌ FAILED: Neutron Prediction (Robust)
INFO:__main__:
==================== Starting Multi-Curve Comparison ====================
INFO:__main__:
============================================================
INFO:__main__:🎯 DEMO 4: Multi-Curve Comparison
INFO:__main__:============================================================
INFO:__main__:Configuration: Compare training setups for 5 different curves
INFO:__main__:Expected: Different configurations, loss functions, physics constraints
INFO:__main__:Creating 1500 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 178.2 API
INFO:__main__:  CNL range: 17.8 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.34 g/cm³
INFO:__main__:  RLLD range: 0.1 - 1000.0 ohm-m
INFO:__main__:  VP range: 70.5 - 156.0 μs/ft
INFO:__main__:
--- Testing AC prediction setup ---
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'AC' with mse loss
INFO:vp_predictor.core.loss_functions:  Physics range: (40, 400)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 1.0
INFO:__main__:  ✓ AC: 4 inputs → 1 output
INFO:__main__:    Physics range: (40, 400)
INFO:__main__:    Loss: mse (constraint: 1.0)
INFO:__main__:
--- Testing DEN prediction setup ---
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'DEN' with mse loss        
INFO:vp_predictor.core.loss_functions:  Physics range: (1.5, 3.0)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.5
INFO:__main__:  ✓ DEN: 4 inputs → 1 output
INFO:__main__:    Physics range: (1.5, 3.0)
INFO:__main__:    Loss: mse (constraint: 0.5)
INFO:__main__:
--- Testing CNL prediction setup ---
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'CNL' with huber loss      
INFO:vp_predictor.core.loss_functions:  Physics range: (0, 60)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.8
INFO:__main__:  ✓ CNL: 4 inputs → 1 output
INFO:__main__:    Physics range: (0, 60)
INFO:__main__:    Loss: huber (constraint: 0.8)
INFO:__main__:
--- Testing GR prediction setup ---
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'GR' with mse loss
INFO:vp_predictor.core.loss_functions:  Physics range: (0, 200)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.3
INFO:__main__:  ✓ GR: 4 inputs → 1 output
INFO:__main__:    Physics range: (0, 200)
INFO:__main__:    Loss: mse (constraint: 0.3)
INFO:__main__:
--- Testing RLLD prediction setup ---
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'RLLD' with mae loss       
INFO:vp_predictor.core.loss_functions:  Physics range: (0.1, 1000)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 0.2
INFO:__main__:  ✓ RLLD: 4 inputs → 1 output
INFO:__main__:    Physics range: (0.1, 1000)
INFO:__main__:    Loss: mae (constraint: 0.2)
INFO:__main__:
✓ Multi-curve comparison complete
INFO:__main__:  Successful configurations: 5/5
INFO:__main__:  Supported curves: AC, DEN, CNL, GR, RLLD
INFO:__main__:
✅ PASSED: Multi-Curve Comparison
INFO:__main__:
==================== Starting Training Simulation ====================
INFO:__main__:
============================================================
INFO:__main__:🎯 DEMO 5: Training Process Simulation
INFO:__main__:============================================================
INFO:__main__:Configuration: Simulate complete training workflow
INFO:__main__:Expected: Dataset, model, trainer, loss computation, validation
INFO:__main__:Creating 4000 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 168.6 API
INFO:__main__:  CNL range: 19.6 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.38 g/cm³
INFO:__main__:  RLLD range: 0.1 - 1000.0 ohm-m
INFO:__main__:  VP range: 68.9 - 157.8 μs/ft
INFO:__main__:Creating 1000 points of realistic synthetic well data...
INFO:__main__:✓ Realistic well data created with geological relationships
INFO:__main__:  GR range: 0.0 - 169.5 API
INFO:__main__:  CNL range: 20.9 - 60.0 %
INFO:__main__:  DEN range: 1.50 - 2.46 g/cm³
INFO:__main__:  RLLD range: 0.2 - 1000.0 ohm-m
INFO:__main__:  VP range: 70.9 - 153.2 μs/ft
INFO:__main__:✓ Training and validation data created
INFO:vp_predictor.core.dataset:Preparing dataset windows: min_length=4000, total_seqlen=480, step_size=240, num_samples=15, est_mem=0.14 MB
INFO:vp_predictor.core.dataset:Windowing progress: 0% (0/15)
INFO:vp_predictor.core.dataset:Windowing progress: 7% (1/15)
INFO:vp_predictor.core.dataset:Windowing progress: 13% (2/15)
INFO:vp_predictor.core.dataset:Windowing progress: 20% (3/15)
INFO:vp_predictor.core.dataset:Windowing progress: 27% (4/15)
INFO:vp_predictor.core.dataset:Windowing progress: 33% (5/15)
INFO:vp_predictor.core.dataset:Windowing progress: 40% (6/15)
INFO:vp_predictor.core.dataset:Windowing progress: 47% (7/15)
INFO:vp_predictor.core.dataset:Windowing progress: 53% (8/15)
INFO:vp_predictor.core.dataset:Windowing progress: 60% (9/15)
INFO:vp_predictor.core.dataset:Windowing progress: 67% (10/15)
INFO:vp_predictor.core.dataset:Windowing progress: 73% (11/15)
INFO:vp_predictor.core.dataset:Windowing progress: 80% (12/15)
INFO:vp_predictor.core.dataset:Windowing progress: 87% (13/15)
INFO:vp_predictor.core.dataset:Windowing progress: 93% (14/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 0% (0/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 7% (1/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 13% (2/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 20% (3/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 27% (4/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 33% (5/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 40% (6/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 47% (7/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 53% (8/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 60% (9/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 67% (10/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 73% (11/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 80% (12/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 87% (13/15)
INFO:vp_predictor.core.dataset:Target windowing progress: 93% (14/15)
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 0%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 7%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 13%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 20%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 27%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 33%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 40%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 47%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 53%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 60%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 67%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 73%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 80%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 87%
INFO:vp_predictor.core.dataset:Normalization (inputs:GR) progress: 93%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 0%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 7%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 13%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 20%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 27%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 33%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 40%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 47%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 53%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 60%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 67%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 73%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 80%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 87%
INFO:vp_predictor.core.dataset:Normalization (inputs:CNL) progress: 93%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 0%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 7%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 13%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 20%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 27%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 33%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 40%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 47%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 53%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 60%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 67%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 73%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 80%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 87%
INFO:vp_predictor.core.dataset:Normalization (inputs:DEN) progress: 93%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 0%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 7%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 13%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 20%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 27%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 33%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 40%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 47%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 53%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 60%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 67%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 73%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 80%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 87%
INFO:vp_predictor.core.dataset:Normalization (inputs:RLLD) progress: 93%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 0%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 7%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 13%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 20%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 27%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 33%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 40%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 47%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 53%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 60%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 67%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 73%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 80%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 87%
INFO:vp_predictor.core.dataset:Normalization (target:VP) progress: 93%
INFO:vp_predictor.core.dataset:Data quality: 100.00% valid data
INFO:vp_predictor.core.dataset:GeneralWellLogDataset initialized:
INFO:vp_predictor.core.dataset:  Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
INFO:vp_predictor.core.dataset:  Target curve: VP
INFO:vp_predictor.core.dataset:  Samples: 15
INFO:vp_predictor.core.dataset:  Sequence: 480 -> 400 (step=240)
INFO:vp_predictor.core.dataset:Preparing dataset windows: min_length=1000, total_seqlen=480, step_size=240, num_samples=3, est_mem=0.03 MB
INFO:vp_predictor.core.dataset:Data quality: 100.00% valid data
INFO:vp_predictor.core.dataset:GeneralWellLogDataset initialized:
INFO:vp_predictor.core.dataset:  Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
INFO:vp_predictor.core.dataset:  Target curve: VP
INFO:vp_predictor.core.dataset:  Samples: 3
INFO:vp_predictor.core.dataset:  Sequence: 480 -> 400 (step=240)
CUDA is available. Using GPU: cuda:0
GPU Name: NVIDIA GeForce RTX 2070 Super
GPU Memory: 8.0 GB
INFO:vp_predictor.core.loss_functions:GeneralWellLogLoss initialized for 'VP' with mse loss
INFO:vp_predictor.core.loss_functions:  Physics range: (40, 400)
INFO:vp_predictor.core.loss_functions:  Constraint weight: 1.0
INFO:vp_predictor.core.training:GeneralTrainingManager initialized for 'VP' prediction
INFO:vp_predictor.core.training:  Model: {'total_parameters': 949410, 'trainable_parameters': 949410, 'model_class': 'GeneralWellLogTransformer'}
INFO:vp_predictor.core.training:  Training samples: 15
INFO:vp_predictor.core.training:  Validation samples: 3
INFO:vp_predictor.core.training:  Device: cuda:0
INFO:__main__:✓ Complete training pipeline created
INFO:__main__:  Model: GeneralWellLogTransformer
INFO:__main__:  Parameters: 949,410
INFO:__main__:  Training samples: 15
INFO:__main__:  Validation samples: 3
INFO:__main__:  Target curve: VP
INFO:__main__:  Device: cuda:0
INFO:__main__:  Batch shapes: inputs torch.Size([4, 4, 400]), targets torch.Size([4, 1, 400])       
ERROR:__main__:❌ Training simulation failed: Input type (torch.FloatTensor) and weight type (torch.c
uda.FloatTensor) should be the same or input should be a MKLDNN tensor and weight is a dense tensor 
Traceback (most recent call last):
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\demos\demo_curve_prediction.py", line 483, in demo_training_simulation
    predictions = model(batch_inputs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\core\transformer.py", line 151, in forward
    x = self.feature_embedding(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "d:\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\vp_predictor\model.py", line 47, in forward
    x = self.conv1(x)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\container.py", line 250, in forward
    input = module(input)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1736, in _wrapped_call_impl
    return self._call_impl(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\module.py", line 1747, in _call_impl
    return forward_call(*args, **kwargs)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 375, in forward   
    return self._conv_forward(input, self.weight, self.bias)
  File "C:\Users\<USER>\imputeML\lib\site-packages\torch\nn\modules\conv.py", line 370, in _conv_forward
    return F.conv1d(
RuntimeError: Input type (torch.FloatTensor) and weight type (torch.cuda.FloatTensor) should be the 
same or input should be a MKLDNN tensor and weight is a dense tensor
INFO:__main__:
❌ FAILED: Training Simulation
INFO:__main__:
================================================================================
INFO:__main__:📊 DEMONSTRATION SUMMARY
INFO:__main__:================================================================================      
INFO:__main__:VP Prediction (Backward Compatible) ✅ PASS
INFO:__main__:Density Prediction                  ❌ FAIL
INFO:__main__:Neutron Prediction (Robust)         ❌ FAIL
INFO:__main__:Multi-Curve Comparison              ✅ PASS
INFO:__main__:Training Simulation                 ❌ FAIL
INFO:__main__:
Overall: 2/5 demonstrations passed
WARNING:__main__:
⚠️  3/5 demonstrations failed
INFO:__main__:Some functionality may need refinement before Phase 3