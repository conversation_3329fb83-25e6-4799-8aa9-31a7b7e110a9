PS C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer> & C:
/Users/<USER>/imputeML/Scripts/Activate.ps1
(imputeML) PS C:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transf
ormer> & C:/Users/<USER>/imputeML/Scripts/python.exe "c:/Users/<USER>/Documents/OneDrive - PT Pertam
ina (Persero)/13_Python_PKB/4_GIT/MWLT/Init_transformer/multiwell/validation/model_validation_suite.py"
CUDA is available. Using GPU: cuda:0
GPU Name: Quadro P5000
GPU Memory: 16.0 GB
2025-08-25 17:12:53,016 - INFO - � Model Validator Initialized
2025-08-25 17:12:53,017 - INFO - � Device: cuda:0 (consistent with training)
2025-08-25 17:12:53,018 - INFO - � Starting Comprehensive Model Validation
2025-08-25 17:12:53,018 - INFO - � Loading data...
2025-08-25 17:12:53,019 - INFO -    � Loading same HDF5 data as training...
2025-08-25 17:12:53,020 - INFO -    ✅ Found data files in: C:\Users\<USER>\Documents\OneDrive - PT Pertamina
(Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer
2025-08-25 17:12:53,021 - INFO -    � Processing A1.hdf5...
Processing A1.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5120,), range [165.99, 357.24]
  CNL: (5120,), range [4.37, 35.62]
  DEN: (5120,), range [2.00, 2.77]
  DEPTH: (5120,), range [1805.75, 2450.00]
  GR: (5120,), range [20.31, 149.43]
  RLLD: (5120,), range [0.84, 3.83]
2025-08-25 17:12:53,027 - INFO -    � Creating 13 windows from 5120 samples
2025-08-25 17:12:53,032 - INFO -    � Processing A2.hdf5...
Processing A2.hdf5
Available curves: ['AC', 'CNL', 'DEN', 'DEPTH', 'GR', 'RLLD']
  AC: (5194,), range [151.63, 357.28]
  CNL: (5194,), range [0.11, 55.20]
  DEN: (5194,), range [2.00, 2.86]
  DEPTH: (5194,), range [2653.38, 3313.88]
  GR: (5194,), range [22.42, 194.46]
  RLLD: (5194,), range [0.70, 3.84]
2025-08-25 17:12:53,038 - INFO -    � Creating 13 windows from 5194 samples
2025-08-25 17:12:53,051 - INFO -    ✅ Loaded real data with 18720 total samples
2025-08-25 17:12:53,052 - INFO -    ✅ Loaded same HDF5 data as training
2025-08-25 17:12:53,052 - INFO - � Loading trained models...
2025-08-25 17:12:53,053 - INFO - Checking for models in: enhanced_training_outputs
2025-08-25 17:12:53,057 - INFO -    ✅ Found AC fold 0 model
2025-08-25 17:12:53,059 - INFO -    ✅ Found AC fold 1 model
2025-08-25 17:12:53,061 - INFO -    ✅ Found AC fold 2 model
2025-08-25 17:12:53,068 - INFO -    ✅ Found AC fold 3 model
2025-08-25 17:12:53,070 - INFO -    ✅ Found AC fold 4 model
2025-08-25 17:12:53,072 - INFO -    ✅ Found CNL fold 0 model
2025-08-25 17:12:53,073 - INFO -    ✅ Found CNL fold 1 model
2025-08-25 17:12:53,074 - INFO -    ✅ Found CNL fold 2 model
2025-08-25 17:12:53,075 - INFO -    ✅ Found CNL fold 3 model
2025-08-25 17:12:53,077 - INFO -    ✅ Found CNL fold 4 model
2025-08-25 17:12:53,078 - INFO -    ✅ Found DEN fold 0 model
2025-08-25 17:12:53,079 - INFO -    ✅ Found DEN fold 1 model
2025-08-25 17:12:53,082 - INFO -    ✅ Found DEN fold 2 model
2025-08-25 17:12:53,083 - INFO -    ✅ Found DEN fold 3 model
2025-08-25 17:12:53,084 - INFO -    ✅ Found DEN fold 4 model
2025-08-25 17:12:53,086 - INFO -    ✅ Found GR fold 0 model
2025-08-25 17:12:53,087 - INFO -    ✅ Found GR fold 1 model
2025-08-25 17:12:53,090 - INFO -    ✅ Found GR fold 2 model
2025-08-25 17:12:53,092 - INFO -    ✅ Found GR fold 3 model
2025-08-25 17:12:53,093 - INFO -    ✅ Found GR fold 4 model
2025-08-25 17:12:53,094 - INFO -    ✅ Found RLLD fold 0 model
2025-08-25 17:12:53,096 - INFO -    ✅ Found RLLD fold 1 model
2025-08-25 17:12:53,097 - INFO -    ✅ Found RLLD fold 2 model
2025-08-25 17:12:53,099 - INFO -    ✅ Found RLLD fold 3 model
2025-08-25 17:12:53,100 - INFO -    ✅ Found RLLD fold 4 model
2025-08-25 17:12:53,101 - INFO - Checking for models in: enhanced_training_outputs
2025-08-25 17:12:53,104 - INFO -    ✅ Found AC fold 0 model
2025-08-25 17:12:53,105 - INFO -    ✅ Found AC fold 1 model
2025-08-25 17:12:53,107 - INFO -    ✅ Found AC fold 2 model
2025-08-25 17:12:53,108 - INFO -    ✅ Found AC fold 3 model
2025-08-25 17:12:53,109 - INFO -    ✅ Found AC fold 4 model
2025-08-25 17:12:53,111 - INFO -    ✅ Found CNL fold 0 model
2025-08-25 17:12:53,113 - INFO -    ✅ Found CNL fold 1 model
2025-08-25 17:12:53,116 - INFO -    ✅ Found CNL fold 2 model
2025-08-25 17:12:53,117 - INFO -    ✅ Found CNL fold 3 model
2025-08-25 17:12:53,119 - INFO -    ✅ Found CNL fold 4 model
2025-08-25 17:12:53,120 - INFO -    ✅ Found DEN fold 0 model
2025-08-25 17:12:53,124 - INFO -    ✅ Found DEN fold 1 model
2025-08-25 17:12:53,125 - INFO -    ✅ Found DEN fold 2 model
2025-08-25 17:12:53,126 - INFO -    ✅ Found DEN fold 3 model
2025-08-25 17:12:53,128 - INFO -    ✅ Found DEN fold 4 model
2025-08-25 17:12:53,129 - INFO -    ✅ Found GR fold 0 model
2025-08-25 17:12:53,130 - INFO -    ✅ Found GR fold 1 model
2025-08-25 17:12:53,132 - INFO -    ✅ Found GR fold 2 model
2025-08-25 17:12:53,132 - INFO -    ✅ Found GR fold 3 model
2025-08-25 17:12:53,134 - INFO -    ✅ Found GR fold 4 model
2025-08-25 17:12:53,136 - INFO -    ✅ Found RLLD fold 0 model
2025-08-25 17:12:53,137 - INFO -    ✅ Found RLLD fold 1 model
2025-08-25 17:12:53,138 - INFO -    ✅ Found RLLD fold 2 model
2025-08-25 17:12:53,139 - INFO -    ✅ Found RLLD fold 3 model
2025-08-25 17:12:53,140 - INFO -    ✅ Found RLLD fold 4 model
2025-08-25 17:12:53,141 - INFO -
================================================================================
2025-08-25 17:12:53,142 - INFO - � VALIDATING AC PREDICTION MODEL
2025-08-25 17:12:53,144 - INFO - ================================================================================
2025-08-25 17:12:53,145 - INFO - � Cross-validating AC prediction (5 folds)
2025-08-25 17:12:53,146 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'RLLD']
2025-08-25 17:12:53,148 - INFO -    � Fold 1/5
2025-08-25 17:12:53,203 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:53,443 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:58,411 - INFO -       � Physical units - R²: -1.9049, MAE: 23.76 μs/ft, RMSE: 28.76 μs/ft
2025-08-25 17:12:58,414 - INFO -       � R²: -1.9049, MAE: 0.3168, RMSE: 0.3834
2025-08-25 17:12:58,415 - INFO -    � Fold 2/5
2025-08-25 17:12:58,486 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:58,536 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:58,935 - INFO -       � Physical units - R²: -1.1903, MAE: 30.91 μs/ft, RMSE: 42.36 μs/ft
2025-08-25 17:12:58,946 - INFO -       � R²: -1.1903, MAE: 0.4122, RMSE: 0.5648
2025-08-25 17:12:58,947 - INFO -    � Fold 3/5
2025-08-25 17:12:59,003 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:59,064 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:12:59,492 - INFO -       � Physical units - R²: -0.7453, MAE: 34.40 μs/ft, RMSE: 48.04 μs/ft
2025-08-25 17:12:59,497 - INFO -       � R²: -0.7453, MAE: 0.4587, RMSE: 0.6406
2025-08-25 17:12:59,498 - INFO -    � Fold 4/5
2025-08-25 17:12:59,560 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:12:59,607 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:00,032 - INFO -       � Physical units - R²: -0.6579, MAE: 24.44 μs/ft, RMSE: 34.13 μs/ft
2025-08-25 17:13:00,038 - INFO -       � R²: -0.6579, MAE: 0.3259, RMSE: 0.4551
2025-08-25 17:13:00,040 - INFO -    � Fold 5/5
2025-08-25 17:13:00,086 - INFO -    � Created 49 sequences for AC prediction
2025-08-25 17:13:00,150 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:00,585 - INFO -       � Physical units - R²: -0.3168, MAE: 18.01 μs/ft, RMSE: 25.31 μs/ft
2025-08-25 17:13:00,595 - INFO -       � R²: -0.3168, MAE: 0.2401, RMSE: 0.3374
2025-08-25 17:13:00,606 - INFO -    � Overall Performance:
2025-08-25 17:13:00,607 - INFO -       R² (normalized) = -0.9630 ± 0.2736 (95% CI: [-1.7226, -0.2034])
2025-08-25 17:13:00,609 - INFO -       MAE (normalized) = 0.3507 ± 0.0384
2025-08-25 17:13:00,612 - INFO -       RMSE (normalized) = 0.4763 ± 0.0562
2025-08-25 17:13:00,615 - INFO -       R² (physical) = -0.9630 ± 0.2736 (95% CI: [-1.7226, -0.2034])
2025-08-25 17:13:00,616 - INFO -       MAE (physical) = 26.31 ± 2.88 μs/ft
2025-08-25 17:13:00,617 - INFO -       RMSE (physical) = 35.72 ± 4.22 μs/ft
2025-08-25 17:13:00,619 - INFO - � Analyzing overfitting for AC
2025-08-25 17:13:00,621 - ERROR - ❌ Validation failed for AC: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:00,636 - INFO -
================================================================================
2025-08-25 17:13:00,645 - INFO - � VALIDATING CNL PREDICTION MODEL
2025-08-25 17:13:00,647 - INFO - ================================================================================
2025-08-25 17:13:00,648 - INFO - � Cross-validating CNL prediction (5 folds)
2025-08-25 17:13:00,649 - INFO -    � Input curves: ['GR', 'DEN', 'AC', 'RLLD']
2025-08-25 17:13:00,651 - INFO -    � Fold 1/5
2025-08-25 17:13:00,704 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:00,751 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:01,155 - INFO -       � R²: -0.1359, MAE: 0.8676, RMSE: 1.0623
2025-08-25 17:13:01,156 - INFO -    � Fold 2/5
2025-08-25 17:13:01,217 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:01,265 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:01,681 - INFO -       � R²: -0.0780, MAE: 0.8205, RMSE: 1.0353
2025-08-25 17:13:01,682 - INFO -    � Fold 3/5
2025-08-25 17:13:01,742 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:01,791 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:02,236 - INFO -       � R²: -0.0399, MAE: 0.7991, RMSE: 0.9980
2025-08-25 17:13:02,237 - INFO -    � Fold 4/5
2025-08-25 17:13:02,285 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:02,331 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:02,771 - INFO -       � R²: -0.2742, MAE: 0.7435, RMSE: 1.0775
2025-08-25 17:13:02,772 - INFO -    � Fold 5/5
2025-08-25 17:13:02,842 - INFO -    � Created 49 sequences for CNL prediction
2025-08-25 17:13:02,897 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:03,452 - INFO -       � R²: -0.2208, MAE: 0.7651, RMSE: 0.9727
2025-08-25 17:13:03,462 - INFO -    � Overall Performance:
2025-08-25 17:13:03,462 - INFO -       R² (normalized) = -0.1498 ± 0.0436 (95% CI: [-0.2707, -0.0288])
2025-08-25 17:13:03,465 - INFO -       MAE (normalized) = 0.7992 ± 0.0217
2025-08-25 17:13:03,466 - INFO -       RMSE (normalized) = 1.0292 ± 0.0195
2025-08-25 17:13:03,479 - INFO - � Analyzing overfitting for CNL
2025-08-25 17:13:03,480 - ERROR - ❌ Validation failed for CNL: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:03,862 - INFO -
================================================================================
2025-08-25 17:13:03,864 - INFO - � VALIDATING DEN PREDICTION MODEL
2025-08-25 17:13:03,865 - INFO - ================================================================================
2025-08-25 17:13:03,869 - INFO - � Cross-validating DEN prediction (5 folds)
2025-08-25 17:13:03,871 - INFO -    � Input curves: ['GR', 'CNL', 'AC', 'RLLD']
2025-08-25 17:13:03,874 - INFO -    � Fold 1/5
2025-08-25 17:13:03,937 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:03,989 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:04,702 - INFO -       � R²: -0.2077, MAE: 0.9052, RMSE: 1.0881
2025-08-25 17:13:04,703 - INFO -    � Fold 2/5
2025-08-25 17:13:04,757 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:04,811 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:05,540 - INFO -       � R²: -0.0203, MAE: 0.8060, RMSE: 0.9928
2025-08-25 17:13:05,541 - INFO -    � Fold 3/5
2025-08-25 17:13:05,593 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:05,643 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:06,267 - INFO -       � R²: -0.0034, MAE: 0.6859, RMSE: 0.9278
2025-08-25 17:13:06,268 - INFO -    � Fold 4/5
2025-08-25 17:13:06,328 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:06,381 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:06,977 - INFO -       � R²: -0.0365, MAE: 0.7788, RMSE: 0.9828
2025-08-25 17:13:06,978 - INFO -    � Fold 5/5
2025-08-25 17:13:07,024 - INFO -    � Created 49 sequences for DEN prediction
2025-08-25 17:13:07,078 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:07,657 - INFO -       � R²: -0.1706, MAE: 0.7877, RMSE: 1.0328
2025-08-25 17:13:07,666 - INFO -    � Overall Performance:
2025-08-25 17:13:07,667 - INFO -       R² (normalized) = -0.0877 ± 0.0422 (95% CI: [-0.2047, 0.0293])
2025-08-25 17:13:07,669 - INFO -       MAE (normalized) = 0.7927 ± 0.0350
2025-08-25 17:13:07,670 - INFO -       RMSE (normalized) = 1.0049 ± 0.0267
2025-08-25 17:13:07,683 - INFO - � Analyzing overfitting for DEN
2025-08-25 17:13:07,684 - ERROR - ❌ Validation failed for DEN: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:07,694 - INFO -
================================================================================
2025-08-25 17:13:07,695 - INFO - � VALIDATING GR PREDICTION MODEL
2025-08-25 17:13:07,696 - INFO - ================================================================================
2025-08-25 17:13:07,699 - INFO - � Cross-validating GR prediction (5 folds)
2025-08-25 17:13:07,700 - INFO -    � Input curves: ['CNL', 'DEN', 'AC', 'RLLD']
2025-08-25 17:13:07,702 - INFO -    � Fold 1/5
2025-08-25 17:13:07,760 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:07,806 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:08,410 - INFO -       � R²: -0.0007, MAE: 0.7821, RMSE: 0.9862
2025-08-25 17:13:08,411 - INFO -    � Fold 2/5
2025-08-25 17:13:08,473 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:08,523 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:09,125 - INFO -       � R²: 0.0489, MAE: 0.7285, RMSE: 0.9681
2025-08-25 17:13:09,126 - INFO -    � Fold 3/5
2025-08-25 17:13:09,166 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:09,217 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:09,800 - INFO -       � R²: -0.1890, MAE: 0.8588, RMSE: 1.0871
2025-08-25 17:13:09,801 - INFO -    � Fold 4/5
2025-08-25 17:13:09,861 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:09,913 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:10,469 - INFO -       � R²: -0.1880, MAE: 0.8456, RMSE: 1.0831
2025-08-25 17:13:10,469 - INFO -    � Fold 5/5
2025-08-25 17:13:10,510 - INFO -    � Created 49 sequences for GR prediction
2025-08-25 17:13:10,558 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:11,123 - INFO -       � R²: -0.1618, MAE: 0.7765, RMSE: 1.0517
2025-08-25 17:13:11,132 - INFO -    � Overall Performance:
2025-08-25 17:13:11,133 - INFO -       R² (normalized) = -0.0981 ± 0.0507 (95% CI: [-0.2390, 0.0428])
2025-08-25 17:13:11,134 - INFO -       MAE (normalized) = 0.7983 ± 0.0240
2025-08-25 17:13:11,136 - INFO -       RMSE (normalized) = 1.0352 ± 0.0247
2025-08-25 17:13:11,142 - INFO - � Analyzing overfitting for GR
2025-08-25 17:13:11,143 - ERROR - ❌ Validation failed for GR: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\
multiwell\validation\model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:11,154 - INFO -
================================================================================
2025-08-25 17:13:11,155 - INFO - � VALIDATING RLLD PREDICTION MODEL
2025-08-25 17:13:11,158 - INFO - ================================================================================
2025-08-25 17:13:11,160 - INFO - � Cross-validating RLLD prediction (5 folds)
2025-08-25 17:13:11,163 - INFO -    � Input curves: ['GR', 'CNL', 'DEN', 'AC']
2025-08-25 17:13:11,165 - INFO -    � Fold 1/5
2025-08-25 17:13:11,209 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:11,263 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:11,819 - INFO -       � R²: -0.2082, MAE: 0.8810, RMSE: 1.0765
2025-08-25 17:13:11,820 - INFO -    � Fold 2/5
2025-08-25 17:13:11,872 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:11,921 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:12,498 - INFO -       � R²: -0.0362, MAE: 0.7612, RMSE: 1.0016
2025-08-25 17:13:12,498 - INFO -    � Fold 3/5
2025-08-25 17:13:12,540 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:12,590 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:13,154 - INFO -       � R²: -0.1253, MAE: 0.8663, RMSE: 1.0556
2025-08-25 17:13:13,156 - INFO -    � Fold 4/5
2025-08-25 17:13:13,216 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:13,265 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:13,830 - INFO -       � R²: -0.0485, MAE: 0.7980, RMSE: 1.0063
2025-08-25 17:13:13,831 - INFO -    � Fold 5/5
2025-08-25 17:13:13,892 - INFO -    � Created 49 sequences for RLLD prediction
2025-08-25 17:13:13,953 - WARNING -       ⚠️  Using untrained model (for baseline)
2025-08-25 17:13:14,524 - INFO -       � R²: -0.0960, MAE: 0.8548, RMSE: 1.0416
2025-08-25 17:13:14,528 - INFO -    � Overall Performance:
2025-08-25 17:13:14,529 - INFO -       R² (normalized) = -0.1028 ± 0.0309 (95% CI: [-0.1885, -0.0172])
2025-08-25 17:13:14,533 - INFO -       MAE (normalized) = 0.8323 ± 0.0227
2025-08-25 17:13:14,537 - INFO -       RMSE (normalized) = 1.0363 ± 0.0144
2025-08-25 17:13:14,546 - INFO - � Analyzing overfitting for RLLD
2025-08-25 17:13:14,547 - ERROR - ❌ Validation failed for RLLD: 'directory'
Traceback (most recent call last):
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\validation\
model_validation_suite.py", line 1007, in run_comprehensive_validation
    overfitting_results = self.detect_overfitting(model_info, target_curve)
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "c:\Users\<USER>\Documents\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT\Init_transformer\multiwell\validation\
model_validation_suite.py", line 566, in detect_overfitting
    history_file = model_info['directory'] / "training_history.json"
                   ~~~~~~~~~~^^^^^^^^^^^^^
KeyError: 'directory'
2025-08-25 17:13:14,580 - INFO - � Validation results saved: validation_outputs\comprehensive_validation_results.json
2025-08-25 17:13:14,589 - INFO - � Validation report saved: validation_outputs\validation_report.md
2025-08-25 17:13:14,590 - INFO - � Comprehensive Model Validation Complete!