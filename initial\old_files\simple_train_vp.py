"""
Simple Vp training script without external dependencies
"""
import os
import argparse
import time
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import h5py

# Import existing modules
from utils import get_device, save_checkpoint, load_checkpoint, EarlyStopping, cal_RMSE, cal_R2
from model import MWLT_Small, MWLT_Base, MWLT_Large
from dataset import WellDataset
from las_processor import LASProcessor

def create_simple_vp_data():
    """
    Create simple training data from A1.hdf5 and A2.hdf5
    """
    print("=== Creating Simple Vp Training Data ===")
    
    processor = LASProcessor()
    
    # Create directories
    train_dir = "../simple_vp_data/train"
    val_dir = "../simple_vp_data/val"
    os.makedirs(train_dir, exist_ok=True)
    os.makedirs(val_dir, exist_ok=True)
    
    # Process A1.hdf5 and A2.hdf5
    source_files = ['../A1.hdf5', '../A2.hdf5']
    
    total_samples = 0
    for i, source_file in enumerate(source_files):
        if not os.path.exists(source_file):
            print(f"Warning: {source_file} not found, skipping...")
            continue
        
        print(f"Processing {source_file}...")
        
        # Load data
        curves = processor.process_hdf5_to_las_format(source_file)
        
        # Create 10 samples per file using sliding window
        total_length = len(curves['AC'])
        window_size = 720
        
        # Calculate step size for 10 samples
        if total_length > window_size:
            step_size = max(1, (total_length - window_size) // 9)  # 10 samples
        else:
            step_size = 1
        
        sample_count = 0
        for start_idx in range(0, min(total_length - window_size + 1, 10 * step_size), step_size):
            if sample_count >= 10:  # Limit to 10 samples per file
                break
                
            end_idx = start_idx + window_size
            
            # Extract window
            sample_curves = {}
            for curve_name, data in curves.items():
                sample_curves[curve_name] = data[start_idx:end_idx]
            
            # Prepare for model
            input_features, target_ac = processor.prepare_for_prediction(sample_curves, 720)
            
            # Create filename
            file_prefix = f"A{i+1}_sample_{sample_count:03d}"
            
            # Split: first 8 samples for training, last 2 for validation
            if sample_count >= 8:
                output_dir = val_dir
            else:
                output_dir = train_dir
            
            output_file = os.path.join(output_dir, f"{file_prefix}.hdf5")
            
            # Save as HDF5 file
            with h5py.File(output_file, 'w') as f:
                # Input curves
                curve_names = ['GR', 'CNL', 'DEN', 'RLLD']
                for j, curve_name in enumerate(curve_names):
                    f.create_dataset(curve_name, data=input_features[j].astype(np.float32))
                
                # Target curve (AC)
                f.create_dataset('AC', data=target_ac.squeeze().astype(np.float32))
            
            sample_count += 1
            total_samples += 1
        
        print(f"Created {sample_count} samples from {source_file}")
    
    train_files = len([f for f in os.listdir(train_dir) if f.endswith('.hdf5')])
    val_files = len([f for f in os.listdir(val_dir) if f.endswith('.hdf5')])
    
    print(f"\nDataset created:")
    print(f"  Training samples: {train_files}")
    print(f"  Validation samples: {val_files}")
    
    return train_dir, val_dir

def simple_train_vp():
    """
    Simple training function
    """
    print("=== Simple Vp Model Training ===")
    
    # Fixed configuration for simplicity
    config = {
        'model_type': 'small',
        'batch_size': 4,
        'epochs': 30,
        'learning_rate': 1e-3,
        'patience': 15,
        'device': '0'
    }
    
    device = get_device(int(config['device']))
    
    # Create save directory
    save_path = "../simple_vp_training"
    os.makedirs(save_path, exist_ok=True)
    
    # Create training data
    train_dir, val_dir = create_simple_vp_data()
    
    # Prepare datasets
    train_dataset = WellDataset(
        root_path=train_dir,
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['AC'],
        transform=True,
        total_seqlen=720,
        effect_seqlen=640
    )
    
    val_dataset = WellDataset(
        root_path=val_dir,
        input_curves=['GR', 'CNL', 'DEN', 'RLLD'],
        output_curves=['AC'],
        transform=False,
        total_seqlen=720,
        effect_seqlen=640
    )
    
    print(f"Training samples: {len(train_dataset)}")
    print(f"Validation samples: {len(val_dataset)}")
    
    if len(train_dataset) == 0 or len(val_dataset) == 0:
        print("Error: No training or validation data found!")
        return
    
    # Create data loaders
    train_loader = DataLoader(dataset=train_dataset, batch_size=config['batch_size'], shuffle=True)
    val_loader = DataLoader(dataset=val_dataset, batch_size=1, shuffle=False)
    
    # Build model
    model = MWLT_Small(
        in_channels=4,  # GR, CNL, DEN, RLLD
        out_channels=1,  # AC
        feature_num=64,
        use_pe=True,
        drop=0.1,
        attn_drop=0.1,
        position_drop=0.1
    )
    
    model = model.to(device)
    
    # Setup training
    optimizer = optim.Adam(model.parameters(), lr=config['learning_rate'])
    criterion = nn.MSELoss().to(device)
    
    # Early stopping
    early_stopping = EarlyStopping(
        patience=config['patience'],
        path=os.path.join(save_path, "best_simple_vp_model.pth"),
        verbose=True
    )
    
    print(f"\nStarting training...")
    print(f"Device: {device}")
    print(f"Model: {config['model_type']}")
    print(f"Epochs: {config['epochs']}")
    print("-" * 60)
    
    # Training loop
    for epoch in range(1, config['epochs'] + 1):
        epoch_start_time = time.time()
        
        # Training phase
        model.train()
        train_total_loss = 0.0
        
        for conditions, targets in train_loader:
            conditions = conditions.to(device)
            targets = targets.to(device)
            
            predictions = model(conditions)
            loss = criterion(predictions, targets)
            
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()
            
            train_total_loss += loss.item()
        
        train_loss_epoch = train_total_loss / len(train_loader)
        
        # Validation phase
        model.eval()
        val_total_loss = 0.0
        all_predictions = []
        all_targets = []
        
        with torch.no_grad():
            for conditions, targets in val_loader:
                conditions = conditions.to(device)
                targets = targets.to(device)
                
                predictions = model(conditions)
                loss = criterion(predictions, targets)
                val_total_loss += loss.item()
                
                all_predictions.append(predictions.cpu().numpy())
                all_targets.append(targets.cpu().numpy())
        
        val_loss_epoch = val_total_loss / len(val_loader)
        
        # Calculate metrics
        all_predictions = np.concatenate(all_predictions, axis=0).flatten()
        all_targets = np.concatenate(all_targets, axis=0).flatten()
        val_rmse = cal_RMSE(all_predictions, all_targets)
        val_r2 = cal_R2(all_predictions, all_targets)
        
        # Print progress
        epoch_time = time.time() - epoch_start_time
        print(f"Epoch [{epoch:3d}/{config['epochs']}] | "
              f"Train: {train_loss_epoch:.6f} | "
              f"Val: {val_loss_epoch:.6f} | "
              f"RMSE: {val_rmse:.4f} | "
              f"R²: {val_r2:.4f} | "
              f"Time: {epoch_time:.2f}s")
        
        # Save checkpoint and check early stopping
        state = {
            "model_state_dict": model.state_dict(),
            "optimizer_state_dict": optimizer.state_dict(),
            "loss": val_loss_epoch,
            "epoch": epoch,
            "rmse": val_rmse,
            "r2": val_r2
        }
        
        early_stopping(state, model)
        
        if early_stopping.early_stop:
            print(f"Early stopping at epoch {epoch}")
            break
    
    print("-" * 60)
    print("Training completed!")
    print(f"Best model saved: {os.path.join(save_path, 'best_simple_vp_model.pth')}")
    
    # Test the trained model
    print("\nTesting trained model...")
    test_trained_model(os.path.join(save_path, "best_simple_vp_model.pth"), device)

def test_trained_model(model_path, device):
    """
    Quick test of the trained model
    """
    try:
        # Load model
        model = MWLT_Small(in_channels=4, out_channels=1, feature_num=64)
        model = model.to(device)
        
        model_dict, epoch, loss = load_checkpoint(model_path, device)
        model.load_state_dict(model_dict)
        model.eval()
        
        print(f"✅ Model loaded (epoch {epoch}, loss {loss:.6f})")
        
        # Test with A1.hdf5
        print("Testing with A1.hdf5...")
        processor = LASProcessor()
        curves = processor.process_hdf5_to_las_format("../A1.hdf5")
        input_features, target_ac = processor.prepare_for_prediction(curves, 720)
        
        # Convert to tensor
        input_tensor = torch.from_numpy(input_features[:, :640]).unsqueeze(0).float().to(device)
        target_tensor = torch.from_numpy(target_ac[:, :640]).float().to(device)
        
        with torch.no_grad():
            prediction = model(input_tensor)
            
            pred_np = prediction.cpu().numpy().flatten()
            target_np = target_tensor.cpu().numpy().flatten()
            
            rmse = cal_RMSE(pred_np, target_np)
            r2 = cal_R2(pred_np, target_np)
            
            print(f"Test Results:")
            print(f"  RMSE: {rmse:.4f}")
            print(f"  R²: {r2:.4f}")
            print(f"  Prediction range: [{pred_np.min():.2f}, {pred_np.max():.2f}]")
            print(f"  Target range: [{target_np.min():.2f}, {target_np.max():.2f}]")
            
    except Exception as e:
        print(f"❌ Test failed: {e}")

if __name__ == "__main__":
    simple_train_vp()
