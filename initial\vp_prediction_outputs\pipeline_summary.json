{"pipeline_execution_time": 40.978718757629395, "timestamp": "2025-08-23T22:45:33.475732", "configuration": {"model": {"type": "base", "input_channels": 4, "output_channels": 1, "feature_num": 64, "sequence_length": 720, "effect_length": 640}, "training": {"batch_size": 8, "learning_rate": 0.0001, "weight_decay": 1e-05, "epochs": 200, "patience": 50, "validation_split": 0.2, "random_seed": 42, "device": 0, "resume_training": false}, "validation": {"cv_folds": 5, "monte_carlo_samples": 50, "confidence_level": 0.95, "metrics": ["r2", "rmse", "mae"]}, "data": {"input_curves": ["GR", "CNL", "DEN", "RLLD"], "target_curve": "AC", "data_augmentation": true, "augmentation_factor": 2.0, "quality_threshold": 0.8}, "output": {"base_dir": "vp_prediction_outputs", "save_plots": true, "save_models": true, "save_predictions": true, "plot_format": "png", "plot_dpi": 300}}, "stages_completed": ["training", "validation", "prediction"], "performance_summary": {"training": {"best_val_r2": 0.860914945602417, "best_val_rmse": 0.16187715530395508}, "validation": {"mean_cv_r2": 0.9160463571548462, "cv_r2_std": 0.028528236561268863, "performance_rating": "Excellent"}, "prediction": {"test_r2": 0.8767944574356079, "test_rmse": 0.1021847188203645, "test_samples": 3200}}, "output_files": {"training_model": "vp_prediction_outputs\\training\\best_vp_model.pth", "validation_report": "vp_prediction_outputs\\validation\\validation_report.json", "prediction_report": "vp_prediction_outputs\\prediction\\prediction_report.json", "pipeline_log": "vp_prediction_outputs\\pipeline.log"}}