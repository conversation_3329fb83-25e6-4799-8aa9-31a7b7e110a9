"""
VpTransformer Prediction Examples
Demonstrates how to use the VpTransformer integration package for sonic velocity prediction
"""
import os
import sys
import numpy as np

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import VpPredictor, VpTransformerAPI, get_model, get_normalizer, get_processor
    from vp_predictor import MODEL_CONFIG, get_package_info
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    print("Please ensure the vp_predictor package is properly installed or accessible")
    sys.exit(1)

def example_1_basic_prediction():
    """
    Example 1: Basic sonic velocity prediction from synthetic well log curves
    """
    print("\n" + "="*60)
    print("EXAMPLE 1: Basic VpTransformer Prediction")
    print("="*60)
    
    # Note: Replace with actual trained model path
    model_path = "../models/best_vp_model.pth"
    
    if not os.path.exists(model_path):
        print(f"⚠️  Model file not found: {model_path}")
        print("Creating synthetic model path for demonstration...")
        model_path = "demo_model.pth"  # This would fail, but shows the API
    
    try:
        # Initialize predictor
        predictor = VpPredictor(model_path, device_id=0, model_type="base")
        
        # Create synthetic well log data (640 points)
        depth_points = 640
        print(f"📊 Creating synthetic well log data ({depth_points} points)...")
        
        # Generate realistic well log curves
        depth = np.linspace(2000, 2500, depth_points)  # 500m interval
        
        # Gamma Ray (API units)
        gr = 60 + 40 * np.sin(depth / 100) + 10 * np.random.normal(0, 1, depth_points)
        gr = np.clip(gr, 0, 200)
        
        # Neutron Porosity (%)  
        cnl = 25 + 15 * np.sin(depth / 150 + 1) + 5 * np.random.normal(0, 1, depth_points)
        cnl = np.clip(cnl, 0, 60)
        
        # Bulk Density (g/cm³)
        den = 2.3 + 0.4 * np.sin(depth / 120 + 2) + 0.05 * np.random.normal(0, 1, depth_points)
        den = np.clip(den, 1.5, 3.0)
        
        # Deep Resistivity (ohm-m)
        rlld = 10 ** (0.5 + 1.5 * np.sin(depth / 200 + 3) + 0.2 * np.random.normal(0, 1, depth_points))
        rlld = np.clip(rlld, 0.1, 1000)
        
        print(f"📈 Input curves ranges:")
        print(f"   GR:   {gr.min():.1f} - {gr.max():.1f} API")
        print(f"   CNL:  {cnl.min():.1f} - {cnl.max():.1f} %")
        print(f"   DEN:  {den.min():.2f} - {den.max():.2f} g/cm³")
        print(f"   RLLD: {rlld.min():.1f} - {rlld.max():.1f} ohm-m")
        
        # Make prediction
        print("\n🔮 Making Vp prediction...")
        vp_prediction = predictor.predict_from_curves(gr, cnl, den, rlld)
        
        print(f"✅ Prediction completed!")
        print(f"🎯 Predicted Vp range: {vp_prediction.min():.1f} - {vp_prediction.max():.1f} μs/ft")
        print(f"📊 Mean Vp: {vp_prediction.mean():.1f} μs/ft")
        print(f"📊 Std Vp:  {vp_prediction.std():.1f} μs/ft")
        
        # Show model info
        model_info = predictor.get_model_info()
        print(f"\n📋 Model Information:")
        print(f"   Type: {model_info['model_type']}")
        print(f"   Architecture: {model_info['architecture']}")
        print(f"   Device: {model_info['device']}")
        print(f"   Improvements: {model_info['improvements']}")
        
        return vp_prediction
        
    except Exception as e:
        print(f"❌ Example 1 failed: {e}")
        return None

def example_2_file_prediction():
    """
    Example 2: Predicting from HDF5/LAS files
    """
    print("\n" + "="*60)
    print("EXAMPLE 2: File-based Prediction")
    print("="*60)
    
    # Sample data file path (replace with actual file)
    sample_file = "../examples/sample_data.hdf5"
    model_path = "../models/best_vp_model.pth"
    
    if not os.path.exists(model_path):
        print(f"⚠️  Model file not found: {model_path}")
        print("This example requires a trained model checkpoint")
        return None
    
    if not os.path.exists(sample_file):
        print(f"⚠️  Sample file not found: {sample_file}")
        print("Creating synthetic data file for demonstration...")
        # In practice, you would have actual well data files
        return None
    
    try:
        # Initialize predictor  
        predictor = VpPredictor(model_path, device_id=-1, model_type="base")  # Use CPU
        
        # Predict from file
        print(f"📂 Loading data from: {sample_file}")
        vp_prediction = predictor.predict_from_file(sample_file)
        
        print(f"✅ File prediction completed!")
        print(f"🎯 Predicted Vp range: {vp_prediction.min():.1f} - {vp_prediction.max():.1f} μs/ft")
        
        return vp_prediction
        
    except Exception as e:
        print(f"❌ Example 2 failed: {e}")
        return None

def example_3_advanced_api():
    """
    Example 3: Advanced API with validation and batch processing
    """
    print("\n" + "="*60)
    print("EXAMPLE 3: Advanced VpTransformer API")
    print("="*60)
    
    model_path = "../models/best_vp_model.pth"
    
    if not os.path.exists(model_path):
        print(f"⚠️  Model file not found: {model_path}")
        print("This example requires a trained model checkpoint")
        return None
    
    try:
        # Initialize advanced API
        api = VpTransformerAPI(model_path, device="auto", model_type="base")
        
        # Show package information
        print("📦 Package Information:")
        package_info = get_package_info()
        print(f"   Version: {package_info['version']}")
        print(f"   Improvements: {package_info['improvements']}")
        print(f"   Vp Range: {package_info['vp_range']} μs/ft")
        
        # Verify model integrity
        print(f"\n🔍 Model integrity check: {'✅ PASSED' if api.verify_model_integrity() else '❌ FAILED'}")
        
        # Create test data with validation
        curves_data = {
            'GR': 50 + 30 * np.random.random(640),
            'CNL': 15 + 20 * np.random.random(640),  
            'DEN': 2.0 + 0.6 * np.random.random(640),
            'RLLD': 1 + 99 * np.random.random(640)
        }
        
        # Enhanced prediction with validation
        print("\n🔮 Making enhanced prediction with validation...")
        result = api.predict(curves_data, format="curves", validate=True)
        
        if result['predictions'] is not None:
            print(f"✅ Prediction successful!")
            print(f"🎯 Vp range: {result['predictions'].min():.1f} - {result['predictions'].max():.1f} μs/ft")
            print(f"📊 Confidence: {result['confidence'].mean():.2f} ± {result['confidence'].std():.2f}")
            print(f"⚠️  Warnings: {len(result['warnings'])}")
            
            if result['warnings']:
                for warning in result['warnings']:
                    print(f"   - {warning}")
                    
            # Show metadata
            metadata = result['metadata']
            print(f"\n📋 Prediction Metadata:")
            print(f"   Range: [{metadata['prediction_range'][0]:.1f}, {metadata['prediction_range'][1]:.1f}] μs/ft")
            print(f"   Mean: {metadata['mean_prediction']:.1f} μs/ft")
            print(f"   Std:  {metadata['std_prediction']:.1f} μs/ft")
            print(f"   Device: {metadata['device']}")
        else:
            print(f"❌ Prediction failed: {result.get('error', 'Unknown error')}")
        
        # Batch prediction example
        print(f"\n🔄 Batch prediction example...")
        batch_data = [curves_data.copy() for _ in range(3)]  # 3 identical predictions
        batch_results = api.batch_predict(batch_data)
        
        print(f"📊 Batch processed: {len(batch_results)} items")
        for i, result in enumerate(batch_results):
            if result['predictions'] is not None:
                print(f"   Item {i}: Vp range {result['predictions'].min():.1f}-{result['predictions'].max():.1f} μs/ft")
            else:
                print(f"   Item {i}: Failed")
        
        return result
        
    except Exception as e:
        print(f"❌ Example 3 failed: {e}")
        return None

def example_4_model_variants():
    """
    Example 4: Comparing different model variants (Small, Base, Large)
    """
    print("\n" + "="*60)
    print("EXAMPLE 4: Model Variants Comparison")
    print("="*60)
    
    # Show available model configurations
    print("🏗️  Available Model Variants:")
    variants = MODEL_CONFIG['model_variants']
    for name, config in variants.items():
        print(f"   {name.upper()}:")
        print(f"     ResNet Blocks: {config['res_blocks']}")
        print(f"     Encoders: {config['encoders']}")
        print(f"     Attention Heads: {config['heads']}")
        print(f"     Features: {config['features']}")
    
    print(f"\n⚙️  Training Parameters:")
    train_params = MODEL_CONFIG['training_params']
    for param, value in train_params.items():
        print(f"   {param}: {value}")
    
    print(f"\n🎯 Expected Vp Range: {MODEL_CONFIG['vp_range']} μs/ft")
    print(f"📏 Sequence Length: {MODEL_CONFIG['sequence_length']}")
    print(f"📊 Input Curves: {MODEL_CONFIG['input_curves']}")
    
    # Demonstrate model creation (without actual prediction)
    try:
        print(f"\n🔧 Creating model variants (architecture only):")
        
        for variant in ['small', 'base', 'large']:
            model = get_model(variant)
            param_count = sum(p.numel() for p in model.parameters())
            print(f"   {variant.upper()}: {param_count:,} parameters")
        
        print(f"\n✅ Model variants created successfully!")
        
    except Exception as e:
        print(f"❌ Example 4 failed: {e}")

def example_5_utilities():
    """
    Example 5: Using utility functions and components
    """
    print("\n" + "="*60)
    print("EXAMPLE 5: Utility Functions and Components")
    print("="*60)
    
    try:
        # Data normalizer
        print("🔧 Testing VpDataNormalizer...")
        normalizer = get_normalizer()
        
        # Test data
        test_vp = np.array([60, 80, 100, 120])
        normalized = normalizer.normalize_vp(test_vp)
        denormalized = normalizer.denormalize_vp(normalized)
        
        print(f"   Original Vp: {test_vp}")
        print(f"   Normalized: {normalized}")
        print(f"   Denormalized: {denormalized}")
        print(f"   ✅ Normalization test {'PASSED' if np.allclose(test_vp, denormalized) else 'FAILED'}")
        
        # LAS processor
        print(f"\n📂 Testing LAS Processor...")
        processor = get_processor()
        print(f"   Required curves: {processor.required_curves}")
        print(f"   Target curve: {processor.target_curve}")
        
        # Create synthetic curves and test processing
        synthetic_curves = {
            'GR': np.random.uniform(20, 120, 720),
            'CNL': np.random.uniform(5, 40, 720),
            'DEN': np.random.uniform(2.0, 2.8, 720),
            'RLLD': np.random.uniform(1, 100, 720),
            'AC': np.random.uniform(60, 120, 720)
        }
        
        input_features, target = processor.prepare_for_prediction(synthetic_curves)
        print(f"   Input shape: {input_features.shape}")
        print(f"   Target shape: {target.shape}")
        print(f"   ✅ Data processing test PASSED")
        
        # Show normalization parameters
        print(f"\n📊 Normalization Parameters:")
        print(f"   Input curves:")
        for curve, stats in normalizer.input_stats.items():
            print(f"     {curve}: mean={stats['mean']}, std={stats['std']}")
        
        vp_stats = normalizer.vp_stats
        print(f"   Vp curve: mean={vp_stats['mean']}, std={vp_stats['std']}")
        
        print(f"\n✅ Utilities test completed successfully!")
        
    except Exception as e:
        print(f"❌ Example 5 failed: {e}")

def main():
    """
    Run all examples
    """
    print("🚀 VpTransformer Integration Examples")
    print("="*60)
    print("This script demonstrates how to use the VpTransformer integration package")
    print("for sonic velocity prediction from well log data.")
    
    # Run examples
    try:
        example_1_basic_prediction()
        example_2_file_prediction()  
        example_3_advanced_api()
        example_4_model_variants()
        example_5_utilities()
        
        print("\n" + "="*60)
        print("🎉 ALL EXAMPLES COMPLETED")
        print("="*60)
        print("\n📚 Next Steps:")
        print("1. Train a VpTransformer model using the training scripts")
        print("2. Save the trained model checkpoint (.pth file)")
        print("3. Update the model_path variables in these examples")
        print("4. Run predictions on your actual well log data")
        print("\n📖 For more information, see the README.md file")
        
    except Exception as e:
        print(f"\n❌ Examples failed with error: {e}")
        print("\n🔧 Troubleshooting:")
        print("1. Ensure all dependencies are installed: pip install -r requirements.txt")
        print("2. Check that the vp_predictor package is accessible")
        print("3. Verify model checkpoint paths exist")
        print("4. Review the integration guide in core_module_incorporation.md")

if __name__ == "__main__":
    main()