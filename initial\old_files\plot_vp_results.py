"""
Enhanced plotting script for Vp prediction results
Creates comprehensive visualizations of the improved Vp predictions
"""
import matplotlib.pyplot as plt
import numpy as np
import os
import h5py
from datetime import datetime

def create_vp_comparison_plots(results, save_dir="../vp_improved_results"):
    """
    Create comprehensive comparison plots for Vp prediction results
    """
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # Set up the plotting style
    plt.style.use('default')
    plt.rcParams['figure.figsize'] = (15, 10)
    plt.rcParams['font.size'] = 10
    
    # Create a comprehensive figure with multiple subplots
    fig = plt.figure(figsize=(20, 12))
    fig.suptitle('Improved Vp (Sonic Velocity) Prediction Results', fontsize=16, fontweight='bold')
    
    # Number of files
    n_files = len(results)
    
    # Create subplots: 2 rows, n_files columns for individual comparisons
    # Plus additional plots for overall analysis
    
    # Individual file comparisons
    for i, result in enumerate(results):
        # Prediction vs Actual plot
        ax1 = plt.subplot(3, n_files, i + 1)
        
        actual = result['actual']
        predicted = result['prediction']
        
        # Create depth array (assuming uniform sampling)
        depth = np.arange(len(actual))
        
        ax1.plot(actual, depth, 'b-', label='Actual Vp', linewidth=2, alpha=0.8)
        ax1.plot(predicted, depth, 'r--', label='Predicted Vp', linewidth=2, alpha=0.8)
        ax1.set_xlabel('Vp (μs/ft)')
        ax1.set_ylabel('Sample Index')
        ax1.set_title(f'{result["file"]}\nRMSE: {result["rmse"]:.2f}, R²: {result["r2"]:.3f}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.invert_yaxis()  # Invert y-axis to simulate depth
        
        # Scatter plot (Predicted vs Actual)
        ax2 = plt.subplot(3, n_files, i + 1 + n_files)
        
        ax2.scatter(actual, predicted, alpha=0.6, s=20)
        
        # Perfect prediction line
        min_val = min(actual.min(), predicted.min())
        max_val = max(actual.max(), predicted.max())
        ax2.plot([min_val, max_val], [min_val, max_val], 'k--', alpha=0.8, label='Perfect Prediction')
        
        ax2.set_xlabel('Actual Vp (μs/ft)')
        ax2.set_ylabel('Predicted Vp (μs/ft)')
        ax2.set_title(f'Scatter Plot - {result["file"]}')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # Residual plot
        ax3 = plt.subplot(3, n_files, i + 1 + 2*n_files)
        
        residuals = predicted - actual
        ax3.scatter(actual, residuals, alpha=0.6, s=20)
        ax3.axhline(y=0, color='k', linestyle='--', alpha=0.8)
        ax3.set_xlabel('Actual Vp (μs/ft)')
        ax3.set_ylabel('Residuals (μs/ft)')
        ax3.set_title(f'Residuals - {result["file"]}')
        ax3.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save the comprehensive plot
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_file = os.path.join(save_dir, f'vp_improved_analysis_{timestamp}.png')
    plt.savefig(plot_file, dpi=300, bbox_inches='tight')
    print(f"📊 Comprehensive plot saved: {plot_file}")
    
    # Create summary statistics plot
    create_summary_plot(results, save_dir, timestamp)
    
    # Show the plot
    plt.show()
    
    return plot_file

def create_summary_plot(results, save_dir, timestamp):
    """
    Create a summary plot with overall statistics
    """
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 10))
    fig.suptitle('Improved Vp Prediction - Summary Statistics', fontsize=14, fontweight='bold')
    
    # Extract metrics
    files = [r['file'] for r in results]
    rmse_values = [r['rmse'] for r in results]
    r2_values = [r['r2'] for r in results]
    pred_ranges = [r['pred_range'] for r in results]
    actual_ranges = [r['actual_range'] for r in results]
    
    # RMSE comparison
    ax1.bar(files, rmse_values, color='skyblue', alpha=0.7)
    ax1.set_ylabel('RMSE (μs/ft)')
    ax1.set_title('RMSE by File')
    ax1.tick_params(axis='x', rotation=45)
    for i, v in enumerate(rmse_values):
        ax1.text(i, v + max(rmse_values)*0.01, f'{v:.1f}', ha='center', va='bottom')
    
    # R² comparison
    ax2.bar(files, r2_values, color='lightcoral', alpha=0.7)
    ax2.set_ylabel('R² Score')
    ax2.set_title('R² Score by File')
    ax2.tick_params(axis='x', rotation=45)
    ax2.axhline(y=0, color='k', linestyle='--', alpha=0.5)
    for i, v in enumerate(r2_values):
        ax2.text(i, v + (max(r2_values) - min(r2_values))*0.02, f'{v:.2f}', ha='center', va='bottom')
    
    # Range comparison
    x_pos = np.arange(len(files))
    width = 0.35
    
    actual_mins = [r[0] for r in actual_ranges]
    actual_maxs = [r[1] for r in actual_ranges]
    pred_mins = [r[0] for r in pred_ranges]
    pred_maxs = [r[1] for r in pred_ranges]
    
    ax3.bar(x_pos - width/2, actual_maxs, width, label='Actual Max', alpha=0.7, color='blue')
    ax3.bar(x_pos - width/2, actual_mins, width, label='Actual Min', alpha=0.7, color='lightblue')
    ax3.bar(x_pos + width/2, pred_maxs, width, label='Predicted Max', alpha=0.7, color='red')
    ax3.bar(x_pos + width/2, pred_mins, width, label='Predicted Min', alpha=0.7, color='lightcoral')
    
    ax3.set_ylabel('Vp (μs/ft)')
    ax3.set_title('Value Ranges Comparison')
    ax3.set_xticks(x_pos)
    ax3.set_xticklabels(files, rotation=45)
    ax3.legend()
    
    # Overall statistics text
    avg_rmse = np.mean(rmse_values)
    avg_r2 = np.mean(r2_values)
    
    stats_text = f"""
    OVERALL PERFORMANCE SUMMARY
    
    Average RMSE: {avg_rmse:.2f} μs/ft
    Average R²: {avg_r2:.3f}
    
    Files Processed: {len(results)}
    
    INTERPRETATION:
    • RMSE < 50: Excellent
    • RMSE 50-100: Good  
    • RMSE > 100: Needs improvement
    
    • R² > 0.8: Excellent
    • R² 0.5-0.8: Good
    • R² < 0.5: Poor
    
    STATUS: {'✅ EXCELLENT' if avg_rmse < 50 and avg_r2 > 0.8 else '✅ GOOD' if avg_rmse < 100 and avg_r2 > 0.5 else '⚠️ NEEDS IMPROVEMENT'}
    """
    
    ax4.text(0.05, 0.95, stats_text, transform=ax4.transAxes, fontsize=10,
             verticalalignment='top', fontfamily='monospace',
             bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    ax4.set_xlim(0, 1)
    ax4.set_ylim(0, 1)
    ax4.axis('off')
    
    plt.tight_layout()
    
    # Save summary plot
    summary_file = os.path.join(save_dir, f'vp_improved_summary_{timestamp}.png')
    plt.savefig(summary_file, dpi=300, bbox_inches='tight')
    print(f"📈 Summary plot saved: {summary_file}")
    
    plt.show()
    
    return summary_file

def main():
    """
    Main function to create plots from existing results
    """
    print("📊 Creating enhanced Vp prediction plots...")
    
    # This would typically be called from test_vp_improved.py
    # For standalone use, you'd need to load results from somewhere
    print("This script is designed to be called from test_vp_improved.py")
    print("Run: python test_vp_improved.py to generate plots automatically")

if __name__ == "__main__":
    main()
