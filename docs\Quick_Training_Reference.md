# MWLT Quick Training Reference
## Fast Reference for Common Training Scenarios

**Quick Start**: Copy-paste ready code for immediate use  
**Phase 2 Complete**: All curve types supported

---

## 🚀 Quick Start Templates

### 1. Density Prediction (Most Common)

```python
from vp_predictor import *

# Load your data (shape: [n_samples, sequence_length])
data_dict = {
    'GR': your_gamma_ray_data,
    'CNL': your_neutron_data, 
    'AC': your_sonic_data,
    'RLLD': your_resistivity_data,
    'DEN': your_density_data  # Target
}

# Quick setup
model = GeneralWellLogTransformer.from_template('density_prediction')
normalizer = GeneralDataNormalizer(['GR', 'CNL', 'AC', 'RLLD'], ['DEN'])
dataset = GeneralWellLogDataset(data_dict, ['GR', 'CNL', 'AC', 'RLLD'], 'DEN', normalizer)

# Train
trainer = GeneralTrainingManager(model, dataset, get_training_template('density_training'))
results = trainer.train()
```

### 2. Neutron Prediction

```python
# Neutron porosity prediction
model = GeneralWellLogTransformer.from_template('vp_prediction')  # Reuse base
normalizer = GeneralDataNormalizer(['GR', 'DEN', 'AC', 'RLLD'], ['CNL'])
dataset = GeneralWellLogDataset(data_dict, ['GR', 'DEN', 'AC', 'RLLD'], 'CNL', normalizer)

config = get_training_template('neutron_training')
trainer = GeneralTrainingManager(model, dataset, config)
results = trainer.train()
```

### 3. Resistivity Prediction

```python
# Resistivity prediction (challenging due to log scale)
model = GeneralWellLogTransformer.from_template('vp_prediction')
normalizer = GeneralDataNormalizer(['GR', 'CNL', 'DEN', 'AC'], ['RLLD'])
dataset = GeneralWellLogDataset(data_dict, ['GR', 'CNL', 'DEN', 'AC'], 'RLLD', normalizer)

config = get_training_template('resistivity_training')
config['max_epochs'] = 150  # Needs more epochs
trainer = GeneralTrainingManager(model, dataset, config)
results = trainer.train()
```

---

## 📊 Curve-Specific Quick Configs

### Optimal Hyperparameters by Curve

| Curve | Learning Rate | Epochs | Batch Size | Loss Type | Notes |
|-------|---------------|--------|------------|-----------|-------|
| **DEN** | 1e-4 | 100 | 8 | mse | Stable, good baseline |
| **CNL** | 8e-5 | 120 | 8 | huber | Robust to noise |
| **GR** | 1.2e-4 | 80 | 12 | mse | Fast convergence |
| **RLLD** | 5e-5 | 150 | 6 | mae | Log-scale challenges |
| **AC** | 1e-4 | 100 | 8 | mse | Well-tested baseline |

### Quick Config Generator

```python
def get_quick_config(target_curve):
    """Get optimal config for any curve type"""
    configs = {
        'DEN': {'lr': 1e-4, 'epochs': 100, 'batch': 8, 'loss': 'mse'},
        'CNL': {'lr': 8e-5, 'epochs': 120, 'batch': 8, 'loss': 'huber'},
        'GR': {'lr': 1.2e-4, 'epochs': 80, 'batch': 12, 'loss': 'mse'},
        'RLLD': {'lr': 5e-5, 'epochs': 150, 'batch': 6, 'loss': 'mae'},
        'AC': {'lr': 1e-4, 'epochs': 100, 'batch': 8, 'loss': 'mse'}
    }
    
    base_config = get_training_template(f'{target_curve.lower()}_training')
    quick = configs[target_curve]
    
    base_config.update({
        'learning_rate': quick['lr'],
        'max_epochs': quick['epochs'],
        'batch_size': quick['batch'],
        'loss_type': quick['loss']
    })
    
    return base_config
```

---

## 🔧 Common Modifications

### 1. Fast Training (Testing/Development)

```python
# Quick training for testing
config = get_training_template('fast_training')
config.update({
    'max_epochs': 10,
    'batch_size': 16,
    'early_stopping_patience': 3,
    'validation_frequency': 1
})
```

### 2. High-Quality Training (Production)

```python
# Production-quality training
config = get_training_template('density_training')  # or your curve
config.update({
    'max_epochs': 200,
    'early_stopping_patience': 30,
    'learning_rate': 5e-5,  # Lower for stability
    'gradient_clipping': 1.0,
    'save_frequency': 5
})
```

### 3. Robust Training (Noisy Data)

```python
# For noisy or poor-quality data
dataset = GeneralWellLogDataset(
    data_dict, input_curves, target_curve, normalizer,
    missing_data_strategy='interpolation',
    quality_threshold=0.7,  # Accept 70%+ valid data
    transform=True
)

config = get_training_template('robust_training')
config.update({
    'loss_type': 'huber',
    'physics_constraints': True,
    'gradient_clipping': 0.5
})
```

---

## 🚨 Troubleshooting Quick Fixes

### Problem: Training Loss Not Decreasing

```python
# Quick fixes to try:
config['learning_rate'] *= 2  # Double learning rate
# OR
config['learning_rate'] /= 2  # Halve learning rate
# OR
config['batch_size'] = 4      # Smaller batches
```

### Problem: Overfitting (Val Loss Increasing)

```python
# Quick overfitting fixes:
config['early_stopping_patience'] = 10  # Stop earlier
model_config = get_model_template('vp_prediction')
model_config['model_config']['dropout'] = 0.2  # More dropout
```

### Problem: Unrealistic Predictions

```python
# Force physics constraints:
config['physics_constraints'] = True
config['constraint_weight'] = 2.0  # Stronger constraints

# Check data ranges:
for curve, data in data_dict.items():
    print(f"{curve}: {data.min():.2f} to {data.max():.2f}")
```

### Problem: Slow Training

```python
# Speed up training:
config['batch_size'] = 16     # Larger batches
config['dataloader_workers'] = 4  # Parallel loading
model_config['model_config']['encoder_layers'] = 4  # Smaller model
```

---

## 📈 Performance Expectations

### Typical Performance Metrics

| Curve | Good RMSE | Excellent RMSE | Good R² | Excellent R² |
|-------|-----------|----------------|---------|--------------|
| **DEN** | < 0.15 g/cm³ | < 0.08 g/cm³ | > 0.7 | > 0.85 |
| **CNL** | < 8% | < 5% | > 0.6 | > 0.8 |
| **GR** | < 15 API | < 8 API | > 0.7 | > 0.85 |
| **RLLD** | < 50 ohm-m | < 20 ohm-m | > 0.5 | > 0.75 |
| **AC** | < 15 μs/ft | < 8 μs/ft | > 0.8 | > 0.9 |

### Training Time Estimates

| Dataset Size | Model Size | Training Time | Notes |
|--------------|------------|---------------|-------|
| 100 samples | Base | 5-10 min | Quick testing |
| 500 samples | Base | 15-30 min | Development |
| 1000+ samples | Base | 30-60 min | Production |
| 1000+ samples | Large | 1-2 hours | High accuracy |

---

## 🎯 One-Liner Solutions

### Complete Training Pipeline

```python
# One-liner for any curve type
def train_curve(data_dict, target_curve, input_curves=None):
    if input_curves is None:
        all_curves = list(data_dict.keys())
        input_curves = [c for c in all_curves if c != target_curve]
    
    model = GeneralWellLogTransformer.from_template('vp_prediction')
    normalizer = GeneralDataNormalizer(input_curves, [target_curve])
    dataset = GeneralWellLogDataset(data_dict, input_curves, target_curve, normalizer)
    config = get_quick_config(target_curve)
    trainer = GeneralTrainingManager(model, dataset, config)
    return trainer.train()

# Usage:
results = train_curve(your_data, 'DEN')  # Train density prediction
```

### Quick Evaluation

```python
# Quick model evaluation
def quick_eval(model, test_data, target_curve):
    predictions, targets = [], []
    for input_data, target in test_data:
        pred = model(input_data.unsqueeze(0)).squeeze()
        predictions.append(pred.detach().numpy())
        targets.append(target.numpy())
    
    pred_array, target_array = np.array(predictions), np.array(targets)
    rmse = np.sqrt(np.mean((pred_array - target_array) ** 2))
    r2 = 1 - np.sum((target_array - pred_array) ** 2) / np.sum((target_array - np.mean(target_array)) ** 2)
    
    print(f"{target_curve} - RMSE: {rmse:.4f}, R²: {r2:.4f}")
    return {'rmse': rmse, 'r2': r2}
```

---

## 📋 Checklist for New Users

### Before Training
- [ ] Data loaded and shapes verified: `(n_samples, sequence_length)`
- [ ] All required curves present in data_dict
- [ ] Data ranges within physics limits (check CURVE_CONFIGURATIONS)
- [ ] No excessive NaN values (< 10% missing)

### During Training
- [ ] Training loss decreasing steadily
- [ ] Validation loss following training loss
- [ ] No gradient explosion (loss going to inf/nan)
- [ ] Reasonable training time (see estimates above)

### After Training
- [ ] Final metrics meet expectations (see performance table)
- [ ] Predictions within physics ranges
- [ ] Model saved successfully
- [ ] Test on held-out data

### Production Deployment
- [ ] Model exported (TorchScript/ONNX)
- [ ] Normalizer saved with model
- [ ] Inference pipeline tested
- [ ] Performance benchmarked

---

## 🔗 Quick Links

- **Full Documentation**: `docs/Advanced_Training_Guide.md`
- **Production Examples**: `examples/production_training_examples.py`
- **Integration Tests**: `test_phase2_implementation.py`
- **API Reference**: `vp_predictor/__init__.py`

---

*This quick reference covers 90% of common training scenarios. For advanced use cases, see the full Advanced Training Guide.*
