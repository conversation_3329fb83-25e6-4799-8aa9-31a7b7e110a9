# Core Codebase Documentation - VP Prediction System

## 🎯 Overview

This document provides a comprehensive overview of the **core codebase** required to run the VP (Acoustic Velocity) prediction system in the `initial/` directory. This includes all essential files, dependencies, and architectural components for the **Phase 2 Enhanced General Well Log Transformer**.

## 📁 Core File Structure

### Essential Python Files (Required)

```
initial/
├── 🔥 CORE PIPELINE FILES
│   ├── train_vp_improved.py          # Main pipeline executor (Phase 2)
│   ├── vp_model_improved.py          # Enhanced VpTransformer architecture with fixed sigmoid
│   ├── model.py                      # Base transformer components (ResNet, Attention)
│   ├── utils.py                      # Device management and training utilities
│   ├── las_processor.py              # Well log data preprocessing
│   └── dataset.py                    # Dataset utilities and data loading
│
├── 🧪 TESTING & VALIDATION
│   └── test_vp_pipeline.py           # Comprehensive test suite
│
├── 📊 DATA FILES (Required)
│   ├── A1.hdf5                       # Training data file 1 (not in repo)
│   ├── A2.hdf5                       # Training data file 2 (not in repo)
│   ├── A1_converted.las              # Sample LAS file
│   └── test_well.las                 # Test well data
│
└── 📚 DOCUMENTATION
    ├── README.md                     # Main entry point
    ├── DIRECTORY_SUMMARY.md          # Directory overview
    ├── SETUP_CHECKLIST.md            # Setup verification
    ├── VP_QUICK_START_GUIDE.md       # Step-by-step guide
    └── docs/                         # Detailed documentation
```

## 🔧 Core Python Files Details

### 1. **train_vp_improved.py** (Main Pipeline)
**Purpose**: Enhanced Phase 2 pipeline executor with complete workflow
**Key Features**:
- Three-stage pipeline (Training → Validation → Prediction)
- Intelligent device detection and GPU/CPU optimization
- Enhanced VpDecoder architecture integration
- Command-line interface with flexible configuration
- Comprehensive logging and result generation

**Dependencies**: All other core files
**Size**: ~2,000+ lines (Phase 2 enhanced)
**Entry Points**: Command-line execution, stage-specific runs

### 2. **vp_model_improved.py** (Enhanced Architecture)
**Purpose**: Phase 2 improved VpTransformer with fixed sigmoid constraints
**Key Components**:
- `VpTransformer`: Main model class with enhanced VpDecoder
- `VpDecoder`: Improved decoder without artificial bounds
- `VpDataNormalizer`: Enhanced normalization with log-transform
- Model variants: Small, Base, Large configurations

**Key Improvements**:
- Eliminated sigmoid constraints (no ~40 μs/ft floor)
- Proper scaling to full [40-400] μs/ft range
- Enhanced activation functions and gradient flow
- Curve-specific normalization parameters

### 3. **model.py** (Base Components)
**Purpose**: Core transformer building blocks
**Key Classes**:
- `Input_Embedding`: ResNet-based feature extraction
- `TransformerBlock`: Multi-head attention encoders
- `SelfAttention`: Attention mechanism implementation
- `FeedForward`: MLP layers with GELU activation
- `Decoder`: Original decoder (for compatibility)

### 4. **utils.py** (Utilities)
**Purpose**: Device management and training utilities
**Key Functions**:
- `get_device()`: Intelligent GPU/CPU detection
- `load_checkpoint()`: Model loading with device mapping
- Training utilities and helper functions
- Performance monitoring and logging

### 5. **las_processor.py** (Data Processing)
**Purpose**: Well log data preprocessing and normalization
**Key Features**:
- HDF5 to LAS format processing
- Well log curve extraction (GR, CNL, DEN, RLLD)
- Data validation and quality checks
- Sequence preparation for transformer input

### 6. **dataset.py** (Dataset Management)
**Purpose**: Dataset utilities and data loading
**Key Features**:
- PyTorch Dataset classes
- Data augmentation and preprocessing
- Batch preparation and sequence handling
- Train/validation/test split management

## 🧪 Testing and Validation

### **test_vp_pipeline.py**
**Purpose**: Comprehensive test suite for Phase 2 architecture
**Test Coverage**:
- Architecture validation (Phase 2 components)
- Data processing pipeline tests
- Device detection and GPU/CPU functionality
- Model loading and inference tests
- Output validation and format checks

## 📊 Required Data Files

### Training Data (Not in Repository)
- **A1.hdf5**: Primary training dataset with well log curves
- **A2.hdf5**: Additional training data for enhanced model performance

### Sample Data (Included)
- **A1_converted.las**: Sample LAS file for testing
- **test_well.las**: Test well data for pipeline validation

### Data Format Requirements
```
Input Curves (640-point sequences):
├── GR (Gamma Ray): 0-200 API units
├── CNL (Neutron): 0-60 % porosity
├── DEN (Density): 1.5-3.0 g/cm³
└── RLLD (Resistivity): 0.1-1000 ohm-m (log-transformed)

Output:
└── Vp (Velocity): 40-400 μs/ft (full range, no artificial bounds)
```

## 🗂️ Output Structure

### **vp_prediction_outputs/** (Auto-generated)
```
vp_prediction_outputs/
├── training/
│   ├── best_vp_model.pth          # Trained Phase 2 model
│   ├── training_history.json      # Training metrics
│   ├── training_progress.png      # Enhanced training plots
│   ├── training_results.json      # Detailed results
│   └── training.log               # Training logs
│
├── validation/
│   ├── cv_results.json            # Cross-validation results
│   ├── cv_results.png             # Validation plots
│   ├── validation_report.json     # Performance assessment
│   └── validation.log             # Validation logs
│
├── prediction/
│   ├── detailed_predictions.json  # Complete prediction data
│   ├── prediction_report.json     # Analysis and metrics
│   ├── prediction_results.png     # Enhanced visualizations
│   └── prediction.log             # Prediction logs
│
├── pipeline_config.json           # Pipeline configuration
├── pipeline_summary.json          # Complete run summary
└── pipeline.log                   # Master log file
```

## 📦 Python Dependencies

### Core Dependencies (Required)
```python
# Deep Learning Framework
torch>=1.8.0              # PyTorch for neural networks
torchvision>=0.9.0         # Vision utilities
torchaudio>=0.8.0          # Audio processing utilities

# Numerical Computing
numpy>=1.19.0              # Numerical arrays and operations
scikit-learn>=0.24.0       # Machine learning utilities

# Data Handling
h5py>=3.1.0                # HDF5 file reading/writing
```

### Enhanced Features (Recommended)
```python
# Visualization and Analysis
matplotlib>=3.3.0          # Plotting and charts
seaborn>=0.11.0           # Statistical visualizations
scipy>=1.6.0              # Scientific computing

# Progress and Utilities
tqdm>=4.60.0              # Progress bars
pandas>=1.2.0             # Data analysis (optional)

# Well Log Processing (Optional)
lasio>=0.30               # LAS file reading
```

### Development and Profiling (Optional)
```python
# Model Analysis
thop>=0.0.31              # FLOPs and parameter counting

# Testing and Quality
pytest>=6.0               # Testing framework
black>=21.0               # Code formatting
```

## 💻 System Requirements

### Minimum Requirements
```
Operating System: Windows, Linux, macOS
Python: 3.7+ (3.8+ recommended)
RAM: 8GB+ system memory
Storage: 2GB+ free space
CPU: Multi-core processor (Intel/AMD)
```

### Recommended (Enhanced Performance)
```
GPU: NVIDIA GPU with 4GB+ VRAM
CUDA: 11.1+ for optimal PyTorch support
RAM: 16GB+ system memory
Storage: SSD with 5GB+ free space
Network: Internet for dependency installation
```

### GPU Acceleration Setup
```bash
# Check CUDA availability
nvidia-smi

# Install GPU-enabled PyTorch (adjust CUDA version)
pip install torch torchvision torchaudio --extra-index-url https://download.pytorch.org/whl/cu118

# Verify GPU detection
python -c "import torch; print('CUDA available:', torch.cuda.is_available())"
```

## 🏗️ Architecture Overview

### Model Architecture Flow
```
Input Processing:
[B, 4, 640] → VpDataNormalizer → Normalized curves

Feature Extraction:
Normalized → Input_Embedding (ResNet) → [B, 640, 64]

Sequence Modeling:
[B, 640, 64] → TransformerBlock(s) → [B, 640, 64]

VP Prediction:
[B, 640, 64] → VpDecoder → [B, 1, 640] → Denormalized Vp

Where:
- B = Batch size
- 4 = Input curves (GR, CNL, DEN, RLLD)
- 640 = Sequence length
- 64 = Feature dimension
```

### Model Variants
| Variant | ResNet Blocks | Transformer Layers | Attention Heads | Parameters |
|---------|---------------|-------------------|-----------------|------------|
| Small   | 2             | 2                 | 2               | ~1.2M      |
| Base    | 4             | 4                 | 4               | ~2.1M      |
| Large   | 6             | 6                 | 8               | ~8.4M      |

## 🚀 Quick Start Commands

### Essential Installation
```bash
# Navigate to directory
cd initial/

# Install core dependencies
pip install torch numpy scikit-learn h5py matplotlib scipy tqdm

# Verify installation
python -c "import torch, numpy, sklearn, h5py; print('✅ Core dependencies ready')"

# Test Phase 2 architecture
python -c "from vp_model_improved import VpTransformer; print('✅ Phase 2 architecture loaded')"
```

### Basic Usage
```bash
# Complete pipeline (recommended)
python train_vp_improved.py

# Individual stages
python train_vp_improved.py --stage training
python train_vp_improved.py --stage validation --model-path vp_prediction_outputs/training/best_vp_model.pth
python train_vp_improved.py --stage prediction --model-path vp_prediction_outputs/training/best_vp_model.pth

# Test pipeline
python test_vp_pipeline.py
```

## 🔍 File Dependencies Map

### Dependency Relationships
```
train_vp_improved.py (MAIN)
├── vp_model_improved.py (Phase 2 architecture)
│   ├── model.py (base components)
│   └── utils.py (device management)
├── las_processor.py (data processing)
├── dataset.py (data loading)
└── Configuration files (optional)

test_vp_pipeline.py (TESTING)
├── All above core files
└── Sample data files

Data Pipeline:
A1.hdf5, A2.hdf5 → las_processor.py → dataset.py → train_vp_improved.py
```

### Import Dependencies
```python
# Core imports in train_vp_improved.py
from vp_model_improved import VpTransformer, VpDataNormalizer
from model import Input_Embedding, TransformerBlock
from utils import get_device, load_checkpoint
from las_processor import LASProcessor
from dataset import VpDataset

# External dependencies
import torch, numpy, sklearn, h5py, matplotlib
```

## 📋 Verification Checklist

### Phase 2 Architecture Verification
```bash
# 1. Verify core files present
ls train_vp_improved.py vp_model_improved.py model.py utils.py las_processor.py dataset.py

# 2. Test Phase 2 components
python -c "from vp_model_improved import VpDecoder; print('✅ Enhanced VpDecoder ready')"

# 3. Check device intelligence
python -c "from utils import get_device; device, info = get_device(); print(f'Device: {device}, Info: {info}')"

# 4. Validate data processing
python -c "from las_processor import LASProcessor; print('✅ Data processor ready')"

# 5. Run comprehensive tests
python test_vp_pipeline.py
```

### Success Indicators
- ✅ All core files present and importable
- ✅ Phase 2 VpDecoder architecture loads without errors
- ✅ Device detection working (GPU/CPU auto-selection)
- ✅ Data files accessible (A1.hdf5, A2.hdf5)
- ✅ Test suite passes all checks
- ✅ Pipeline can be executed without import errors

## 🎯 Performance Expectations

### Phase 2 Enhanced Performance
- **Training R²**: 0.80-0.95 (improved with fixed constraints)
- **Validation R²**: 0.75-0.90 (better generalization)
- **Test R²**: 0.70-0.85 (superior accuracy)
- **RMSE**: <15 μs/ft (enhanced precision)
- **Range Coverage**: Full [40-400] μs/ft without artificial bounds
- **Training Time**: 10-25 minutes (optimized convergence)

### Hardware Performance
- **GPU Acceleration**: 4-5x faster than CPU
- **Memory Usage**: 4-8GB RAM during training
- **Disk Usage**: ~2GB for complete outputs
- **GPU Memory**: 2-4GB VRAM recommended

---

## 🚀 Ready to Run?

With this core codebase documentation, you have everything needed to:

1. **Understand the architecture** - Phase 2 enhanced VpTransformer system
2. **Set up dependencies** - Complete installation guide
3. **Verify installation** - Comprehensive testing procedures
4. **Execute pipeline** - Step-by-step command reference
5. **Troubleshoot issues** - Dependency and architecture validation

**🎯 Current Status**: Phase 2 Complete - Enhanced VpDecoder with fixed sigmoid constraints, superior accuracy, and full range coverage ready for immediate use!