"""
Configuration management for General Well Log Transformer

This module provides comprehensive configuration management for flexible 
single-target and multi-curve well log predictions, replacing hardcoded 
Vp-specific parameters.
"""

from .curves import CURVE_CONFIGURATIONS, get_curve_config, ACTIVATION_FUNCTIONS
from .models import MODEL_TEMPLATES, get_model_template
from .validation import (
    validate_curve_config, 
    validate_model_config, 
    validate_full_config,
    ConfigurationError
)

# Phase 2 additions - Training configurations
from .training import (
    TRAINING_TEMPLATES, DATA_TEMPLATES, INTEGRATED_TEMPLATES,
    CURVE_TRAINING_HINTS,
    get_training_template, get_data_template, get_integrated_template,
    create_custom_training_config, get_curve_training_hints,
    validate_training_config, list_available_templates,
    print_template_info, get_example_configs
)

__all__ = [
    # Phase 1 - Core configurations
    'CURVE_CONFIGURATIONS',
    'ACTIVATION_FUNCTIONS',
    'MODEL_TEMPLATES', 
    'get_curve_config',
    'get_model_template',
    'validate_curve_config',
    'validate_model_config',
    'validate_full_config',
    'ConfigurationError',
    
    # Phase 2 - Training configurations
    # Training templates
    'TRAINING_TEMPLATES', 'DATA_TEMPLATES', 'INTEGRATED_TEMPLATES',
    'CURVE_TRAINING_HINTS',
    
    # Template utilities
    'get_training_template', 'get_data_template', 'get_integrated_template',
    'create_custom_training_config', 'get_curve_training_hints',
    'validate_training_config', 'list_available_templates',
    'print_template_info', 'get_example_configs'
]