"""
Simple Well Log Prediction Test
Demonstrates basic usage of the current VpTransformer with A1.hdf5 and A2.hdf5 data

This script provides a practical example of:
1. Loading HDF5 well log data
2. Preparing data for VpTransformer
3. Creating and using the model
4. Visualizing results
"""
import os
import sys
import numpy as np
import h5py
import matplotlib.pyplot as plt

# Add the vp_predictor package to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

try:
    from vp_predictor import VpDataNormalizer, MWLT_Vp_Base
    from vp_predictor.utils import get_device
    import torch
except ImportError as e:
    print(f"Error importing vp_predictor: {e}")
    print("Please ensure the vp_predictor package is properly installed")
    sys.exit(1)

def load_hdf5_data(file_path):
    """Load well log data from HDF5 file"""
    print(f"📂 Loading data from: {os.path.basename(file_path)}")
    
    curves = {}
    with h5py.File(file_path, 'r') as f:
        print(f"   Available curves: {list(f.keys())}")
        
        for curve_name in f.keys():
            data = f[curve_name][:]
            if data.ndim > 1:
                data = data.squeeze()
            curves[curve_name] = data
            print(f"   {curve_name}: shape {data.shape}, range [{data.min():.2f}, {data.max():.2f}]")
    
    return curves

def resample_to_length(data, target_length=640):
    """Resample curve data to target length"""
    if len(data) == target_length:
        return data
    
    original_indices = np.linspace(0, len(data) - 1, len(data))
    target_indices = np.linspace(0, len(data) - 1, target_length)
    resampled = np.interp(target_indices, original_indices, data)
    
    return resampled

def prepare_vp_data(curves, sequence_length=640):
    """Prepare data for Vp prediction"""
    # Input curves for VpTransformer
    input_curves = ['GR', 'CNL', 'DEN', 'RLLD']
    target_curve = 'AC'  # Acoustic curve (Vp)
    
    print(f"\n🔄 Preparing data for Vp prediction...")
    print(f"   Input curves: {input_curves}")
    print(f"   Target curve: {target_curve}")
    print(f"   Sequence length: {sequence_length}")
    
    # Prepare input features
    input_features = []
    for curve_name in input_curves:
        if curve_name in curves:
            data = resample_to_length(curves[curve_name], sequence_length)
            input_features.append(data)
            print(f"   ✅ {curve_name}: resampled to {len(data)} points")
        else:
            print(f"   ⚠️  Missing {curve_name}, using zeros")
            input_features.append(np.zeros(sequence_length))
    
    # Prepare target
    if target_curve in curves:
        target_data = resample_to_length(curves[target_curve], sequence_length)
        print(f"   ✅ {target_curve}: resampled to {len(target_data)} points")
    else:
        print(f"   ⚠️  Missing {target_curve}, using zeros")
        target_data = np.zeros(sequence_length)
    
    input_array = np.array(input_features)  # Shape: (4, 720)
    
    print(f"   📊 Final shapes - Input: {input_array.shape}, Target: {target_data.shape}")
    
    return input_array, target_data

def create_model_and_predict(input_data, device):
    """Create VpTransformer model and make prediction"""
    print(f"\n🏗️  Creating VpTransformer model...")
    
    # Create model
    model = MWLT_Vp_Base(in_channels=4, out_channels=1, feature_num=64)
    model = model.to(device)
    
    # Model info
    param_count = sum(p.numel() for p in model.parameters())
    print(f"   Model parameters: {param_count:,}")
    print(f"   Device: {device}")
    
    # Make prediction (with random weights - for demonstration)
    print(f"\n🔮 Making prediction...")
    model.eval()
    with torch.no_grad():
        # Convert to tensor and add batch dimension
        input_tensor = torch.FloatTensor(input_data).unsqueeze(0).to(device)
        print(f"   Input tensor shape: {input_tensor.shape}")
        
        # Forward pass
        prediction = model(input_tensor)
        
        # Convert back to numpy
        prediction_np = prediction.squeeze().cpu().numpy()
        print(f"   Prediction shape: {prediction_np.shape}")
        print(f"   Prediction range: [{prediction_np.min():.1f}, {prediction_np.max():.1f}]")
    
    return model, prediction_np

def visualize_results(curves, input_data, target_data, prediction, well_name):
    """Create visualization of input curves, target, and prediction"""
    print(f"\n📊 Creating visualization for {well_name}...")
    
    # Create depth array
    depth = curves.get('DEPTH', np.arange(len(target_data)))
    if len(depth) != len(target_data):
        depth = np.linspace(depth.min(), depth.max(), len(target_data))
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 3, figsize=(15, 10))
    fig.suptitle(f'Well Log Prediction Results - {well_name}', fontsize=16)
    
    # Input curves
    curve_names = ['GR', 'CNL', 'DEN', 'RLLD']
    for i, (ax, curve_name) in enumerate(zip(axes[0], curve_names)):
        ax.plot(input_data[i], depth, 'b-', linewidth=1)
        ax.set_title(f'{curve_name}')
        ax.set_ylabel('Depth')
        ax.grid(True, alpha=0.3)
        ax.invert_yaxis()
    
    # Target vs Prediction
    axes[1, 0].plot(target_data, depth, 'g-', linewidth=2, label='Actual AC')
    axes[1, 0].plot(prediction, depth, 'r--', linewidth=2, label='Predicted AC')
    axes[1, 0].set_title('AC: Actual vs Predicted')
    axes[1, 0].set_ylabel('Depth')
    axes[1, 0].legend()
    axes[1, 0].grid(True, alpha=0.3)
    axes[1, 0].invert_yaxis()
    
    # Scatter plot
    axes[1, 1].scatter(target_data, prediction, alpha=0.6, s=1)
    axes[1, 1].plot([target_data.min(), target_data.max()], 
                    [target_data.min(), target_data.max()], 'r--', label='Perfect fit')
    axes[1, 1].set_xlabel('Actual AC')
    axes[1, 1].set_ylabel('Predicted AC')
    axes[1, 1].set_title('Actual vs Predicted Scatter')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    # Error plot
    error = prediction - target_data
    axes[1, 2].plot(error, depth, 'purple', linewidth=1)
    axes[1, 2].axvline(x=0, color='black', linestyle='-', alpha=0.5)
    axes[1, 2].set_title('Prediction Error')
    axes[1, 2].set_xlabel('Error (Predicted - Actual)')
    axes[1, 2].set_ylabel('Depth')
    axes[1, 2].grid(True, alpha=0.3)
    axes[1, 2].invert_yaxis()
    
    plt.tight_layout()
    
    # Save plot
    output_file = f'prediction_results_{well_name.lower()}.png'
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"   📈 Plot saved as: {output_file}")
    
    # Show statistics
    rmse = np.sqrt(np.mean(error**2))
    mae = np.mean(np.abs(error))
    r2 = 1 - np.sum(error**2) / np.sum((target_data - target_data.mean())**2)
    
    print(f"   📊 Statistics:")
    print(f"      RMSE: {rmse:.2f}")
    print(f"      MAE:  {mae:.2f}")
    print(f"      R²:   {r2:.3f}")
    
    return fig

def main():
    """Main function to run the prediction test"""
    print("🚀 SIMPLE WELL LOG PREDICTION TEST")
    print("="*60)
    print("Testing VpTransformer with A1.hdf5 and A2.hdf5 data")
    
    # Setup
    device = get_device(device_id=0)  # Use GPU if available, else CPU
    print(f"🖥️  Using device: {device}")
    
    # Test files (adjust paths since we're running from examples directory)
    test_files = [
        ("A1.hdf5", "A1"),
        ("A2.hdf5", "A2")
    ]
    
    results = {}
    
    for file_path, well_name in test_files:
        print(f"\n" + "="*60)
        print(f"PROCESSING WELL {well_name}")
        print("="*60)
        
        try:
            # Load data
            curves = load_hdf5_data(file_path)
            
            # Prepare data
            input_data, target_data = prepare_vp_data(curves)
            
            # Create model and predict
            model, prediction = create_model_and_predict(input_data, device)
            
            # Visualize results
            fig = visualize_results(curves, input_data, target_data, prediction, well_name)
            
            # Store results
            results[well_name] = {
                'input_data': input_data,
                'target_data': target_data,
                'prediction': prediction,
                'curves': curves
            }
            
            print(f"✅ {well_name} processing completed successfully!")
            
        except Exception as e:
            print(f"❌ Error processing {well_name}: {e}")
            results[well_name] = None
    
    # Summary
    print(f"\n" + "="*60)
    print("🎉 TEST SUMMARY")
    print("="*60)
    
    success_count = sum(1 for result in results.values() if result is not None)
    print(f"✅ Successfully processed: {success_count}/{len(test_files)} wells")
    
    for well_name, result in results.items():
        if result is not None:
            print(f"✅ {well_name}: Prediction completed")
        else:
            print(f"❌ {well_name}: Failed")
    
    print(f"\n📋 IMPORTANT NOTES:")
    print(f"⚠️  This demonstration uses UNTRAINED model weights!")
    print(f"   For actual predictions, you need to:")
    print(f"   1. Train the VpTransformer model using training data")
    print(f"   2. Save the trained model checkpoint (.pth file)")
    print(f"   3. Load the checkpoint for inference")
    
    print(f"\n📚 NEXT STEPS:")
    print(f"1. Train VpTransformer: python -c 'from vp_predictor import create_improved_vp_data; create_improved_vp_data()'")
    print(f"2. Check training results in the generated model files")
    print(f"3. Use trained model for actual predictions")
    print(f"4. Implement GeneralTransformer for multi-curve prediction")
    
    return results

if __name__ == "__main__":
    main()
