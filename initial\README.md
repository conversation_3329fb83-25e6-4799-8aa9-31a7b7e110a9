# VP (Acoustic Velocity) Prediction Pipeline

## 🚀 Quick Start

**Want to train and predict <PERSON> right away?** → See **[VP_QUICK_START_GUIDE.md](VP_QUICK_START_GUIDE.md)**

## 📁 Directory Structure

```
initial/
├── 🚀 MAIN FILES
│   ├── train_vp_improved.py          # Main VP prediction pipeline
│   ├── VP_QUICK_START_GUIDE.md       # Step-by-step guide (START HERE)
│   └── README.md                     # This file
│
├── 🔧 CORE COMPONENTS
│   ├── vp_model_improved.py          # VP model architectures
│   ├── utils.py                      # Utility functions
│   ├── las_processor.py              # Data processing
│   ├── model.py                      # Base model components
│   └── dataset.py                    # Dataset utilities
│
├── 🧪 TESTING
│   └── test_vp_pipeline.py           # Pipeline test suite
│
├── 📄 DATA FILES
│   ├── A1_converted.las              # Sample data file 1
│   └── test_well.las                 # Sample data file 2
│
├── 📊 RESULTS (auto-created)
│   └── vp_prediction_outputs/        # Training, validation, prediction results
│
├── 📚 DOCUMENTATION
│   └── docs/                         # Detailed documentation
│
└── 📦 ARCHIVED FILES
    └── old_files/                    # Old scripts (archived)
```

## ⚡ One-Command Usage

```bash
# Complete VP training and prediction pipeline
python train_vp_improved.py
```

This single command will:
1. ✅ Train a VP prediction model
2. ✅ Validate the model with cross-validation  
3. ✅ Make predictions and generate visualizations
4. ✅ Save all results to `vp_prediction_outputs/`

## 📋 Prerequisites

1. **Install Dependencies:**
   ```bash
   pip install torch numpy matplotlib scikit-learn scipy h5py tqdm
   ```

2. **Ensure Data Files Available:**
   - `A1.hdf5` and `A2.hdf5` (pipeline will auto-detect location)

## 🎯 Expected Results

After running the pipeline, you'll get:

- **Trained Model**: `vp_prediction_outputs/training/best_vp_model.pth`
- **Performance Metrics**: R² scores, RMSE, MAE
- **Visualizations**: Training progress, validation results, prediction plots
- **Reports**: JSON files with detailed analysis

## 📈 Typical Performance

- **Training R²**: 0.75 - 0.90
- **Validation R²**: 0.70 - 0.85
- **Test R²**: 0.65 - 0.80
- **Training Time**: 10-30 minutes

## 🔧 Customization

### Individual Stages
```bash
# Train only
python train_vp_improved.py --stage training

# Validate only
python train_vp_improved.py --stage validation --model-path vp_prediction_outputs/training/best_vp_model.pth

# Predict only  
python train_vp_improved.py --stage prediction --model-path vp_prediction_outputs/training/best_vp_model.pth
```

### Custom Configuration
```bash
# Use custom settings
python train_vp_improved.py --config my_config.json --output-dir my_results
```

## 🆘 Need Help?

1. **Quick Start**: Read `VP_QUICK_START_GUIDE.md` for step-by-step instructions
2. **Test Setup**: Run `python test_vp_pipeline.py` to verify installation
3. **Detailed Docs**: Check `docs/` folder for comprehensive documentation
4. **Command Help**: Run `python train_vp_improved.py --help`

## 🏗️ Architecture

This pipeline implements a comprehensive three-stage VP prediction workflow:

- **Stage 1 - Training**: Model training with checkpointing and monitoring
- **Stage 2 - Validation**: Cross-validation and performance assessment  
- **Stage 3 - Prediction**: Model inference and result visualization

Built with professional software engineering practices:
- ✅ Modular, object-oriented design
- ✅ Comprehensive error handling and logging
- ✅ Configurable parameters and settings
- ✅ Organized output structure
- ✅ Extensive testing and validation

---

**🚀 Ready to start?** → Open **[VP_QUICK_START_GUIDE.md](VP_QUICK_START_GUIDE.md)** and follow the step-by-step instructions!
